/**
 * Theme Loader Utility
 * Dynamically loads CSS variables based on the app configuration
 */

import appConfig from '@/src/config/apps';

/**
 * Loads theme CSS variables into the :root element
 * This ensures that the theme is applied consistently across the application
 */
export const loadThemeVariables = () => {
  if (typeof document === 'undefined') {
    return; // Skip on server-side
  }

  const theme = appConfig.theme;
  
  // Set CSS variables on the :root element
  document.documentElement.style.setProperty('--primary-color', theme.primary);
  document.documentElement.style.setProperty('--primary-light-color', theme.primaryLight);
  document.documentElement.style.setProperty('--primary-hover-color', theme.primaryHover);
  document.documentElement.style.setProperty('--secondary-color', theme.secondary);
  document.documentElement.style.setProperty('--accent-color', theme.accent);
  document.documentElement.style.setProperty('--border-color', theme.border);
  document.documentElement.style.setProperty('--background-color', theme.background);
  document.documentElement.style.setProperty('--text-color', theme.text);
  document.documentElement.style.setProperty('--text-light-color', theme.textLight);
  document.documentElement.style.setProperty('--text-primary-color', theme.textPrimary);
  document.documentElement.style.setProperty('--text-secondary-color', theme.textSecondary);
};

export default loadThemeVariables;
