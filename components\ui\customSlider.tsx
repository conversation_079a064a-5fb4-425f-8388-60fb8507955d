import React, { useState, useRef, useEffect } from 'react';

interface CustomSliderProps {
  min?: number;
  max?: number;
  value: number;
  onChange: (value: number) => void;
  vertical?: boolean;
  className?: string;
}

const CustomSlider: React.FC<CustomSliderProps> = ({
  min = 0,
  max = 100,
  value,
  onChange,
  vertical = false,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const percentage = ((value - min) / (max - min)) * 100;

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    updateValue(e);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      updateValue(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateValue = (e: MouseEvent | React.MouseEvent) => {
    if (!sliderRef.current) return;

    const rect = sliderRef.current.getBoundingClientRect();
    let newPercentage: number;

    if (vertical) {
      const clickY = e.clientY - rect.top;
      newPercentage = ((rect.height - clickY) / rect.height) * 100;
    } else {
      const clickX = e.clientX - rect.left;
      newPercentage = (clickX / rect.width) * 100;
    }

    newPercentage = Math.max(0, Math.min(100, newPercentage));
    const newValue = min + (newPercentage / 100) * (max - min);
    onChange(Math.round(newValue));
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div
      ref={sliderRef}
      className={`relative cursor-pointer ${
        vertical ? 'w-2 h-full flex items-center justify-center' : 'h-2 w-full flex items-center'
      } ${className}`}
      onMouseDown={handleMouseDown}
    >
      {/* Track */}
      <div
        className={`absolute bg-gray-300 rounded-full ${
          vertical ? 'w-1 h-full' : 'w-full h-1'
        }`}
      />

      {/* Progress */}
      <div
        className={`absolute bg-primary rounded-full ${
          vertical ? 'w-1 bottom-0' : 'h-1 left-0'
        }`}
        style={{
          [vertical ? 'height' : 'width']: `${percentage}%`,
        }}
      />

      {/* Handle */}
      <div
        className="absolute w-3 h-3 bg-white border-2 border-primary rounded-full hover:border-primary transition-colors"
        style={{
          [vertical ? 'bottom' : 'left']: `${percentage}%`,
          transform: vertical
            ? 'translateX(-50%) translateY(50%)'
            : 'translateY(-50%) translateX(-50%)',
          left: vertical ? '50%' : undefined,
          top: vertical ? undefined : '50%',
        }}
      />
    </div>
  );
};

export default CustomSlider;
