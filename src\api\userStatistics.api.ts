import { doc, getDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import { UserStatistics } from "../services/userStatistics.service";

/**
 * Fetch user statistics from Firebase
 * Returns null if user is not logged in or statistics don't exist
 * 
 * @returns Promise<UserStatistics | null>
 */
export const fetchUserStatistics = async (): Promise<UserStatistics | null> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            console.warn("User not logged in - cannot fetch statistics");
            return null;
        }

        const docRef = doc(db, `users/${userId}/statistics/userStatistics`);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            return docSnap.data() as UserStatistics;
        } else {
            // Return default statistics if none exist
            return {
                bestStreak: 0,
                currentStreak: 0,
                lastUpdatedTime: Date.now(),
            };
        }
    } catch (error) {
        console.error("Error fetching user statistics:", error);
        return null;
    }
};
