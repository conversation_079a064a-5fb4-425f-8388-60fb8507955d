# Media Library API Implementation

## Overview
This implementation provides a comprehensive media library service that manages lecture data retrieval from both IndexedDB (local storage) and Firebase (cloud storage). It includes intelligent data source selection, local filtering, sorting capabilities, and deep search functionality.

## Features Implemented

### 1. Hybrid Data Source Management
- **IndexedDB Priority**: Prefers local storage for performance and offline access
- **Firebase Fallback**: Automatically falls back to cloud storage when needed
- **Smart Source Selection**: Configurable data source selection based on requirements

### 2. Advanced Filtering and Sorting
- **Language Filtering**: Filter lectures by primary language
- **Category Filtering**: Filter by lecture categories (BG, CC, SB, etc.)
- **Custom Sorting**: Flexible sorting by various fields with direction control
- **Date Handling**: Special handling for complex date structures

### 3. Search Capabilities
- **Local Search**: In-memory filtering for fast results
- **Deep Search**: External API integration for comprehensive search
- **Query Parameters**: Flexible search parameter handling

### 4. Performance Optimization
- **Local Processing**: Client-side filtering and sorting for speed
- **Batch Operations**: Efficient handling of large datasets
- **Caching Strategy**: Leverages IndexedDB for data caching

## Data Structures

### Fetch Lectures Payload
```typescript
interface FetchLecturesPayload {
  limit?: number;           // Maximum number of results
  orderBy?: string;         // Field to sort by
  order?: OrderByDirection; // Sort direction (asc/desc)
  language?: string;        // Language filter
  category?: string;        // Category filter
  useIndexedDB?: boolean;   // Data source preference
  searchQuery?: string;     // Search query text
  deepSearch?: boolean;     // Enable deep search
}
```

### Search Query Parameters
```typescript
interface searchQueryParams {
  query: string;            // Search query text
  size: number;             // Results per page
  from: number;             // Pagination offset
}
```

## Implementation Details

### Core Functions

#### 1. `fetchLectures(payload)`
- **Purpose**: Primary function for retrieving lecture data
- **Features**: 
  - Intelligent data source selection
  - Local filtering and sorting
  - Fallback mechanism to Firebase
  - Performance optimization

#### Data Source Logic:
1. **IndexedDB First**: Attempts local data retrieval
2. **Local Processing**: Applies filters and sorting in-memory
3. **Firebase Fallback**: Uses cloud data if local unavailable
4. **Query Optimization**: Builds efficient Firebase queries

#### 2. `getDeepSearchResults(queryParams)`
- **Purpose**: External search API integration
- **Features**: 
  - Full-text search capabilities
  - Pagination support
  - Advanced search parameters

#### 3. `addToFavourite(lectureIds)` (Legacy)
- **Purpose**: Legacy favorite management function
- **Note**: Superseded by dedicated favorite service
- **Features**: Firestore document management

### Filtering Implementation

#### Language Filtering
```javascript
// Local filtering
if (language) {
  filteredLectures = filteredLectures.filter(lecture =>
    lecture.language && lecture.language.main === language
  );
}

// Firebase query
if (language) {
  queryConstraints.push(where("language.main", "==", language));
}
```

#### Category Filtering
```javascript
// Local filtering
if (category) {
  filteredLectures = filteredLectures.filter(lecture =>
    lecture.category && lecture.category.includes(category)
  );
}

// Firebase query
if (category) {
  queryConstraints.push(where("category", "array-contains", category));
}
```

### Sorting Implementation

#### Date Sorting (Special Handling)
```javascript
const getDateValue = (lecture) => {
  if (!lecture.dateOfRecording) return 0;
  
  if (typeof lecture.dateOfRecording === 'object' && lecture.dateOfRecording.year) {
    // Original structure: {year, month, day}
    return new Date(
      lecture.dateOfRecording.year,
      lecture.dateOfRecording.month - 1,
      lecture.dateOfRecording.day || 1
    ).getTime();
  } else if (typeof lecture.dateOfRecording === 'string') {
    // Deep search structure: "YYYY-MM-DD"
    return new Date(lecture.dateOfRecording).getTime();
  }
  return 0;
};
```

## Integration Points

The media library API integrates with:

1. **IndexedDB Service**
   - `getAllLectures()` - Local data retrieval
   - Offline functionality support

2. **Firebase Firestore**
   - Cloud data queries and retrieval
   - Real-time data synchronization

3. **Search API**
   - External search service integration
   - Deep search capabilities

4. **UI Components**
   - Lecture lists and grids
   - Search interfaces
   - Filter components

## Usage Examples

### Basic Lecture Fetching
```javascript
import { fetchLectures } from './mediaLibrary.api';

// Default fetch (5 latest lectures)
const lectures = await fetchLectures();

// Custom parameters
const customLectures = await fetchLectures({
  limit: 20,
  orderBy: 'title',
  order: 'asc',
  language: 'english',
  category: 'BG'
});
```

### Force Firebase Usage
```javascript
// Bypass IndexedDB and use Firebase directly
const firebaseLectures = await fetchLectures({
  useIndexedDB: false,
  limit: 10,
  orderBy: 'dateOfRecording',
  order: 'desc'
});
```

### Deep Search
```javascript
import { getDeepSearchResults } from './mediaLibrary.api';

const searchResults = await getDeepSearchResults({
  query: "bhagavad gita chapter 2",
  size: 20,
  from: 0
});
```

### Language and Category Filtering
```javascript
// English Bhagavad-gītā lectures
const bgLectures = await fetchLectures({
  language: 'english',
  category: 'BG',
  limit: 50,
  orderBy: 'dateOfRecording',
  order: 'desc'
});
```

## Performance Optimizations

### 1. Local Processing
- **In-memory Filtering**: Avoids database queries for filtering
- **Client-side Sorting**: Reduces server load and improves response time
- **Batch Processing**: Efficient handling of large datasets

### 2. Smart Fallbacks
- **Graceful Degradation**: Seamless fallback to Firebase when IndexedDB fails
- **Error Recovery**: Continues operation despite individual component failures
- **Data Source Optimization**: Uses most appropriate data source

### 3. Query Optimization
- **Compound Queries**: Efficient Firebase query construction
- **Index Usage**: Leverages Firebase indexes for performance
- **Limit Application**: Applies limits at query level for efficiency

## Error Handling
- **Data Source Failures**: Graceful handling of IndexedDB or Firebase failures
- **Network Issues**: Robust handling of connectivity problems
- **Data Validation**: Validates data structure and content
- **Fallback Mechanisms**: Multiple recovery strategies for different failure modes

## Benefits
1. **Offline Support**: Full functionality with IndexedDB when offline
2. **Performance**: Fast local data access with cloud fallback
3. **Flexibility**: Configurable data sources and parameters
4. **Scalability**: Handles large datasets efficiently
5. **Search Integration**: Comprehensive search capabilities
6. **Data Consistency**: Intelligent synchronization between local and cloud
7. **User Experience**: Fast response times with reliable data access
