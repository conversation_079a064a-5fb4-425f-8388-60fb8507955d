import { db } from "../config/firebase.config";
import {
    getFirestore,
    collection,
    query,
    orderBy,
    limit,
    getDocs,
    where,
    OrderByDirection,
    QueryConstraint,
} from "firebase/firestore";

export interface FetchPlaylistPayload {
    type?: string;
    limit?: number;
    orderBy?: string;
    order?: OrderByDirection;
}

export const fetchPlaylist = async (payload?: FetchPlaylistPayload): Promise<any[]> => {
    try {
        // Set default values if payload is not provided
        const {
            type: playlistType = "PublicPlaylists",
            limit: limitCount = 5,
            orderBy: orderByField = "creationTime",
            order: orderDirection = "desc"
        } = payload || {};

        const adminsSnapshot = await getDocs(collection(db, "admins"));
        const admins: any = adminsSnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));

        const adminEmails = admins.map((admin: any) => admin.email);

        // Start building the query constraints
        const queryConstraints: QueryConstraint[] = [
            // limit(limitCount)
        ];

        // Add where condition if adminEmails is not empty
        if (adminEmails.length > 0) {
            // Firestore supports 'in' operator for up to 10 values
            queryConstraints.unshift(
                where("authorEmail", "in", adminEmails.slice(0, 10))
            );
            // If adminEmails has more than 10, you need to run multiple queries and merge results
        }

        // Create the query with all constraints
        const q = query(
            collection(db, playlistType),
            ...queryConstraints
        );

        const querySnapshot = await getDocs(q);

        // Extract data from the documents
        const fetchedPlaylists = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));

        const seen = new Set()
        const playlists = fetchedPlaylists.filter((p: any) => {
            if (seen.has(p.title)) return false
            seen.add(p.title)
            return true
        })

        return playlists;
    } catch (error) {
        console.error("Error fetching playlists: ", error);
        throw new Error("Failed to fetch plalists.");
    }
};
