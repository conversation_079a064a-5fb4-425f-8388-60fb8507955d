/**
 * Theme configuration
 * Exports theme variables and utility functions for styling
 */

import appConfig from './apps';

// Export theme colors from the app config
export const theme = appConfig.theme;

// CSS variables for use in styled-components or inline styles
export const cssVariables = {
  '--primary-color': theme.primary,
  '--primary-light-color': theme.primaryLight,
  '--primary-hover-color': theme.primaryHover,
  '--secondary-color': theme.secondary,
  '--accent-color': theme.accent,
  '--border-color': theme.border,
  '--background-color': theme.background,
  '--text-color': theme.text,
  '--text-secondary-color': theme.textSecondary,
};

// Function to get a theme color by key
export const getThemeColor = (key: keyof typeof theme) => {
  return theme[key];
};

// Function to generate tailwind theme extension
export const getTailwindTheme = () => {
  return {
    colors: {
      primary: theme.primary,
      'primary-light': theme.primaryLight,
      'primary-hover': theme.primaryHover,
      secondary: theme.secondary,
      accent: theme.accent,
      border: theme.border,
      background: theme.background,
      text: theme.text,
      textLight: theme.textLight,
      'text-primary': theme.textPrimary,
      'text-secondary': theme.textSecondary,
    },
    borderRadius: {
      ...appConfig.ui.borderRadius,
    },
  };
};

export default theme;
