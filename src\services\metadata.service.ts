import { doc, getDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";

// Interface for metadata document
export interface MetadataInfo {
    message: string;
    source: string;
    status: string;
    timestamp: number;
    totallectures: number;
    lastSyncTimestamp?: number;
}

/**
 * Fetch metadata information from Firebase
 * Returns metadata with total lectures count
 *
 * @returns Promise<MetadataInfo | null>
 */
export const fetchMetadata = async (): Promise<MetadataInfo | null> => {
    try {
        // Assuming metadata is stored in a 'metadata' collection with a specific document
        // You may need to adjust the document path based on your Firebase structure
        const metadataRef = doc(db, "metadata", "lastSyncTimestamp");
        const metadataSnap = await getDoc(metadataRef);

        if (metadataSnap.exists()) {
            const data = metadataSnap.data();
            return {
                message: data.message || "",
                source: data.source || "",
                status: data.status || "",
                timestamp: data.timestamp || 0,
                totallectures: data.totallectures || 0,
                lastSyncTimestamp:
                    data.lastSyncTimestamp || data.timestamp || 0,
            } as MetadataInfo;
        } else {
            console.warn("Metadata document does not exist");
            return null;
        }
    } catch (error) {
        console.error("Error fetching metadata:", error);
        return null;
    }
};

/**
 * Get total lectures count from metadata
 * Returns 0 if metadata is not available
 *
 * @returns Promise<number>
 */
export const getTotalLecturesCount = async (): Promise<number> => {
    try {
        const metadata = await fetchMetadata();
        console.log("metadata?.totallectures", metadata?.totallectures);
        return metadata?.totallectures || 0;
    } catch (error) {
        console.error("Error getting total lectures count:", error);
        return 0;
    }
};
