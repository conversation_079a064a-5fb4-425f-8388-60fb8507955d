import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '../config/firebase.config';
import {
  storeLectures,
  updateSyncInfo,
  getSyncInfo,
  getLectureCount,
  getAllLectures,
  Lecture,
  SyncInfo
} from './indexedDB.service';

/**
 * Helper function to normalize timestamp values
 * Handles both string ISO timestamps and numeric timestamps
 * Returns a Date object for comparison
 */
const normalizeTimestamp = (timestamp: string | number | any): Date => {
  if (typeof timestamp === 'string') {
    // String timestamp format (e.g., "2025-05-15T23:47:56.206353593Z")
    return new Date(timestamp);
  } else if (typeof timestamp === 'number') {
    // Numeric timestamp (milliseconds since epoch)
    return new Date(timestamp);
  }
  // Default to epoch start for invalid formats
  return new Date(0);
};

/**
 * Fetch all lectures from Firebase and store them in IndexedDB
 */
export const syncAllLectures = async (): Promise<void> => {
  try {
    console.log('Syncing all lectures from Firebase to IndexedDB');

    // Check if we already have sync info
    const existingSyncInfo = await getSyncInfo();

    // Check if we've synced recently (within the last day)
    if (existingSyncInfo) {
      const ONE_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      const timeSinceLastSync = Date.now() - existingSyncInfo.lastSyncTimestamp;

      if (timeSinceLastSync < ONE_DAY) {
        console.log(`Full sync was performed ${Math.round(timeSinceLastSync / 1000 / 60 / 60)} hours ago. Using incremental sync instead.`);
        return syncNewLectures();
      }
    }

    // Get all lectures from Firebase
    // We'll fetch all lectures regardless of timestamp for a full sync
    console.log('Fetching all lectures from Firebase for full sync...');
    const lecturesRef = collection(db, 'lectures');
    const q = query(lecturesRef, orderBy('creationTimestamp', 'desc'));
    const querySnapshot = await getDocs(q);

    // Convert to array of lectures
    const lectures: Lecture[] = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Lecture));

    console.log(`Retrieved ${lectures.length} lectures from Firebase`);

    // Store lectures in IndexedDB
    await storeLectures(lectures);

    // --- Extract Unique Metadata ---

    const languages = new Set<string>();
    const translation = new Set<string>();
    const categories = new Set<string>();
    const countries = new Set<string>();
    const place = new Set<string>();
    const tags = new Set<string>();
    const years = new Set<string>();

    const currentYear = new Date().getFullYear();

    lectures.forEach(lecture => {
      if (lecture.language?.main) {
        languages.add(lecture.language.main);
      }

      if (Array.isArray(lecture.language?.translations)) {
        lecture.language.translations.forEach((t: string) => translation.add(t));
      }

      if (Array.isArray(lecture.category)) {
        lecture.category.forEach((c: string) => categories.add(c));
      }

      if (lecture.location?.country) {
        countries.add(lecture.location.country);
      }

      if (Array.isArray(lecture.place)) {
        lecture.place.forEach((p: string) => place.add(p));
      }

      if (Array.isArray(lecture.tags)) {
        lecture.tags.forEach((tag: string) => {
          if (tag.trim()) tags.add(tag);
        });
      }

      const year = lecture.dateOfRecording?.year;
      if (year && /^\d{4}$/.test(year)) {
        const numYear = parseInt(year, 10);
        if (numYear <= currentYear) {
          years.add(year);
        }
      }
    });

    // --- Save to localStorage ---

    const formatList = (set: Set<string>) =>
      Array.from(set)
        .filter(Boolean)
        .map((item) => ({ value: item, label: item }));

    localStorage.setItem('languages', JSON.stringify(formatList(languages)));
    localStorage.setItem('translation', JSON.stringify(formatList(translation)));
    localStorage.setItem('categories', JSON.stringify(formatList(categories)));
    localStorage.setItem('countries', JSON.stringify(formatList(countries)));
    localStorage.setItem('place', JSON.stringify(formatList(place)));
    localStorage.setItem('tags', JSON.stringify(formatList(tags)));

    localStorage.setItem('years', JSON.stringify(
      Array.from(years)
        .filter(Boolean)
        .sort((a, b) => Number(b) - Number(a))
        .map((year) => ({ value: year, label: year }))
    ));

    // Get the actual count from IndexedDB to ensure accuracy
    const actualCount = await getLectureCount();

    // Update sync info
    const syncInfo: SyncInfo = {
      id: 'syncInfo',
      lastSyncTimestamp: Date.now(),
      totalLectures: actualCount
    };

    await updateSyncInfo(syncInfo);

    console.log(`Synced ${lectures.length} lectures to IndexedDB. Total lectures in database: ${actualCount}`);
  } catch (error) {
    console.error('Error syncing all lectures:', error);
    throw error;
  }
};

/**
 * Sync only new lectures that were added since the latest lecture in IndexedDB
 */
export const syncNewLectures = async (): Promise<void> => {
  try {
    console.log('Syncing new lectures from Firebase to IndexedDB');

    // Get the last sync timestamp
    const syncInfo = await getSyncInfo();

    if (!syncInfo) {
      // If no sync info exists, perform a full sync
      return syncAllLectures();
    }

    // Check if we've synced recently (within the last hour)
    // const ONE_HOUR = 60 * 60 * 1000; // 1 hour in milliseconds
    // const timeSinceLastSync = Date.now() - syncInfo.lastSyncTimestamp;

    // if (timeSinceLastSync < ONE_HOUR) {
    //   console.log(`Last sync was ${Math.round(timeSinceLastSync / 1000 / 60)} minutes ago. Skipping sync.`);
    //   return;
    // }

    // Get all existing lectures from IndexedDB to find the latest creationTimestamp
    const existingLectures = await getAllLectures();

    // If no lectures exist yet, perform a full sync
    if (!existingLectures || existingLectures.length === 0) {
      console.log('No existing lectures found in IndexedDB. Performing full sync...');
      return syncAllLectures();
    }

    // Find the latest creationTimestamp among existing lectures
    let latestCreationTimestamp: string | null = null;
    let latestCreationDate = new Date(0); // Start with earliest possible date

    for (const lecture of existingLectures) {
      if (lecture.creationTimestamp) {
        // Use the helper function to normalize the timestamp
        const lectureDate = normalizeTimestamp(lecture.creationTimestamp);

        if (!isNaN(lectureDate.getTime()) && lectureDate > latestCreationDate) {
          latestCreationDate = lectureDate;
          // Store the original timestamp format for the query
          latestCreationTimestamp = typeof lecture.creationTimestamp === 'string'
            ? lecture.creationTimestamp
            : lectureDate.toISOString();
        }
      }
    }

    if (!latestCreationTimestamp) {
      // console.log('No valid creationTimestamp found in existing lectures. Performing full sync...');
      return syncAllLectures();
    }

    // console.log(`Latest lecture creationTimestamp in IndexedDB: ${latestCreationTimestamp} (${latestCreationDate.toLocaleString()})`);

    // Get lectures created after the latest lecture timestamp
    console.log(`Querying Firebase for lectures with creationTimestamp > ${latestCreationTimestamp}`);
    const lecturesRef = collection(db, 'lectures');
    const q = query(
      lecturesRef,
      where('creationTimestamp', '>', latestCreationTimestamp),
      orderBy('creationTimestamp', 'desc')
    );

    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.size} new lectures in Firebase`);

    // If no new lectures, just update the timestamp
    if (querySnapshot.empty) {
      await updateSyncInfo({
        ...syncInfo,
        lastSyncTimestamp: Date.now()
      });
      console.log('No new lectures to sync');
      return;
    }

    // Convert to array of lectures
    const newLectures: Lecture[] = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Lecture));

    // Store new lectures in IndexedDB
    await storeLectures(newLectures);

    // Get the actual count from IndexedDB to ensure accuracy
    const actualCount = await getLectureCount();

    // Update sync info
    await updateSyncInfo({
      id: 'syncInfo',
      lastSyncTimestamp: Date.now(),
      totalLectures: actualCount
    });

    console.log(`Synced ${newLectures.length} new lectures to IndexedDB. Total lectures in database: ${actualCount}`);
  } catch (error) {
    console.error('Error syncing new lectures:', error);
    throw error;
  }
};

/**
 * Fetch and merge lectureInfo data for the current user
 * Updates existing fields in local lectures if they've been updated in Firebase
 */
export const syncLectureInfo = async (): Promise<void> => {
  try {
    console.log('Syncing lectureInfo data from Firebase to IndexedDB');

    // Get the current user ID from localStorage
    const userId = localStorage.getItem('firebaseUid');
    if (!userId) {
      console.error('No user ID found in localStorage');
      return;
    }

    // Get all lectures from IndexedDB
    const lectures = await getAllLectures();
    if (!lectures || lectures.length === 0) {
      console.log('No lectures found in IndexedDB to merge with lectureInfo');
      return;
    }

    // Create a map of lectures by ID for faster lookup
    const lecturesMap = new Map();
    lectures.forEach(lecture => {
      lecturesMap.set(lecture.id.toString(), lecture);
    });

    // Fetch all documents from the user's lectureInfo subcollection
    console.log(`Fetching lectureInfo for user ${userId}`);
    const lectureInfoRef = collection(db, `users/${userId}/lectureInfo`);
    const lectureInfoSnapshot = await getDocs(lectureInfoRef);

    if (lectureInfoSnapshot.empty) {
      console.log('No lectureInfo documents found for the user');
      return;
    }

    console.log(`Found ${lectureInfoSnapshot.size} lectureInfo documents`);

    // Process each lectureInfo document
    const updatedLectures: Lecture[] = [];

    lectureInfoSnapshot.forEach(doc => {
      const lectureInfo = doc.data();

      // Find the corresponding lecture by ID
      const lectureId = lectureInfo.id?.toString();
      if (!lectureId) {
        console.warn('lectureInfo document missing id field:', doc.id);
        return;
      }

      const lecture = lecturesMap.get(lectureId);
      if (!lecture) {
        // If the lecture doesn't exist in IndexedDB but exists in Firebase lectureInfo,
        // we'll create a new document in IndexedDB with the lectureInfo data
        console.log(`Creating new lecture document for lectureInfo with id ${lectureId}`);

        // Create a minimal lecture document with the lectureInfo data
        const newLecture: Lecture = {
          id: lectureId,
          ...lectureInfo,
          // Add required fields with default values if they don't exist in lectureInfo
          title: lectureInfo.title || [],
          thumbnail: lectureInfo.thumbnail || '',
          audioUrl: lectureInfo.audioUrl || '',
          category: lectureInfo.category || [],
          language: lectureInfo.language || { main: '' },
          dateOfRecording: lectureInfo.dateOfRecording || {},
          length: lectureInfo.length || 0,
          creationTimestamp: lectureInfo.creationTimestamp || Date.now(),
        };

        updatedLectures.push(newLecture);
        return;
      }

      // Merge lectureInfo data into the lecture document
      const mergedLecture = { ...lecture };
      let isUpdated = false;

      // Add or update lectureInfo fields in the lecture
      Object.keys(lectureInfo).forEach(key => {
        // Skip the id field since it's already in the lecture
        if (key === 'id') return;

        // Check if the field needs to be updated
        // Update if:
        // 1. The field doesn't exist in the lecture, or
        // 2. The field exists but has a different value in lectureInfo
        const shouldUpdate =
          !(key in lecture) ||
          lecture[key] === undefined ||
          lecture[key] === null ||
          JSON.stringify(lecture[key]) !== JSON.stringify(lectureInfo[key]);

        if (shouldUpdate) {
          mergedLecture[key] = lectureInfo[key];
          isUpdated = true;

          // Log the update for debugging
          if (key in lecture && lecture[key] !== undefined && lecture[key] !== null) {
            console.log(`Updating field '${key}' for lecture ${lectureId}: ${JSON.stringify(lecture[key])} -> ${JSON.stringify(lectureInfo[key])}`);
          }
        }
      });

      // Only add to updatedLectures if something changed
      if (isUpdated) {
        updatedLectures.push(mergedLecture);
      }
    });

    if (updatedLectures.length > 0) {
      console.log(`Updating ${updatedLectures.length} lectures with lectureInfo data`);
      await storeLectures(updatedLectures);
    } else {
      console.log('No lectures needed updates from lectureInfo');
    }

    console.log('lectureInfo sync completed successfully');
  } catch (error) {
    console.error('Error syncing lectureInfo:', error);
    throw error;
  }
};

/**
 * Main sync function that decides whether to do a full sync or incremental sync
 * based on whether we have existing data in IndexedDB
 */
export const syncLectures = async (): Promise<void> => {
  try {
    console.log('Starting sync process...');

    // Get sync info to check if we've synced before
    const syncInfo = await getSyncInfo();

    if (syncInfo && syncInfo.totalLectures > 0) {
      console.log(`Found existing data: ${syncInfo.totalLectures} lectures, last sync: ${new Date(syncInfo.lastSyncTimestamp).toLocaleString()}`);
      console.log('Performing incremental sync based on latest lecture timestamp...');
      // We have existing data, so sync new lectures based on the latest lecture timestamp
      await syncNewLectures();
    } else {
      console.log('No existing data found. Performing full sync...');
      // First time sync - get all lectures
      await syncAllLectures();
    }

    // After syncing lectures, sync lectureInfo data
    console.log('Syncing lectureInfo data...');
    await syncLectureInfo();

    console.log('Sync process completed successfully');
  } catch (error) {
    console.error('Error in syncLectures:', error);
    throw error;
  }
};
