"use client";
import React, { useState, useCallback, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { <PERSON>o } from "next/font/google";
import { Button, Input } from "antd";
import SearchResults from "./searchResults";
import {
    getTranscriptionSearchResults,
    getBooksSearchResults,
    SearchResult,
} from "@/src/api/knowledge-base.api";
import { getAuth } from "firebase/auth";
import { useFilterContext } from "@/src/context/filter.context";
import { useBookFilterContext } from "@/src/context/book-filter.context";

const roboto = Roboto({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

// Default page size for search results
const DEFAULT_PAGE_SIZE = 20;

const KnowledgrBase = () => {
    const searchParams = useSearchParams();
    const [activeTab, setActiveTab] = useState("transcriptions");
    const [searchQuery, setSearchQuery] = useState("");
    const [isSearched, setIsSearched] = useState(false);
    const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
    const [totalResults, setTotalResults] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(false);
    const [from, setFrom] = useState(0);
    const [activeFilters, setActiveFilters] = useState<{
        [key: string]: string[];
    }>({
        LANGUAGE: [],
        COUNTRY: [],
        PLACE: [],
        YEAR: [],
        MONTH: [],
        CATEGORY: [],
        TRANSLATION: [],
        BOOKS: [],
    });

    // Get filter contexts
    const { setSelectedFilterValues } = useFilterContext();
    const { clearSelectedBooks } = useBookFilterContext();

    // Check for search query and active tab in URL parameters (for returning from detail pages)
    useEffect(() => {
        const queryFromUrl = searchParams.get("search");
        const activeTabFromUrl = searchParams.get("activeTab");

        // Only process URL parameters if they exist
        if (queryFromUrl || activeTabFromUrl) {
            // Set active tab if provided in URL
            if (
                activeTabFromUrl &&
                (activeTabFromUrl === "transcriptions" ||
                    activeTabFromUrl === "books")
            ) {
                setActiveTab(activeTabFromUrl);
            }

            if (queryFromUrl) {
                setSearchQuery(queryFromUrl);
                // Trigger search with the query from URL
                setTimeout(() => {
                    searchWithFilters(
                        queryFromUrl,
                        0,
                        true,
                        {
                            LANGUAGE: [],
                            COUNTRY: [],
                            PLACE: [],
                            YEAR: [],
                            MONTH: [],
                            CATEGORY: [],
                            TRANSLATION: [],
                            BOOKS: [],
                        },
                        activeTabFromUrl || undefined // Pass the active tab from URL to the search function
                    );
                    setIsSearched(true);
                }, 100);
            }

            // Clear URL parameters after processing them
            // Use history.replaceState to update the URL without causing a page reload
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);
        }
    }, [searchParams]);

    const handleTabClick = (tab: string) => {
        // Clear filters when changing tabs
        if (tab !== activeTab) {
            if (tab === "transcriptions") {
                // Clear book filters when switching to transcriptions
                clearSelectedBooks();
            } else {
                // Clear transcription filters when switching to books
                setSelectedFilterValues({
                    LANGUAGE: [],
                    COUNTRY: [],
                    PLACE: [],
                    YEAR: [],
                    MONTH: [],
                    CATEGORY: [],
                    TRANSLATION: [],
                });
            }

            // Reset active filters
            setActiveFilters({
                LANGUAGE: [],
                COUNTRY: [],
                PLACE: [],
                YEAR: [],
                MONTH: [],
                CATEGORY: [],
                TRANSLATION: [],
                BOOKS: [],
            });
        }

        // Set the active tab
        setActiveTab(tab);

        // If already searched, perform a new search with the new tab and empty filters
        if (isSearched) {
            setFrom(0);
            setSearchResults([]);

            // Create a new searchWithFilters call with empty filters
            const emptyFilters = {
                LANGUAGE: [],
                COUNTRY: [],
                PLACE: [],
                YEAR: [],
                MONTH: [],
                CATEGORY: [],
                TRANSLATION: [],
                BOOKS: [],
            };

            // Use the empty filters for the search
            searchWithFilters(searchQuery, 0, true, emptyFilters, tab);
        }
    };

    const handleSearch = (reset: boolean = true, tabOverride?: string) => {
        // Check if search query is empty
        if (searchQuery.trim() === "") {
            setIsSearched(false);
            setSearchResults([]);
            setTotalResults(0);
            setHasMore(false);
            return;
        }

        // Set search state
        setIsSearched(true);

        // If reset, clear previous results and start from beginning
        if (reset) {
            setSearchResults([]);
            setFrom(0);
        }

        // Use searchWithFilters to ensure we use the latest filter values
        searchWithFilters(
            searchQuery,
            reset ? 0 : from,
            reset,
            activeFilters,
            tabOverride
        );
    };

    // We now use searchWithFilters instead of fetchSearchResultsForTab

    // Function to search with specific filters - this ensures we use the latest filter values
    // instead of relying on the state which might not be updated yet
    const searchWithFilters = async (
        query: string,
        fromIndex: number,
        reset: boolean,
        filters: { [key: string]: string[] } = {},
        tabOverride?: string
    ) => {
        if (!query.trim()) return;

        try {
            setIsLoading(true);

            // Get current user's ID token if available
            const auth = getAuth();
            const user = auth.currentUser;
            let idToken = null;

            if (user) {
                idToken = await user.getIdToken();
            }

            // Prepare base query parameters
            const queryParams: any = {
                query,
                from: fromIndex,
                size: DEFAULT_PAGE_SIZE,
            };

            // Determine the current tab
            const currentTab = tabOverride || activeTab;

            // Add filter parameters based on the current tab
            if (currentTab === "transcriptions") {
                // Only add transcription-related filters
                if (filters.LANGUAGE && filters.LANGUAGE.length > 0) {
                    queryParams.language = filters.LANGUAGE.join(",");
                }
                if (filters.YEAR && filters.YEAR.length > 0) {
                    queryParams.year = filters.YEAR.join(",");
                }
                if (filters.COUNTRY && filters.COUNTRY.length > 0) {
                    queryParams.country = filters.COUNTRY.join(",");
                }
                if (filters.PLACE && filters.PLACE.length > 0) {
                    queryParams.place = filters.PLACE.join(",");
                }
                if (filters.CATEGORY && filters.CATEGORY.length > 0) {
                    queryParams.category = filters.CATEGORY.join(",");
                }
                if (filters.MONTH && filters.MONTH.length > 0) {
                    queryParams.month = filters.MONTH.join(",");
                }
            } else if (currentTab === "books") {
                // Only add book-related filters
                if (filters.BOOKS && filters.BOOKS.length > 0) {
                    queryParams.bookIds = filters.BOOKS.join(",");
                }
            }

            console.log("Search with direct filters:", queryParams);
            console.log("Using tab for search:", currentTab);

            let response;
            let newResults: SearchResult[] = [];
            let itemsLength = 0;

            // Call the appropriate API based on the current tab
            if (currentTab === "transcriptions") {
                // Call transcription search API
                response = await getTranscriptionSearchResults(
                    queryParams,
                    idToken
                );

                if (response && response.data) {
                    // If reset is true, replace the results, otherwise append
                    newResults = reset
                        ? response.data.transcriptions
                        : [...searchResults, ...response.data.transcriptions];

                    itemsLength = response.data.transcriptions.length;
                }
            } else if (currentTab === "books") {
                // Call book search API
                response = await getBooksSearchResults(queryParams, idToken);

                if (response && response.data) {
                    // If reset is true, replace the results, otherwise append
                    newResults = reset
                        ? response.data.books
                        : [...searchResults, ...response.data.books];

                    itemsLength = response.data.books.length;
                }
            }

            // Check if the response is valid
            if (response && response.data) {
                setSearchResults(newResults);
                setTotalResults(response.data.total);

                // If we received fewer items than requested or we've loaded all results, there are no more results
                if (
                    itemsLength < DEFAULT_PAGE_SIZE ||
                    newResults.length >= response.data.total
                ) {
                    setHasMore(false);
                } else {
                    setHasMore(true);
                }

                // Update the from index for the next page
                const newFromIndex = fromIndex + itemsLength;
                setFrom(newFromIndex);
            }
        } catch (error) {
            console.error("Error fetching search results with filters:", error);
        } finally {
            setIsLoading(false);
        }
    };

    // Function to load more results when scrolling
    const loadMoreResults = useCallback(() => {
        if (!hasMore || isLoading) {
            return;
        }
        // Use searchWithFilters to ensure we use the latest filter values
        searchWithFilters(searchQuery, from, false, activeFilters);
    }, [searchQuery, from, hasMore, isLoading, activeTab, activeFilters]);

    // Debug the current state
    useEffect(() => {}, [searchResults, totalResults, hasMore, from]);

    return (
        <div className={`w-full h-full ${roboto.className}`}>
            {/* Header: Title Section */}
            <div className="flex justify-center items-center space-x-2 sm:space-x-5 py-4 border-b">
                <span className="text-[28px] sm:text-[36px] md:text-[48px] font-[700] text-[#3fa1d1]">
                    Knowledge
                </span>{" "}
                <span>
                    <Image
                        // src={`https://bvksmedia.com/assets/img/knowledge_base.svg`}
                        src={`/images/sidebar/knowledge_base.svg`}
                        alt="Knowledge Base"
                        width={60}
                        height={60}
                        className="w-[40px] h-[40px] sm:w-[50px] sm:h-[50px] md:w-[60px] md:h-[60px]"
                    />
                </span>{" "}
                <span className="text-[28px] sm:text-[36px] md:text-[48px] font-[700] text-primary">
                    Base
                </span>{" "}
            </div>

            {/* Body: Search Section */}
            <div className="pt-8">
                {/* Tabs and Search Input */}
                <div className="bg-white rounded-lg overflow-hidden flex flex-col md:flex-row justify-center items-center gap-3 mb-6 px-4 md:px-0">
                    {/* Tabs */}
                    <div className="flex w-full md:w-auto my-3 md:my-0">
                        <button
                            type="button"
                            className={`px-4 sm:px-6 py-2 sm:py-3 h-[40px] sm:h-[45px] text-xs sm:text-sm font-medium rounded-l-[10px] flex-1 md:flex-none ${
                                activeTab === "transcriptions"
                                    ? "bg-[#3fa1d1] text-white"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            } transition-colors duration-200 focus:outline-none`}
                            onClick={() => handleTabClick("transcriptions")}
                        >
                            Transcriptions
                        </button>
                        <button
                            type="button"
                            className={`px-4 sm:px-6 py-2 sm:py-3 h-[40px] sm:h-[45px] text-xs sm:text-sm font-medium rounded-r-[10px] flex-1 md:flex-none ${
                                activeTab === "books"
                                    ? "bg-[#3fa1d1] text-white"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            } transition-colors duration-200 focus:outline-none`}
                            onClick={() => handleTabClick("books")}
                        >
                            Books
                        </button>
                    </div>

                    {/* Search Input and Button */}
                    <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3 mb-3 md:mb-0">
                        <Input
                            className="flex-grow h-[40px] sm:h-[45px] w-full rounded-[10px] text-sm shadow-none !outline-none hover:!border-primary focus:!border-primary"
                            placeholder="Type your search request"
                            value={searchQuery}
                            onChange={(e) => {
                                const newValue = e.target.value;
                                setSearchQuery(newValue);

                                // If search query is cleared, reset to default view
                                if (newValue === "" && isSearched) {
                                    setIsSearched(false);
                                    setSearchResults([]);
                                }
                            }}
                            onPressEnter={() => handleSearch()}
                            allowClear
                        />
                        <Button
                            className="h-[40px] sm:h-[45px] w-full sm:w-[150px] !rounded-[10px]"
                            style={{
                                backgroundColor: `var(--primary-color)`,
                                color: "#fff",
                                border: `1px solid var(--primary-color)`,
                                opacity: searchQuery.trim() === "" ? 0.7 : 1,
                            }}
                            onClick={() => handleSearch()}
                            disabled={searchQuery.trim() === ""}
                        >
                            <div className="flex items-center justify-center gap-2">
                                <Image
                                    src="/images/navbar/search.png"
                                    width={14}
                                    height={14}
                                    alt="Search Icon"
                                    className="cursor-pointer"
                                />
                                <span
                                    className={`${roboto.className} font-[500]`}
                                >
                                    Search
                                </span>
                            </div>
                        </Button>
                    </div>
                </div>

                {/* Search Results or How to Use the Search */}
                <div
                    id="knowledgeBaseScrollable"
                    className="h-[calc(100vh-265px)] md:h-[calc(100vh-265px)] overflow-y-auto scrollbar"
                >
                    {isSearched ? (
                        <div className="w-full">
                            <SearchResults
                                searchQuery={searchQuery}
                                results={searchResults}
                                totalResults={totalResults}
                                isLoading={isLoading}
                                hasMore={hasMore}
                                loadMore={loadMoreResults}
                                resultType={
                                    activeTab === "transcriptions"
                                        ? "transcription"
                                        : "book"
                                }
                                handleFilterChange={(filters) => {
                                    console.log(
                                        "Filter applied in KnowledgeBase:",
                                        filters
                                    );
                                    setActiveFilters(filters);
                                    setFrom(0);
                                    setSearchResults([]);
                                    searchWithFilters(
                                        searchQuery,
                                        0,
                                        true,
                                        filters
                                    );
                                }}
                            />
                        </div>
                    ) : (
                        <div className="px-4 sm:px-6 sm:pr-6 md:pr-[50px] lg:pr-[100px]">
                            <h2 className="text-[22px] sm:text-[24px] md:text-[28px] font-semibold mb-4">
                                How to Use the Search Effectively
                            </h2>
                            <p className="text-gray-700 mb-4 text-[14px] sm:text-[16px] md:text-[18px] text-justify">
                                Most transcripts use standard English spellings
                                without diacritical marks. To maximize your
                                search results, start by using the standard
                                English spelling of your keywords. If necessary,
                                you can then try alternative or diacritized
                                spellings to find additional information, though
                                this may not always yield more results. Be aware
                                that searching with diacritics may sometimes
                                return no results.
                            </p>

                            <h3 className="text-[16px] sm:text-[18px] md:text-[20px] font-medium mt-6 mb-2">
                                Examples:
                            </h3>
                            <ul className="list-disc pl-4 sm:pl-6 space-y-4">
                                <li>
                                    <p className="font-medium">Upanishad:</p>
                                    <ul className="list-disc pl-4 sm:pl-6 space-y-1">
                                        <li>
                                            First Search: Use{" "}
                                            <span className="font-semibold">
                                                "Upanishad"
                                            </span>{" "}
                                            to retrieve the maximum number of
                                            results.
                                        </li>
                                        <li>
                                            Alternative Search: Then try{" "}
                                            <span className="font-semibold">
                                                "Upanisad"
                                            </span>{" "}
                                            for any additional matches.
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <p className="font-medium">
                                        Bhagavad Gita:
                                    </p>
                                    <ul className="list-disc pl-4 sm:pl-6 space-y-1">
                                        <li>
                                            First Search: Begin with{" "}
                                            <span className="font-semibold">
                                                "Bhagavad Gita"
                                            </span>{" "}
                                            to find the most entries.
                                        </li>
                                        <li>
                                            Alternative Search: You can also
                                            search for{" "}
                                            <span className="font-semibold">
                                                "Bhagavad Gītā,"
                                            </span>{" "}
                                            although it may or may not provide
                                            more results.
                                        </li>
                                    </ul>
                                </li>
                            </ul>

                            <p className="text-gray-700 mt-4 text-[14px] sm:text-[16px]">
                                By starting with standard spellings and then
                                exploring variations, you'll increase your
                                chances of finding comprehensive information on
                                your topic.
                            </p>

                            {/* Advanced Search Techniques */}
                            <div className="mt-8">
                                <h3 className="text-[16px] sm:text-lg font-medium mb-3 sm:mb-4">
                                    1. Phrase Search
                                </h3>
                                <ul className="list-disc pl-4 sm:pl-6 space-y-2">
                                    <li>
                                        <span className="font-medium">
                                            Use:
                                        </span>{" "}
                                        Enclose exact phrases in quotation marks
                                        " ".
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        "pure devotional service"
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Result:
                                        </span>{" "}
                                        Finds passages like:
                                        <p className="italic mt-1 text-[13px] sm:text-[14px]">
                                            "So the guru gives the seed of pure
                                            devotional service."
                                        </p>
                                    </li>
                                </ul>

                                <h3 className="text-[16px] sm:text-lg font-medium mt-5 sm:mt-6 mb-3 sm:mb-4">
                                    2. Wildcard Searches
                                </h3>
                                <ul className="list-disc pl-4 sm:pl-6 space-y-2">
                                    <li>
                                        <span className="font-medium">
                                            Single Character Wildcard ?
                                        </span>{" "}
                                        : Replaces a single character.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        te?t (finds "test" or "text").
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Multiple Character Wildcard *
                                        </span>{" "}
                                        : Replaces zero or more characters.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        devot* (finds "devotee," "devotion,"
                                        "devotional").
                                    </li>
                                </ul>

                                <h3 className="text-[16px] sm:text-lg font-medium mt-5 sm:mt-6 mb-3 sm:mb-4">
                                    3. Proximity Search with Wildcards
                                </h3>
                                <ul className="list-disc pl-4 sm:pl-6 space-y-2">
                                    <li>
                                        <span className="font-medium">
                                            Use:
                                        </span>{" "}
                                        Enclose words in quotes and add ~ plus a
                                        number to specify distance. Wildcards
                                        can also be used within the words.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example 1:
                                        </span>{" "}
                                        "guru disciple"~5
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Result:
                                        </span>{" "}
                                        Finds where "guru" and "disciple" appear
                                        within five words.
                                        <p className="italic mt-1 text-[13px] sm:text-[14px]">
                                            "Every disciple is supposed to see
                                            his guru like that..."
                                        </p>
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example 2:
                                        </span>{" "}
                                        "guru* disciple*"~5
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Result:
                                        </span>{" "}
                                        Finds where variations like "gurudev" or
                                        "gurus" and "disciple" or "disciples"
                                        appear within five words.
                                    </li>
                                </ul>

                                <h3 className="text-[16px] sm:text-lg font-medium mt-5 sm:mt-6 mb-3 sm:mb-4">
                                    4. Boolean Operators
                                </h3>
                                <ul className="list-disc pl-4 sm:pl-6 space-y-2">
                                    <li>
                                        <span className="font-medium">
                                            AND:
                                        </span>{" "}
                                        Finds documents with both terms.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        "spiritual master" AND initiation
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Result:
                                        </span>{" "}
                                        Passages like:
                                        <p className="italic mt-1 text-[13px] sm:text-[14px]">
                                            "One accepts initiation to get the
                                            seed of pure devotional service from
                                            spiritual master"
                                        </p>
                                    </li>
                                    <li>
                                        <span className="font-medium">OR:</span>{" "}
                                        Finds documents with either term.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        Krishna OR guru
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            NOT:
                                        </span>{" "}
                                        Excludes documents with the term after
                                        NOT.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        devotional NOT material
                                    </li>
                                </ul>

                                <h3 className="text-[16px] sm:text-lg font-medium mt-5 sm:mt-6 mb-3 sm:mb-4">
                                    5. Boosting Terms
                                </h3>
                                <ul className="list-disc pl-4 sm:pl-6 space-y-2">
                                    <li>
                                        <span className="font-medium">
                                            Use:
                                        </span>{" "}
                                        Add ^ and a number to a term to increase
                                        its importance.
                                    </li>
                                    <li>
                                        <span className="font-medium">
                                            Example:
                                        </span>{" "}
                                        Krishna^5 "pure devotional service"
                                    </li>
                                </ul>

                                <p className="text-gray-700 my-4 pb-[60px] sm:pb-[100px] text-[14px] sm:text-[16px]">
                                    By applying these examples, you can
                                    efficiently search the transcript to find
                                    relevant information.
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default KnowledgrBase;
