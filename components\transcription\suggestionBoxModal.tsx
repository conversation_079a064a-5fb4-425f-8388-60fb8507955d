"use client";
import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Input, message } from "antd";
import { Poppins } from "next/font/google";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const { TextArea } = Input;

interface SuggestionBoxModalProps {
    isModalOpen: boolean;
    setIsModalOpen: (isOpen: boolean) => void;
    timestamp: string;
    originalText: string;
    transcriptionId: number;
    onSubmit: (newText: string, reason: string) => void;
}

const SuggestionBoxModal = ({
    isModalOpen,
    setIsModalOpen,
    timestamp,
    originalText,
    transcriptionId,
    onSubmit,
}: SuggestionBoxModalProps) => {
    const [editText, setEditText] = useState("");
    const [reason, setReason] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Reset the edit text and reason when the modal opens with new content
    useEffect(() => {
        if (isModalOpen) {
            setEditText(originalText);
            setReason("");
            setIsSubmitting(false);
        }
    }, [isModalOpen, originalText]);

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const handleSubmit = async () => {
        if (editText.trim() && editText !== originalText) {
            setIsSubmitting(true);
            try {
                await onSubmit(editText, reason);
                setIsModalOpen(false);
            } catch (error) {
                // Error handling is done in the parent component
                setIsSubmitting(false);
            }
        }
    };

    const isSubmitDisabled =
        !editText.trim() || editText === originalText || isSubmitting;

    return (
        <Modal
            title={
                <div className={poppins.className}>
                    <h1 className="text-[20px] leading-8 font-[600]">
                        Suggest Edit for Timestamp
                    </h1>
                    <div className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mt-2">
                        {timestamp}
                    </div>
                </div>
            }
            open={isModalOpen}
            onCancel={handleCancel}
            footer={null}
            centered
            width={500}
            className={poppins.className}
        >
            <div className="flex flex-col gap-4 mt-4">
                <div>
                    <p className="text-sm text-gray-500 mb-2">Original Text:</p>
                    <div className="p-3 bg-gray-100 rounded mb-4 text-gray-800">
                        {originalText}
                    </div>
                </div>

                <div>
                    <p className="text-sm text-gray-500 mb-2">
                        Your Suggestion:
                    </p>
                    <TextArea
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        placeholder="Enter your suggested edit here"
                        style={{
                            height: 100,
                            resize: "none",
                            borderRadius: "8px",
                        }}
                        className="py-2 px-3 !shadow-none !outline-none hover:!border-primary focus:!border-primary"
                    />
                </div>

                <div>
                    <p className="text-sm text-gray-500 mb-2">
                        Reason for Edit (Optional):
                    </p>
                    <TextArea
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        placeholder="Explain why you're suggesting this edit"
                        style={{
                            height: 60,
                            resize: "none",
                            borderRadius: "8px",
                        }}
                        className="py-2 px-3 !shadow-none !outline-none hover:!border-primary focus:!border-primary"
                    />
                </div>

                <div className="flex justify-end gap-3 mt-2">
                    <Button
                        onClick={handleCancel}
                        className={`px-4 h-[38px] rounded-[10px] border border-gray-300 hover:bg-gray-100 ${poppins.className}`}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        className={`px-4 h-[38px] text-white rounded-[10px] ${poppins.className}`}
                        style={{
                            backgroundColor: `var(--primary-color)`,
                            color: "#fff",
                            border: "1px solid #f39c12",
                            opacity: isSubmitDisabled ? 0.7 : 1,
                        }}
                        disabled={isSubmitDisabled}
                        loading={isSubmitting}
                    >
                        Submit Suggestion
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default SuggestionBoxModal;
