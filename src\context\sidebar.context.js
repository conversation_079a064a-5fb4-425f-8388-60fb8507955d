"use client";
import React, { createContext, useContext, useState } from "react";

const SidebarContext = createContext();

export const SidebarContextProvider = ({ children }) => {
    const [isCollapsed, setIsCollapsed] = useState(true);
    const [isTabChangeLoading, setIsTabChangeLoading] = useState(false);

  return (
    <SidebarContext.Provider
      value={{
        isCollapsed, setIsCollapsed, isTabChangeLoading, setIsTabChangeLoading
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebarContext = () => {
  return useContext(SidebarContext);
};
