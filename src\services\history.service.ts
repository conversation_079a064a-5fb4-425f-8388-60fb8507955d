import { fetchAllHistory } from "../api/history.api";
import { getAllLectures } from "./indexedDB.service";
import { getDeepSearchResults } from "../api/mediaLibrary.api";
import { applyFilters, applySorting } from "../utils/helperFunctions";

export interface FetchHistoryParams {
  searchQuery?: string;
  periodFilter?: number;
  sortBy?: number;
  allFilter?: { [key: string]: string[] };
  playbackMode?: number;
}

export interface FetchDeepSearchParams {
  searchQuery: string;
  playbackMode?: number;
  sortBy?: number;
  allFilter?: { [key: string]: string[] };
}

// Helper function to filter history documents based on period
const filterHistoryByPeriod = (allHistory: any[], historyPeriod: number = 1) => {
  if (historyPeriod === 1) {
    // All History - return all documents
    return allHistory;
  }

  const now = new Date();
  const currentMonth = now.getMonth() + 1; // getMonth() returns 0-11
  const currentYear = now.getFullYear();

  return allHistory.filter((doc: any) => {
    if (!doc.id) return false;

    try {
      // Parse documentId format "15-5-2025" (day-month-year)
      const [day, month, year] = doc.id.split("-").map(Number);
      const docDate = new Date(year, month - 1, day); // month - 1 because Date constructor expects 0-11

      if (historyPeriod === 2) {
        // This Week - get start of current week (Sunday)
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);

        return docDate >= startOfWeek && docDate <= endOfWeek;
      } else if (historyPeriod === 3) {
        // This Month
        return month === currentMonth && year === currentYear;
      }

      return false;
    } catch (error) {
      console.error("Error parsing documentId:", doc.id, error);
      return false;
    }
  });
};

// Helper function to apply search filtering
const applySearchFilter = (lectures: any[], searchQuery: string) => {
  if (!searchQuery.trim()) return lectures;

  // Convert search query to lowercase for case-insensitive comparison
  const query = searchQuery.toLowerCase().trim();

  // Split the query into words for full name search
  const queryWords = query
    .split(/\s+/)
    .filter((word: string) => word.length > 0);

  // Filter lectures by comparing search query with title array elements
  return lectures.filter((lecture: any) => {
    // Check if title exists and is an array
    if (!lecture.title || !Array.isArray(lecture.title)) return false;

    // Check for exact match first (highest priority)
    if (
      lecture.title.some(
        (titlePart: string) => titlePart.toLowerCase() === query
      )
    ) {
      return true;
    }

    // Check if any element in the title array contains the full search query
    if (
      lecture.title.some((titlePart: string) =>
        titlePart.toLowerCase().includes(query)
      )
    ) {
      return true;
    }

    // If the query has multiple words, check if all words appear in any title part
    if (queryWords.length > 1) {
      // Join all title parts into a single string for multi-word search
      const fullTitle = lecture.title.join(" ").toLowerCase();

      // Check if all query words are present in the full title
      const allWordsPresent = queryWords.every((word: string) =>
        fullTitle.includes(word)
      );

      if (allWordsPresent) {
        return true;
      }
    }

    return false;
  });
};

/**
 * Fetch and process history lectures based on provided parameters
 * @param params - Object containing search query, period filter, sort option, and filter values
 * @returns Promise<any[]> - Array of processed lectures
 */
export const fetchHistory = async (params: FetchHistoryParams = {}): Promise<any[]> => {
  const {
    searchQuery = "",
    periodFilter = 1,
    sortBy = 1,
    allFilter = {},
    playbackMode = 1
  } = params;

  try {
    // Fetch all history from Firebase
    const allHistory = await fetchAllHistory();
    if (!allHistory || allHistory.length === 0) {
      return [];
    }

    // Filter history documents based on selected period
    const filteredHistory = filterHistoryByPeriod(allHistory, periodFilter);
    console.log(`Filtered history for period ${periodFilter}:`, filteredHistory);

    // Extract unique lecture IDs from filtered history
    let historyLecturesId: any = [];
    filteredHistory.forEach((doc: any) => {
      if (doc?.playedIds?.length > 0) {
        historyLecturesId = [...historyLecturesId, ...doc?.playedIds];
      }
    });
    const uniqueHistoryLectureIds = Array.from(new Set(historyLecturesId));
    console.log("uniqueHistoryLectureIds", uniqueHistoryLectureIds);

    if (uniqueHistoryLectureIds.length === 0) {
      return [];
    }

    // Get all lectures from IndexedDB
    const allLectures = await getAllLectures();

    // Get lectures in the same order as uniqueHistoryLectureIds
    let historyLectures = uniqueHistoryLectureIds
      .map((id: any) =>
        allLectures.find((lecture: any) => lecture.id === id)
      )
      .filter((lecture: any) => lecture !== undefined);

    // Apply search filtering if search query is provided
    if (searchQuery.trim()) {
      historyLectures = applySearchFilter(historyLectures, searchQuery);
    }

    // Apply filters (including playback mode)
    const hasActiveFilters = Object.values(allFilter).some(
      (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
    );

    if (hasActiveFilters || playbackMode !== 1) {
      historyLectures = applyFilters(historyLectures, allFilter, playbackMode);
    }

    // Apply sorting
    if (sortBy !== 1) {
      historyLectures = applySorting(historyLectures, sortBy);
    }

    console.log(`Processed ${historyLectures.length} history lectures`);
    return historyLectures;

  } catch (error) {
    console.error("Error in fetchHistory:", error);
    throw error;
  }
};

/**
 * Fetch and process deep search lectures based on provided parameters
 * @param params - Object containing search query, playback mode, sort option, and filter values
 * @returns Promise<any[]> - Array of processed lectures
 */
export const fetchDeepSearchLectures = async (params: FetchDeepSearchParams): Promise<any[]> => {
  const {
    searchQuery,
    playbackMode = 1,
    sortBy = 1,
    allFilter = {}
  } = params;

  if (!searchQuery.trim()) {
    return [];
  }

  try {
    // Prepare deep search parameters
    const searchParams = {
      query: searchQuery,
      size: 9000,
      from: 0,
    };

    // Get all lectures from IndexedDB and perform deep search
    const [allLectures, response] = await Promise.all([
      getAllLectures(),
      getDeepSearchResults(searchParams)
    ]);

    if (!response?.data?.transcriptions) {
      return [];
    }

    // Extract lecture IDs from deep search response
    const responseIds = response.data.transcriptions.map((lecture: any) => lecture.id) || [];

    // Get matching lectures from IndexedDB
    let deepSearchLectures = responseIds
      .map((id: any) =>
        allLectures.find((lecture: any) => lecture.id === id)
      )
      .filter((lecture: any) => lecture !== undefined);

    // Apply filters (including playback mode)
    const hasActiveFilters = Object.values(allFilter).some(
      (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
    );

    if (hasActiveFilters || playbackMode !== 1) {
      deepSearchLectures = applyFilters(deepSearchLectures, allFilter, playbackMode);
    }

    // Apply sorting
    if (sortBy !== 1) {
      deepSearchLectures = applySorting(deepSearchLectures, sortBy);
    }

    console.log(`Processed ${deepSearchLectures.length} deep search lectures`);
    return deepSearchLectures;

  } catch (error) {
    console.error("Error in fetchDeepSearchLectures:", error);
    throw error;
  }
};


// Function to clear whole history
export const clearAllHistory = async () => {

}