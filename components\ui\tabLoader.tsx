"use client";

import React from "react";
import { Roboto } from "next/font/google";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface TabLoaderProps {
  message?: string;
  visible: boolean;
}

const TabLoader: React.FC<TabLoaderProps> = ({
  message = "Loading...",
  visible,
}) => {
  if (!visible) return null;

  return (
    <div
      className={`w-full h-full flex flex-col items-center justify-center bg-white z-50 ${roboto.className}`}
    >
      {/* <Spin
        indicator={
          <LoadingOutlined
            style={{ fontSize: 48, color: "var(--primary-color)" }}
            spin
          />
        }
      /> */}
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      <p className="mt-6 text-lg font-medium text-gray-700">{message}</p>
      <p className="mt-2 text-sm text-gray-500">
        Please wait for a while
      </p>
    </div>
  );
};

export default TabLoader;
