import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { playlistShowOptions } from "@/src/libs/constant";
import { Poppins } from "next/font/google";
import Image from "next/image";
import { useFilterContext } from "@/src/context/filter.context";
import SlideshowIcon from '@mui/icons-material/Slideshow';

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const PlaylistType = () => {
  const [open, setOpen] = useState(false);
  const { playlistShow, setPlaylistShow } = useFilterContext();

  const handleChange = (value: number) => {
    setPlaylistShow(value);
    setOpen(false);
  };

  // Get the label of the currently selected sort option
  const getSelectedShowLabel = () => {
    const selectedShow = playlistShowOptions.find((item) => item.value === playlistShow);
    return selectedShow ? selectedShow.label : "";
  };

  const content = (
    <div className={`!w-[108px] p-1 ${poppins.className}`}>
      <div className="mx-auto flex flex-col gap-[2px]">
        {playlistShowOptions.map((item: any) => (
          <div
            key={item.value}
            className="w-full flex items-center cursor-pointer"
            onClick={() => handleChange(item.value)}
          >
            <h2
              className={`w-full text-[13px] px-3 py-2 leading-5 font-[400] rounded-md text-text-primary ${
                playlistShow === item.value
                  ? "bg-primary-light !text-text"
                  : "hover:bg-gray-100"
              }`}
            >
              {item.label}
            </h2>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`flex flex-col gap-1`}>
      <h2 className="text-[11px] leading-4 md:pl-[2px] font-[500] text-text-primary md:block hidden">
        Show
      </h2>
      <Popover
        placement="topRight"
        title={
          <h2 className="text-[16px] leading-4 font-[500] text-text-primary max-[768px]:block hidden px-3 pt-3">
            Show
          </h2>
        }
        content={content}
        arrow={false}
        trigger={["click"]}
        open={open}
        onOpenChange={setOpen}
        rootClassName="w-[108px]"
      >
        <Button
          className={`h-[32px] md:w-[108px] flex gap-2 items-center justify-between pt-[2px] px-1 md:px-3 text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[12px] hover:!border-primary cursor-pointer transition-all ${
            open && "!border-primary"
          }`}
        >
          <h2
            className={`text-[13px] leading-5 font-[400] text-text-primary hidden md:block`}
          >
            {getSelectedShowLabel()}
          </h2>
          <Image
            src="/images/helperComponents/arrow.svg"
            width={16}
            height={16}
            alt=""
            className={`cursor-pointer transform transition-transform duration-300 ${
              open ? "rotate-180" : "rotate-0"
            } hidden md:block`}
          />
          <div className={`max-[768px]:block hidden !text-text-primary`}>
            <SlideshowIcon className="text-[18px]" />
          </div>
        </Button>
      </Popover>
    </div>
  );
};

export default PlaylistType;
