"use client";
import React, { useEffect } from "react";
import { Modal } from "antd";
import { Inter, Poppins } from "next/font/google";

const inter = Inter({ weight: ["300", "400", "500", "600", "700"], subsets: ["latin"] });
const poppins = Poppins({ weight: ["300", "400", "500", "600", "700"], subsets: ["latin"] });

const LectureInfoModal = ({ isModalOpen, setIsModalOpen, lectureData }: any) => {

  // useEffect(() => {
  //   console.log('lectureData', lectureData)
  // }, [isModalOpen])

  return (
    <Modal
      centered
      open={isModalOpen}
      onCancel={() => setIsModalOpen(false)}
      footer={null}
      width={380}
    >
      <h1
        className={`w-full text-[24px] mt-4 leading-[22.4px] font-bold ${inter.className}`}
      >
        Lecture info
      </h1>
      <div className={`mt-6 mb-2 ${poppins.className}`}>
        <div className="flex flex-col gap-2">
          {Array.isArray(lectureData?.category) && lectureData?.category?.length > 0 && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Category:</span>
              <span>{lectureData?.category.join(", ")}</span>
            </p>
          )}
          {lectureData?.language?.main && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Main language:</span>
              <span>{lectureData?.language?.main}</span>
            </p>
          )}
          {lectureData?.legacyData?.lectureCode && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Lecture code:</span>
              <span>{lectureData?.legacyData?.lectureCode}</span>
            </p>
          )}
          {lectureData?.legacyData?.wpId && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">WP ID:</span>
              <span>{lectureData?.legacyData?.wpId}</span>
            </p>
          )}
          {lectureData?.location?.country && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Country:</span>
              <span>{lectureData?.location?.country}</span>
            </p>
          )}
          {Array.isArray(lectureData?.place) && lectureData?.place?.length > 0 && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Place:</span>
              <span>{lectureData?.place.join(", ")}</span>
            </p>
          )}
          {lectureData?.lengthType && (
            <p className="flex">
              <span className="font-[600] min-w-[140px]">Length Type:</span>
              <span>{lectureData?.lengthType}</span>
            </p>
          )}
          {/* {lectureData?.transcription && (
            <p className="flex flex-col">
              <span className="font-[600] min-w-[140px] mb-1">Transcription Excerpts:</span>
              <div className="max-h-[150px] overflow-y-auto scrollbar-mini pl-2 text-sm">
                {Array.isArray(lectureData.transcription) ?
                  lectureData.transcription.map((excerpt: string, index: number) => (
                    <p key={index} className="mb-2" dangerouslySetInnerHTML={{ __html: excerpt }} />
                  )) :
                  <p>{lectureData.transcription}</p>
                }
              </div>
            </p>
          )} */}
        </div>
      </div>
    </Modal>
  );
};

export default LectureInfoModal;
