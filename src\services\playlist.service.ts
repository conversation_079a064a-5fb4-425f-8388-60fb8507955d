import {
    arrayRemove,
    arrayUnion,
    collection,
    doc,
    getDocs,
    increment,
    query,
    setDoc,
    updateDoc,
    where,
    deleteDoc,
} from "firebase/firestore";
import { db } from "../config/firebase.config";
import { applyFilters, applySorting } from "../utils/helperFunctions";
import { getAllLectures } from "./indexedDB.service";

export const fetchAllPublicPlaylists = async () => {
    try {
        const playlistsRef = collection(db, "PublicPlaylists");
        const querySnapshot = await getDocs(playlistsRef);
        const playlists = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
        return playlists;
    } catch (error) {
        console.error("Error fetching public playlist: ", error);
        throw new Error("We couldn't fetch playlist at this moment.");
    }
};

export const fetchUserPublicPlaylists = async () => {
    try {
        const email = localStorage.getItem("email");

        const playlistsRef = collection(db, "PublicPlaylists");
        const q = query(playlistsRef, where("authorEmail", "==", email));
        const querySnapshot = await getDocs(q);
        const playlists = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
        return playlists;
    } catch (error) {
        console.error("Error fetching public playlist: ", error);
        throw new Error("We couldn't fetch playlist at this moment.");
    }
};

export const fetchUserPrivatePlaylists = async () => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        const email = localStorage.getItem("email");

        if (!userId || !email) {
            throw new Error("User ID or email is missing from localStorage.");
        }

        const playlistsRef = collection(
            db,
            `PrivatePlaylists/${userId}/${email}`
        );
        const querySnapshot = await getDocs(playlistsRef);
        const playlists = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            ...doc.data(),
        }));
        return playlists;
    } catch (error) {
        console.error("Error fetching public playlist: ", error);
        throw new Error("We couldn't fetch playlist at this moment.");
    }
};

export const addToPublicPlaylist = async (playlistId: string, payload: any) => {
    try {
        const playlistRef = doc(db, "PublicPlaylists", playlistId);

        await updateDoc(playlistRef, payload);

        return true;
    } catch (error) {
        console.error("Error adding lectures in to playlist: ", error);
        throw new Error("We couldn't add lectures to playlist at this moment.");
    }
};

export const addToPrivatePlaylist = async (
    playlistId: string,
    payload: any
) => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        const email = localStorage.getItem("email");

        if (!userId || !email) {
            throw new Error("User ID or email is missing from localStorage.");
        }

        const playlistRef = doc(
            db,
            `PrivatePlaylists/${userId}/${email}`,
            playlistId
        );

        await updateDoc(playlistRef, payload);

        return true;
    } catch (error) {
        console.error("Error adding lectures in to playlist: ", error);
        throw new Error("We couldn't add lectures to playlist at this moment.");
    }
};

export const createPlaylist = async (type: string, payload: any) => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        const email = localStorage.getItem("email");

        if (!userId || !email) {
            throw new Error("User ID or email is missing from localStorage.");
        }

        const isPublic = type === "PUBLIC";
        const collectionPath = isPublic
            ? "PublicPlaylists"
            : `PrivatePlaylists/${userId}/${email}`;

        const collectionRef = collection(db, collectionPath);

        const newDocRef = doc(collectionRef);

        payload = {
            ...payload,
            authorEmail: email,
            creationTime: Date.now(),
            lastUpdate: Date.now(),
            listID: newDocRef.id,
            docPath: newDocRef.path,
        };

        await setDoc(newDocRef, payload);

        return true;
    } catch (error) {
        console.error("Error creating playlist: ", error);
        throw new Error("We couldn't create the playlist at this moment.");
    }
};

export const editPlaylist = async (
    type: string,
    listID: string,
    updatedFields: {
        title?: string;
        lecturesCategory?: string;
        discription?: string;
    }
) => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        const email = localStorage.getItem("email");

        if (!userId || !email) {
            throw new Error("User ID or email is missing from localStorage.");
        }

        const isPublic = type === "PUBLIC";
        const docPath = isPublic
            ? `PublicPlaylists/${listID}`
            : `PrivatePlaylists/${userId}/${email}/${listID}`;

        const playlistDocRef = doc(db, docPath);

        await updateDoc(playlistDocRef, {
            ...updatedFields,
            lastUpdate: Date.now(),
        });

        return true;
    } catch (error) {
        console.error("Error editing playlist: ", error);
        throw new Error("We couldn't update the playlist at this moment.");
    }
};

export const fetchPlaylists = async (
    type: number,
    sortOption: number,
    searchQuery: string
) => {
    try {
        let playlists: any[] = [];

        switch (type) {
            case 1:
                playlists = [
                    ...(await fetchUserPrivatePlaylists()),
                    ...(await fetchAllPublicPlaylists()),
                ];
                break;
            case 2:
                playlists = await fetchUserPrivatePlaylists();
                break;
            case 3:
                playlists = await fetchUserPublicPlaylists();
                break;
            default:
                playlists = await fetchAllPublicPlaylists();
        }

        if (searchQuery) {
            playlists = await applySearchToPlaylists(playlists, searchQuery);
        }

        return applyPlaylistSort(playlists, sortOption);
    } catch (error) {
        console.error("Error fetching playlists: ", error);
        throw new Error("We couldn't fetch playlists at this moment.");
    }
};

export const applySearchToPlaylists = async (
    playlists: any[],
    searchQuery: string
) => {
    if (!searchQuery.trim()) return playlists;

    return playlists.filter((playlist: any) =>
        playlist.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
};

export const applyPlaylistSort = (playlists: any[], sortBy: number) => {
    switch (sortBy) {
        case 1:
            return playlists;
        case 2:
            return [...playlists].sort((a, b) =>
                (a.title || "").localeCompare(b.title || "")
            );
        case 3:
            return [...playlists].sort((a, b) =>
                (b.title || "").localeCompare(a.title || "")
            );
        default:
            return playlists;
    }
};

export interface FetchPlaylistLectureParams {
    searchQuery?: string;
    sortBy?: number;
    allFilter?: { [key: string]: string[] };
    playbackMode?: number;
    lectureIds?: number[];
}

// Helper function to apply search filtering
const applySearchFilter = (lectures: any[], searchQuery: string) => {
    if (!searchQuery.trim()) return lectures;

    // Convert search query to lowercase for case-insensitive comparison
    const query = searchQuery.toLowerCase().trim();

    // Split the query into words for full name search
    const queryWords = query
        .split(/\s+/)
        .filter((word: string) => word.length > 0);

    // Filter lectures by comparing search query with title array elements
    return lectures.filter((lecture: any) => {
        // Check if title exists and is an array
        if (!lecture.title || !Array.isArray(lecture.title)) return false;

        // Check for exact match first (highest priority)
        if (
            lecture.title.some(
                (titlePart: string) => titlePart.toLowerCase() === query
            )
        ) {
            return true;
        }

        // Check if any element in the title array contains the full search query
        if (
            lecture.title.some((titlePart: string) =>
                titlePart.toLowerCase().includes(query)
            )
        ) {
            return true;
        }

        // If the query has multiple words, check if all words appear in any title part
        if (queryWords.length > 1) {
            // Join all title parts into a single string for multi-word search
            const fullTitle = lecture.title.join(" ").toLowerCase();

            // Check if all query words are present in the full title
            const allWordsPresent = queryWords.every((word: string) =>
                fullTitle.includes(word)
            );

            if (allWordsPresent) {
                return true;
            }
        }

        return false;
    });
};

/**
 * Fetch and process favorite lectures based on provided parameters
 * @param params - Object containing search query, sort option, and filter values
 * @returns Promise<any[]> - Array of processed favorite lectures
 */
export const fetchPlaylistLectures = async (
    params: FetchPlaylistLectureParams = {}
): Promise<any[]> => {
    const {
        searchQuery = "",
        sortBy = 1,
        allFilter = {},
        playbackMode = 1,
        lectureIds = [],
    } = params;

    try {
        // Get all lectures from IndexedDB
        const allLectures = await getAllLectures();

        // Filter to get only favorite lectures
        let playlistLectures = allLectures.filter((lecture: any) =>
            lectureIds.includes(lecture.id)
        );

        if (playlistLectures.length === 0) {
            return [];
        }

        // Apply search filtering if search query is provided
        if (searchQuery.trim()) {
            playlistLectures = applySearchFilter(playlistLectures, searchQuery);
        }

        // Apply filters (including playback mode)
        const hasActiveFilters = Object.values(allFilter).some(
            (filterValues: any) =>
                Array.isArray(filterValues) && filterValues.length > 0
        );

        if (hasActiveFilters || playbackMode !== 1) {
            playlistLectures = applyFilters(
                playlistLectures,
                allFilter,
                playbackMode
            );
        }

        // Apply sorting
        if (sortBy !== 1) {
            playlistLectures = applySorting(playlistLectures, sortBy);
        }

        console.log(`Processed ${playlistLectures.length} playlist lectures`);
        return playlistLectures;
    } catch (error) {
        console.error("Error in fetchFavorites:", error);
        throw error;
    }
};

/**
 * Deletes a playlist from Firestore.
 * @param type 'public' | 'private'
 * @param playlistId The ID of the playlist document
 */
export const deletePlaylist = async (
    type: "Public" | "Private",
    playlistId: string
) => {
    try {
        if (!playlistId) {
            throw new Error("Playlist ID is required.");
        }

        const email = localStorage.getItem("email");
        const userId = localStorage.getItem("firebaseUid");

        if (!email) {
            throw new Error("Email is missing from localStorage.");
        }

        let docRef;

        if (type === "Public") {
            docRef = doc(db, "PublicPlaylists", playlistId);
        } else if (type === "Private") {
            if (!userId) {
                throw new Error("User ID is missing from localStorage.");
            }
            docRef = doc(db, `PrivatePlaylists/${userId}/${email}`, playlistId);
        } else {
            throw new Error("Invalid playlist type.");
        }

        await deleteDoc(docRef);
        console.log(`Deleted ${type} playlist with ID: ${playlistId}`);
    } catch (error) {
        console.error("Error deleting playlist:", error);
        throw new Error("Failed to delete playlist. Please try again.");
    }
};

export const removeFromPublicPlaylist = async (playlistId: string, lectureId: any) => {
    try {
        const playlistRef = doc(db, "PublicPlaylists", playlistId);
        await updateDoc(playlistRef, {
            lectureIds: arrayRemove(lectureId),
            lectureCount: increment(-1),
        });
        return true;
    } catch (error) {
        console.error("Error removing lecture from public playlist: ", error);
        throw new Error("We couldn't remove the lecture from the playlist at the moment.");
    }
};

export const removeFromPrivatePlaylist = async (playlistId: string, lectureId: any) => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        const email = localStorage.getItem("email");

        if (!userId || !email) {
            throw new Error("User ID or email is missing from localStorage.");
        }

        const playlistRef = doc(db, `PrivatePlaylists/${userId}/${email}`, playlistId);
        await updateDoc(playlistRef, {
            lectureIds: arrayRemove(lectureId),
            lectureCount: increment(-1),
        });
        return true;
    } catch (error) {
        console.error("Error removing lecture from private playlist: ", error);
        throw new Error("We couldn't remove the lecture from the playlist at this moment.");
    }
};
