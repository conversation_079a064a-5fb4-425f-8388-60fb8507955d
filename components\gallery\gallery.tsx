"use client";
import React from "react";
import Image from "next/image";
import { Poppins } from "next/font/google";
import { useRouter } from "next/navigation";
import { FaArrowRight } from "react-icons/fa";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const dummyAlbums = [
  {
    id: "1",
    title: "Srirangam Sravanam Kirtanam Camp",
    date: "January 30, 2019",
    coverPhotoUrl: "/images/about/about2-1.png",
    photoCount: 28,
  },
  {
    id: "2",
    title: "Vrindavan <PERSON>",
    date: "February 12, 2020",
    coverPhotoUrl: "/images/about/about2-2.png",
    photoCount: 42,
  },
  {
    id: "3",
    title: "Bhakti Retreat 2021",
    date: "March 5, 2021",
    coverPhotoUrl: "/images/about/about2-3.png",
    photoCount: 35,
  },
  {
    id: "4",
    title: "Kirtan Mela",
    date: "April 10, 2022",
    coverPhotoUrl: "/images/about/about2-4.png",
    photoCount: 20,
  },
  {
    id: "5",
    title: "Mayapur Festival",
    date: "May 21, 2022",
    coverPhotoUrl: "/images/about/about2-1.png",
    photoCount: 50,
  },
  {
    id: "6",
    title: "Govardhan Parikrama",
    date: "June 18, 2023",
    coverPhotoUrl: "/images/about/about2-2.png",
    photoCount: 31,
  },
];

const Gallery = () => {
  const router = useRouter();

  const handleClick = (id:any) => {
    router.push(`/gallery/photos/${id}`);
  };

  return (
    <div className={`w-full h-full ${poppins.className} tracking-normal`}>
      {/* Header */}
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b">
        <h1 className="text-[24px] md:text-[28px] font-[600] leading-8">
          Gallery
        </h1>
      </div>

      {/* Grid */}
      <div
        id="scrollableDiv"
        className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] py-6 px-8 overflow-y-auto scrollbar"
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {dummyAlbums.map((album) => (
            <div
              key={album.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 flex flex-col hover:shadow-lg transition p-4"
            >
              <div className="relative w-full h-[180px] md:h-[200px] rounded-lg overflow-hidden">
                <Image
                  src={album.coverPhotoUrl}
                  alt={album.title}
                  fill
                  className="object-cover rounded-t-lg"
                />
              </div>
              <div className="flex flex-col pt-2">
                <p className="text-[13px] text-gray-500 mb-1">{album.date}</p>
                <p className="text-[15px] md:text-[16px] font-[500] mb-4">
                  {album.title}
                </p>
                <button
                  style={{
                    backgroundColor: `var(--accent-color)`,
                  }}
                  onClick={() => handleClick(album.id)}
                  className="flex items-center justify-between w-full px-3 py-2 border border-gray-300 rounded-md text-[14px] hover:bg-gray-50 transition "
                >
                  See the photos ({album.photoCount})
                  <FaArrowRight className="ml-2 text-primary" size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Gallery;
