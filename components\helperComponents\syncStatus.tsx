"use client";

import React from 'react';
import { But<PERSON>, Tooltip } from 'antd';
import { SyncOutlined } from '@ant-design/icons';
import { useIndexedDBContext } from '@/src/context/indexedDB.context';

const SyncStatus = () => {
  const { lastSyncTime, totalLectures, syncData, isSyncing } = useIndexedDBContext();

  // Format the last sync time
  const formatLastSync = () => {
    if (!lastSyncTime) return 'Never synced';
    
    const date = new Date(lastSyncTime);
    return `Last synced: ${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  return (
    <div className="flex items-center gap-2">
      <Tooltip title={formatLastSync()}>
        <Button 
          icon={<SyncOutlined spin={isSyncing} />} 
          onClick={() => syncData()}
          loading={isSyncing}
          disabled={isSyncing}
        >
          {isSyncing ? 'Syncing...' : 'Sync'}
        </Button>
      </Tooltip>
      {lastSyncTime && (
        <span className="text-xs text-gray-500">
          {totalLectures} lectures
        </span>
      )}
    </div>
  );
};

export default SyncStatus;
