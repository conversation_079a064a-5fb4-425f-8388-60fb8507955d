"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import { Inter } from "next/font/google";
import { AiOutlineDelete } from "react-icons/ai";

const inter = Inter({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

const DeletePlaylistModal = ({
    isModalOpen,
    setIsModalOpen,
    playlistData,
    onDelete,
}: any) => {
    const [loading, setLoading] = useState(false);

    const handleDelete = async () => {
        try {
            setLoading(true);
            await onDelete(playlistData?.listType, playlistData?.listID);
            setIsModalOpen(false);
            message.success("Playlist deleted successfully");
        } catch (err) {
            message.error("Failed to delete playlist. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            centered
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            footer={null}
            width={400}
        >
            <div className="flex flex-col items-center justify-center mt-4 mb-2">
                {/* <AiOutlineDelete className="text-red-600 text-5xl mb-3" /> */}
                <img
                    src="/images/ui/delete.svg"
                    alt="Description of image"
                    width="60"
                    height="60"
                />
                <h1
                    className={`text-[22px] font-bold text-center mt-2 ${inter.className}`}
                >
                    Delete Playlist?
                </h1>

                <p
                    className={`text-[16px] mt-2 text-center ${inter.className}`}
                >
                    Are you sure you want to delete the{" "}
                    <strong>{playlistData?.title}</strong> playlist?
                </p>
            </div>

            <div className="flex justify-center gap-4 mt-6">
                <Button
                    onClick={() => setIsModalOpen(false)}
                    className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
                    style={{
                        background: `#d9d9d9`,
                        color: "#000",
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleDelete}
                    loading={loading}
                    className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
                    style={{
                        background: "#ff4d4f", // red
                        color: "white",
                    }}
                >
                    Delete
                </Button>
            </div>
        </Modal>
    );
};

export default DeletePlaylistModal;
