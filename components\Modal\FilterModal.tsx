"use client";
import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { motion, AnimatePresence } from "framer-motion";

interface ModalProps {
    isOpen: boolean;
    onClose: () => void;
    title: React.ReactNode;
    children: React.ReactNode;
}

const FilterModal: React.FC<ModalProps> = ({
    isOpen,
    onClose,
    title,
    children,
}) => {
    const [mounted, setMounted] = useState(false);

    // Handle escape key press to close modal
    useEffect(() => {
        const handleEscapeKey = (e: KeyboardEvent) => {
            if (e.key === "Escape" && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.body.style.overflow = "hidden"; // Prevent scrolling when modal is open
            document.addEventListener("keydown", handleEscapeKey);
        }

        return () => {
            document.body.style.overflow = ""; // Restore scrolling when modal is closed
            document.removeEventListener("keydown", handleEscapeKey);
        };
    }, [isOpen, onClose]);

    // Handle client-side rendering
    useEffect(() => {
        setMounted(true);
        return () => setMounted(false);
    }, []);

    // Modal animations
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 },
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.9, y: -20 },
        visible: {
            opacity: 1,
            scale: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 300,
                damping: 25,
            },
        },
        exit: {
            opacity: 0,
            scale: 0.9,
            y: -20,
            transition: {
                duration: 0.2,
            },
        },
    };

    if (!mounted) return null;

    return createPortal(
        <AnimatePresence>
            {isOpen && (
                <div className="fixed inset-0 z-50 flex items-center justify-center">
                    {/* Backdrop */}
                    <motion.div
                        className="fixed inset-0 bg-black/50"
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        variants={backdropVariants}
                        onClick={onClose}
                    />

                    {/* Modal */}
                    <motion.div
                        className="relative z-10 bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        variants={modalVariants}
                    >
                        {/* Header */}
                        <div className="px-4 py-3 border-b">
                            <div className="flex items-center justify-between">
                                <div className="text-lg font-medium">
                                    {title}
                                </div>
                                <button
                                    type="button"
                                    aria-label="Close"
                                    onClick={onClose}
                                    className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-5 w-5 text-gray-500"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M6 18L18 6M6 6l12 12"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        {/* Content */}
                        <div>{children}</div>
                    </motion.div>
                </div>
            )}
        </AnimatePresence>,
        document.body
    );
};

export default FilterModal;
