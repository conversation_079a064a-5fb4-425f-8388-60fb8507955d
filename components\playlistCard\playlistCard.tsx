import Image from "next/image";
import React, { useState } from "react";
import { Poppins } from "next/font/google";
import { Dropdown } from "antd";
import { formatTimestampToDDMMYYYY } from "@/src/utils/timeFormat";
import appConfig from "@/src/config/apps";
import { useRouter } from "next/navigation";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { BiAddToQueue } from "react-icons/bi";
import { MdOutlineQueueMusic } from "react-icons/md";
import { AiOutlineDelete } from "react-icons/ai";
import { deletePlaylist } from "@/src/services/playlist.service";
import DetetePlayListModal from "./deletePlaylistModal";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const PlaylistCard = ({
    key,
    playlistData,
    setDisplayedPlaylists,
    setAllPlaylists,
}: any) => {
    const router = useRouter();
    const { setIsTabChangeLoading } = useSidebarContext();
    const [imageLoaded, setImageLoaded] = useState(false);
    const [isDeletePlaylistModalOpen, setIsDeletePlaylistModalOpen] =
        useState(false);
    const currentUserEmail = localStorage.getItem("email");

    const handleDeletePlaylist = async (
        listType: "Public" | "Private",
        listID: string
    ) => {
        try {
            await deletePlaylist(listType, listID);
            // Update UI states
            setDisplayedPlaylists((prev: any) =>
                prev.filter((p: any) => p.listID !== listID)
            );
            setAllPlaylists((prev: any) =>
                prev.filter((p: any) => p.listID !== listID)
            );
            console.log(
                `${listType} playlist with ID ${listID} deleted successfully`
            );
        } catch (error) {
            console.error("Failed to delete playlist:", error);
        }
    };

    const items = [
        // {
        //     key: "1",
        //     label: (
        //         <div className="flex gap-2 items-center py-1">
        //             <MdOutlineQueueMusic className="w-[20px] h-[20px]" />
        //             <h2
        //                 className={`text-[14px] leading-5 font-[500] text-text-primary ${poppins.className}`}
        //             >
        //                 Play Next
        //             </h2>
        //         </div>
        //     ),
        // },
        // {
        //     key: "2",
        //     label: (
        //         <div className="flex gap-2 items-center py-1">
        //             <BiAddToQueue className="w-[20px] h-[20px]" />
        //             <h2
        //                 className={`text-[14px] leading-5 font-[500] text-text-primary ${poppins.className}`}
        //             >
        //                 Add to Queue
        //             </h2>
        //         </div>
        //     ),
        // },
        ...(playlistData?.authorEmail === currentUserEmail
            ? [
                  {
                      key: "3",
                      label: (
                          <div
                              className="flex gap-2 items-center py-1"
                              onClick={() => setIsDeletePlaylistModalOpen(true)}
                          >
                              <AiOutlineDelete className="w-[20px] h-[20px]" />
                              <h2
                                  className={`text-[14px] leading-5 font-[500] text-text-primary ${poppins.className}`}
                              >
                                  Delete playlist
                              </h2>
                          </div>
                      ),
                  },
              ]
            : []),
    ];

    return (
        <>
            <div
                className="cursor-pointer"
                onClick={() => {
                    setIsTabChangeLoading(true);
                    const data = {
                        title: playlistData?.title,
                        category: playlistData?.lecturesCategory,
                        description: playlistData?.discription,
                        creationTime: formatTimestampToDDMMYYYY(
                            playlistData?.creationTime
                        ),
                        lectureCount: playlistData?.lectureCount,
                        lectureIds: playlistData?.lectureIds,
                        listID: playlistData?.listID,
                        listType: playlistData?.listType,
                    };
                    router.push(
                        `/playlists/${
                            playlistData?.id
                        }?data=${encodeURIComponent(JSON.stringify(data))}`
                    );
                }}
            >
                <div className={`w-full transition-all duration-500 relative`}>
                    <div className="relative">
                        {/* Show skeleton until image loads */}
                        {/* <div
                      className="absolute inset-0 z-10 transition-opacity duration-300"
                      style={{ opacity: imageLoaded ? 0 : 1 }}
                    >
                      <Skeleton height={155} width="100%" borderRadius={6} />
                    </div> */}
                        <img
                            src={
                                playlistData?.thumbnail ||
                                appConfig.defaultPlaylistThumbnail
                            }
                            width={400}
                            height={300}
                            alt=""
                            className={`w-full h-auto rounded-[6px] object-cover aspect-[16/9]`}
                            loading="lazy"
                            onLoad={() => setImageLoaded(true)}
                            onError={(e) => {
                                setImageLoaded(true);
                                e.currentTarget.src =
                                    appConfig.defaultPlaylistThumbnail;
                            }}
                        />
                    </div>
                    <div className="flex w-full justify-end px-2 absolute bottom-2">
                        {/* <p className="px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]">
                      0%
                    </p> */}
                        {/* <p className="px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]">
                      <CheckOutlined /> Completed
                    </p> */}
                        <p className="px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]">
                            {playlistData?.lectureCount} lectures
                        </p>
                    </div>
                </div>
                <div className="flex gap-2">
                    <div className="w-[calc(100%-26px)] flex flex-col gap-1 py-2">
                        <div className="flex gap-1">
                            <Image
                                src={
                                    playlistData?.listType === "Public"
                                        ? "/images/ui/IconPublic.svg"
                                        : "/images/ui/IconPrivate.svg"
                                }
                                width={18}
                                height={18}
                                alt=""
                                className={`w-[18px] h-[18px] relative top-[2px]`}
                            />
                            <h1
                                className={`text-[15px] leading-6 font-[600] text-text w-full truncate ${poppins.className}`}
                            >
                                {playlistData?.title}
                            </h1>
                        </div>
                        <p
                            className={`text-[13px] leading-5 font-[400] text-text-primary w-full truncate ${poppins.className}`}
                        >
                            by {playlistData?.authorEmail}
                        </p>
                        <p
                            className={`text-[13px] leading-5 font-[400] text-text-primary ${poppins.className}`}
                        >
                            {formatTimestampToDDMMYYYY(
                                playlistData?.creationTime
                            )}{" "}
                            • {playlistData?.lecturesCategory}
                        </p>
                    </div>
                    {playlistData?.authorEmail === currentUserEmail && (
                        <div
                            className="pt-4 cursor-pointer"
                            onClick={(e: any) => e.stopPropagation()}
                        >
                            <Dropdown
                                menu={{ items }}
                                trigger={["click"]}
                                placement="bottomRight"
                            >
                                <Image
                                    src="/images/ui/menuDot.svg"
                                    width={18}
                                    height={18}
                                    alt=""
                                    className={`w-[18px] h-[18px] relative left-1`}
                                />
                            </Dropdown>
                        </div>
                    )}
                </div>
            </div>

            <DetetePlayListModal
                isModalOpen={isDeletePlaylistModalOpen}
                setIsModalOpen={setIsDeletePlaylistModalOpen}
                playlistData={playlistData}
                onDelete={handleDeletePlaylist}
            />
        </>
    );
};

export default PlaylistCard;
