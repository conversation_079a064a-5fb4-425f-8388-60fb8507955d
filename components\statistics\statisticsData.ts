// Types for statistics data
export interface CategoryData {
    name: string;
    fullName: string;
    hours: number;
    seconds?: number; // Optional seconds field for h m s formatting
    color: string;
}

export interface ProgressData {
    percentage: number;
    lecturesListened: number;
    streak: number;
    totalListened: string;
}

export interface ChartDataPoint {
    day: string;
    date: string | number;
    hours: number;
}

export interface StatisticsTimeData {
    progress: ProgressData;
    chartData: ChartDataPoint[];
    categories: CategoryData[];
}

export interface ProgressPeriodData {
    totalListened: string;
    categories: CategoryData[];
}

// Available tabs for time periods
export const tabs = ["Year", "Week", "Month"];

// Interface for Firebase listenInfo document
export interface FirebaseListenInfo {
    audioListen: number;
    creationTimestamp: number;
    date: string;
    dateOfRecord: {
        day: number;
        month: number;
        year: number;
    };
    documentId: string;
    documentPath: string;
    lastModifiedTimestamp: number;
    listenDetails: {
        // Support both uppercase and lowercase keys
        BG?: number;
        CC?: number;
        SB?: number;
        Seminars?: number;
        VSN?: number;
        others?: number;
        // Lowercase variants
        bg?: number;
        cc?: number;
        sb?: number;
        seminars?: number;
        vsn?: number;
    };
    playedBy: string[];
    playedIds: number[];
    videoListen: number;
}
