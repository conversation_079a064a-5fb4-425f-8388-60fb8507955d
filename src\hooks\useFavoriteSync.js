"use client";
import { useEffect, useCallback } from 'react';

// Global event emitter for favorite changes
class FavoriteEventEmitter {
    constructor() {
        this.listeners = new Map();
    }

    // Subscribe to favorite changes for a specific lecture ID
    subscribe(lectureId, callback) {
        if (!this.listeners.has(lectureId)) {
            this.listeners.set(lectureId, new Set());
        }
        this.listeners.get(lectureId).add(callback);

        // Return unsubscribe function
        return () => {
            const callbacks = this.listeners.get(lectureId);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.listeners.delete(lectureId);
                }
            }
        };
    }

    // Subscribe to all favorite changes
    subscribeToAll(callback) {
        const allListenersKey = '__ALL__';
        if (!this.listeners.has(allListenersKey)) {
            this.listeners.set(allListenersKey, new Set());
        }
        this.listeners.get(allListenersKey).add(callback);

        // Return unsubscribe function
        return () => {
            const callbacks = this.listeners.get(allListenersKey);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.listeners.delete(allListenersKey);
                }
            }
        };
    }

    // Emit favorite change event
    emit(lectureId, isFavorite) {
        // Notify specific lecture listeners
        const specificCallbacks = this.listeners.get(lectureId);
        if (specificCallbacks) {
            specificCallbacks.forEach(callback => {
                try {
                    callback(lectureId, isFavorite);
                } catch (error) {
                    console.error('Error in favorite change callback:', error);
                }
            });
        }

        // Notify global listeners
        const allCallbacks = this.listeners.get('__ALL__');
        if (allCallbacks) {
            allCallbacks.forEach(callback => {
                try {
                    callback(lectureId, isFavorite);
                } catch (error) {
                    console.error('Error in global favorite change callback:', error);
                }
            });
        }
    }
}

// Global instance
const favoriteEventEmitter = new FavoriteEventEmitter();

// Custom hook for listening to favorite changes for a specific lecture
export const useFavoriteSync = (lectureId, onFavoriteChange) => {
    useEffect(() => {
        if (!lectureId || !onFavoriteChange) return;

        const unsubscribe = favoriteEventEmitter.subscribe(lectureId, onFavoriteChange);
        return unsubscribe;
    }, [lectureId, onFavoriteChange]);
};

// Custom hook for listening to all favorite changes
export const useFavoriteSyncAll = (onFavoriteChange) => {
    useEffect(() => {
        if (!onFavoriteChange) return;

        const unsubscribe = favoriteEventEmitter.subscribeToAll(onFavoriteChange);
        return unsubscribe;
    }, [onFavoriteChange]);
};

// Function to broadcast favorite changes
export const broadcastFavoriteChange = (lectureId, isFavorite) => {
    favoriteEventEmitter.emit(lectureId, isFavorite);
};

// Custom hook for broadcasting favorite changes
export const useFavoriteBroadcast = () => {
    return useCallback((lectureId, isFavorite) => {
        broadcastFavoriteChange(lectureId, isFavorite);
    }, []);
};

export default favoriteEventEmitter;
