"use client";
import React, { useState, useEffect, useRef } from "react";
import { Select } from "antd";
import { Poppins } from "next/font/google";
import Image from "next/image";
import { FaFire } from "react-icons/fa";
import { HiInformationCircle } from "react-icons/hi";
import CircularProgress from "./circularProgress";
import Bar<PERSON>hart from "./bar<PERSON>hart";
import StatisticsSkeleton from "./statisticsSkeleton";
import { tabs, StatisticsTimeData, ProgressPeriodData } from "./statisticsData";
import { fetchUserStatistics } from "../../src/api/userStatistics.api";
import { UserStatistics } from "../../src/services/userStatistics.service";
import {
  getStatisticsData,
  getProgressData,
  getTotalProgressData,
} from "../../src/services/statisticsDataProcessor.service";
import { formatCategoryTimeHMS } from "@/src/utils/chartTimeFormat";
import Skeleton from "react-loading-skeleton";
import InfoModal from "./infoModal";
import { formatToK } from "@/src/utils/helperFunctions";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

// Cache interface for storing data
interface DataCache {
  [key: string]: {
    data: StatisticsTimeData;
    timestamp: number;
  };
}

interface ProgressCache {
  [key: string]: {
    data: ProgressPeriodData;
    timestamp: number;
  };
}

const Statistics = () => {
  const [activeTab, setActiveTab] = useState("Week");
  const [progressPeriod, setProgressPeriod] = useState("All time");
  const [userStats, setUserStats] = useState<UserStatistics | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [statisticsData, setStatisticsData] =
    useState<StatisticsTimeData | null>(null);
  const [currentProgressData, setCurrentProgressData] =
    useState<ProgressPeriodData | null>(null);
  const [totalProgressData, setTotalProgressData] = useState<{
    percentage: number;
    lecturesListened: number;
  } | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [tabChangeLoader, setTabChangeLoader] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [screenWidth, setScreenWidth] = useState<number>(0);

  useEffect(() => {
    const updateWidth = () => setScreenWidth(window.innerWidth);
    updateWidth(); // set initial

    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  // Cache for storing data to avoid repeated API calls
  const dataCache = useRef<DataCache>({});
  const progressCache = useRef<ProgressCache>({});
  const totalProgressCache = useRef<{
    data: { percentage: number; lecturesListened: number };
    timestamp: number;
  } | null>(null);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Check if cached data is still valid
  const isCacheValid = (timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION;
  };

  // Load statistics data based on active tab with caching
  const loadStatisticsData = async (tab: string) => {
    try {
      // Check cache first
      const cachedData = dataCache.current[tab];

      if (cachedData && isCacheValid(cachedData.timestamp)) {
        // Only set the data if this is for the current active tab
        if (tab === activeTab) {
          setStatisticsData(cachedData.data);
          setIsLoadingData(false);
        }
        return;
      }

      setIsLoadingData(true);
      const data = await getStatisticsData(tab);

      // Cache the data
      dataCache.current[tab] = {
        data,
        timestamp: Date.now(),
      };

      // Only set the data if this is for the current active tab
      if (tab === activeTab) {
        setStatisticsData(data);
      }
    } catch (error) {
      console.error(`Error loading statistics data for tab ${tab}:`, error);
      if (tab === activeTab) {
        setStatisticsData(null); // Will fallback to sample data
      }
    } finally {
      if (tab === activeTab) {
        setIsLoadingData(false);
      }
    }
  };

  // Load progress data based on selected period with caching
  const loadProgressData = async (period: string) => {
    try {
      // Check cache first
      const cachedData = progressCache.current[period];
      if (cachedData && isCacheValid(cachedData.timestamp)) {
        setCurrentProgressData(cachedData.data);
        return;
      }

      const data = await getProgressData(period);

      // Cache the data
      progressCache.current[period] = {
        data,
        timestamp: Date.now(),
      };

      setCurrentProgressData(data);
    } catch (error) {
      console.error("Error loading progress data:", error);
      setCurrentProgressData(null); // Will fallback to sample data
    }
  };

  // Load total progress data (all-time percentage and lectures listened)
  const loadTotalProgressData = async () => {
    try {
      // Check cache first
      if (
        totalProgressCache.current &&
        isCacheValid(totalProgressCache.current.timestamp)
      ) {
        setTotalProgressData(totalProgressCache.current.data);
        return;
      }

      // Get all-time progress data
      const totalProgress = await getTotalProgressData();

      // Cache the data
      totalProgressCache.current = {
        data: totalProgress,
        timestamp: Date.now(),
      };

      setTotalProgressData(totalProgress);
    } catch (error) {
      console.error("Error loading total progress data:", error);
      setTotalProgressData(null);
    }
  };

  // Fetch user statistics on component mount
  useEffect(() => {
    const loadUserStatistics = async () => {
      try {
        setIsLoadingStats(true);
        const stats = await fetchUserStatistics();
        setUserStats(stats);
      } catch (error) {
        console.error("Error loading user statistics:", error);
      } finally {
        setIsLoadingStats(false);
      }
    };

    loadUserStatistics();
  }, []);

  // Load initial data and preload all tabs
  useEffect(() => {
    const loadInitialData = async () => {
      setIsInitialLoad(true);

      // Load current tab data first and wait for it to complete
      await loadStatisticsData(activeTab);
      await loadProgressData(progressPeriod);
      await loadTotalProgressData(); // Load total progress data

      setIsInitialLoad(false);

      // Preload other tabs in background after initial load is complete
      const otherTabs = tabs.filter((tab) => tab !== activeTab);
      otherTabs.forEach((tab) => {
        // Use setTimeout to ensure this happens after the current render cycle
        setTimeout(() => {
          loadStatisticsData(tab);
        }, 100);
      });
    };

    loadInitialData();
  }, []);

  // Load data when tab changes (will use cache if available)
  useEffect(() => {
    if (!isInitialLoad) {
      loadStatisticsData(activeTab);
    }
  }, [activeTab]);

  // Load data when progress period changes (will use cache if available)
  useEffect(() => {
    if (!isInitialLoad) {
      loadProgressData(progressPeriod);
    }
  }, [progressPeriod]);

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setTabChangeLoader(true);
    setActiveTab(tab);
    setTimeout(() => {
      setTabChangeLoader(false);
    }, 1000);
  };

  // Handle progress period change
  const handleProgressPeriodChange = (period: string) => {
    setProgressPeriod(period);
  };

  // Show skeleton during initial load
  if (isInitialLoad && isLoadingData) {
    return <StatisticsSkeleton />;
  }

  return (
    <div
      className={`w-full h-full bg-white ${poppins.className} tracking-normal`}
    >
      {/* Header */}
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Statistics
        </h1>
      </div>

      {/* Progress Section */}
      <div className="overflow-y-scroll w-full scrollbar h-[calc(100%-60px)] md:h-[calc(100%-84px)] pb-16">
        <div className="flex flex-col w-full xl:flex-row gap-5 p-4 md:p-6 xl:mb-[100px] mb-0">
          {/* Progress Info */}
          <div className="flex-1 w-full xl:w-[calc(100%-450px)]">
            <div className="flex justify-between items-center w-full mb-5">
              <div>
                <div className="flex flex-wrap items-baseline gap-2">
                  <span className="text-[20px] md:text-[22px] font-[600] text-[#343A40]">
                    Progress:
                  </span>
                  <span className="text-[24px] md:text-[24px] font-[700] text-[#F97316]">
                    {totalProgressData?.percentage ||
                      statisticsData?.progress?.percentage}
                    %
                  </span>
                  <span className="text-[16px] md:text-[22px] font-[600] text-[#343A40]">
                    of goal
                  </span>
                </div>
                <div className="text-[12px] md:text-[16px] text-[#9CA3AF]">
                  {totalProgressData?.lecturesListened ||
                    statisticsData?.progress?.lecturesListened}{" "}
                  lectures so far
                </div>
              </div>

              {/* Streak */}
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 md:w-10 md:h-10 rounded-full">
                  {/* <FaFire className="text-[#F59E0B] text-[14px] md:text-[18px]" />  */}
                  <Image
                    src="/images/ui/flame.svg"
                    alt="Śrīla Prabhupāda"
                    width={25}
                    height={40}
                    className="w-auto h-[50px]"
                  />
                </div>

                <div>
                  <div className="text-[16px] md:text-[18px] font-[600] text-[#343A40]">
                    {isLoadingStats
                      ? "..."
                      : userStats
                      ? `${userStats.currentStreak} day${userStats.currentStreak === 1 ? "" : "s"}`
                      : `${statisticsData?.progress?.streak} day${statisticsData?.progress?.streak === 1 ? "" : "s"}`}
                  </div>
                  <div className="text-[12px] md:text-[14px] text-[#9CA3AF]">
                    {` ${
                      userStats ? formatToK(userStats.bestStreak) : 0
                    } best streak`}
                    {/* Streak */}
                    {/* {userStats &&
                      userStats.bestStreak > userStats.currentStreak && (
                        <span className="ml-2 text-[10px] md:text-[12px] text-[#F97316]">
                          (Best: {userStats.bestStreak})
                        </span>
                      )} */}
                  </div>
                </div>
              </div>
            </div>

            <div
              className="bg-white rounded-2xl"
              // style={{
              //     boxShadow:
              //         "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
              // }}
            >
              {/* Time Period Tabs */}
              <div className="flex justify-center items-center">
                <div
                  className=" bg-white rounded-[10px] w-auto p-1 space-x-1 md:space-x-2 mb-3 mt-6"
                  style={{
                    boxShadow: "0px 0px 8px 0px rgba(0, 0, 0, 0.08)",
                  }}
                >
                  {tabs.map((tab) => (
                    <button
                      key={tab}
                      type="button"
                      onClick={() => handleTabChange(tab)}
                      className={`px-3 md:px-4 py-2 rounded-[10px] text-[12px] md:text-[14px] font-[500] transition-colors ${
                        activeTab === tab
                          ? "bg-[#F97316] text-white"
                          : "bg-white text-[#6C757D] hover:bg-[#F3F4F6]"
                      }`}
                    >
                      {tab}
                    </button>
                  ))}
                </div>
              </div>

              {/* Bar Chart */}
              {tabChangeLoader ? (
                <Skeleton
                  height={
                    window.innerWidth < 768 ? 180 : window.innerHeight - 401
                  }
                  borderRadius={10}
                />
              ) : (
                <div className="bg-white rounded-2xl">
                  {/* <div
                    className={`h-auto ${
                      activeTab === "Month"
                        ? "overflow-x-auto"
                        : "overflow-scroll"
                    } scrollbar`}
                  > */}
                  <BarChart
                    data={statisticsData?.chartData || []}
                    activeTab={activeTab}
                    height={
                      typeof window !== "undefined"
                        ? window.innerWidth < 768
                          ? 180
                          : window.innerHeight - 401
                        : 200
                    }
                  />
                  {/* </div> */}
                </div>
              )}
            </div>
          </div>

          {/* Progress Chart */}
          <div
            className="w-full xl:w-[450px] rounded-2xl p-4 h-auto"
            style={{
              boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
            }}
          >
            <div className="flex items-center justify-between mb-4">
              {/* <div className="flex items-center gap-2">
                  <HiInformationCircle className="text-[#9CA3AF] text-[14px] md:text-[24px]" />
                  <span className="text-[14px] md:text-[22px] font-[600] text-[#343A40]">
                      Progress:
                  </span>
              </div> */}
              <div className="flex items-center min-w-0 gap-1 flex-1 overflow-hidden">
                <HiInformationCircle
                  className="text-[#9CA3AF] text-[16px] sm:text-[18px] md:text-[24px] flex-shrink-0 cursor-pointer"
                  onClick={() => setIsModalOpen(true)}
                />
                <span className="text-[14px] md:text-[20px] font-[600] text-[#343A40]">
                  <div className="marquee ">
                    <span className="marquee-content">
                      Progress since you joined:
                    </span>
                  </div>
                </span>
              </div>
              <Select
                value={progressPeriod}
                onChange={handleProgressPeriodChange}
                className={`w-[90px] sm:w-[130px] text-[12px] sm:text-[16px] custome-selector ${poppins.className}`}
                popupClassName="custom-select-dropdown"
                size="small"
                options={[
                  { value: "All time", label: "All time" },
                  { value: "Last year", label: "Last year" },
                  {
                    value: "Last month",
                    label: "Last month",
                  },
                  { value: "Last week", label: "Last week" },
                ]}
                onDropdownVisibleChange={(value) => setIsDropdownOpen(value)}
                suffixIcon={
                  <Image
                    src="/images/helperComponents/arrow.svg"
                    width={12}
                    height={12}
                    alt=""
                    className={`w-5 h-5 sm:mt-3 mt-1 cursor-pointer transform transition-transform duration-300 ${
                      isDropdownOpen ? "rotate-180" : "rotate-0"
                    }`}
                  />
                }
              />
            </div>

            {/* Total Time Display */}
            <div className="mb-6 md:mb-2">
              <div className="text-[20px] md:text-[22px] font-[600] text-[#343A40]">
                {currentProgressData?.totalListened}
              </div>
              <div className="text-[12px] md:text-[14px] text-[#9CA3AF]">
                listened
              </div>
            </div>

            {/* Circular Progress Chart */}
            <div className="h-[180px] md:h-[250px] mb-12 md:mb-6 flex justify-center items-center">
              <CircularProgress
                categories={currentProgressData?.categories || []}
                totalListened={currentProgressData?.totalListened || ""}
                size={
                  typeof window !== "undefined" && window.innerWidth < 768
                    ? 180
                    : 200
                }
                scale={0.9}
              />
            </div>

            {/* Category Legend */}
            {/* <div className="grid sm:grid-cols-3 grid-cols-2 gap-4 md:gap-6 mx-5 items-center">
              {currentProgressData?.categories.map((category, index) => (
                
                <div key={category.name} className="flex flex-col text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{
                        backgroundColor: category.color,
                      }}
                    />
                    <div className="flex flex-col justify-start text-start w-[calc(100%-20px)]">
                      <span className="text-[12px] md:text-[14px] font-[500] text-[#9CA3AF] uppercase">
                        {category.name}
                      </span>
                      <div className="relative w-full text-[14px] md:text-[14px] font-[600] text-[#343A40] overflow-hidden whitespace-nowrap"> 
                      

                        <div
                          className="inline-block "
                          // marquee marquee-content-time
                          // style={{
                          //     animation:
                          //         " 1s linear infinite",
                          // }}
                        >
                        
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div> */}

            <div className="grid sm:grid-cols-3 grid-cols-2 gap-4 md:gap-6 mx-5 items-center">
              {currentProgressData?.categories.map(
                (category: any, index: number) => {
                  const timeText = category.seconds
                    ? formatCategoryTimeHMS(category.seconds)
                    : `${category.hours}h`;

                  const shouldMarquee =
                    timeText.includes("h") &&
                    timeText.includes("m") &&
                    timeText.includes("s");

                  const enableMarquee =
                    shouldMarquee &&
                    (screenWidth < 400 ||
                      (screenWidth >= 1440 && screenWidth < 1920));

                  return (
                    <div
                      key={category.name}
                      className="flex flex-col text-center"
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{
                            backgroundColor: category.color,
                          }}
                        />
                        <div className="flex flex-col justify-start text-start w-[calc(100%-20px)]">
                          <span className="text-[12px] md:text-[14px] font-[500] text-[#9CA3AF] uppercase">
                            {category.name}
                          </span>
                          <div className="relative w-full overflow-hidden text-[14px] font-[600] text-[#343A40] whitespace-nowrap">
                            <div
                              className={`inline-block ${
                                enableMarquee
                                  ? "marquee marquee-content-time"
                                  : ""
                              }`}
                            >
                              {timeText}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                }
              )}
            </div>
          </div>
        </div>
      </div>
      <InfoModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
    </div>
  );
};

export default Statistics;
