# User Listening Activity Tracking Implementation

## Overview
This implementation creates a comprehensive user listening activity tracking system that records daily listening statistics for audio content, categorizes listening time by content type, and tracks unique audio IDs played.

## Features Implemented

### 1. Daily Listening Records
- **Document Format**: `dd-mm-yyyy` (e.g., "26-12-2024")
- **Collection Path**: `users/{userId}/listenInfo/{documentId}`
- **Auto-creation**: Creates new documents for each day automatically

### 2. Category-Based Tracking
Audio content is automatically categorized based on lecture categories:
- **BG**: Bhagavad-gītā content
- **CC**: Caitanya-caritāmṛta content  
- **SB**: Śrīmad-Bhāgavatam content
- **Seminars**: Seminar content
- **VSN**: Viṣṇu-sahasranāma content
- **others**: All other content types

### 3. Data Structure
```javascript
{
  audioListen: 7320,           // Total seconds listened today
  creationTimestamp: 1718042567936,
  date: 1718042567936,         // Timestamp of document creation
  dateOfRecord: {
    day: 10,
    month: 6, 
    year: 2024
  },
  documentId: "10-6-2024",
  documentPath: "users/00RqKvXf8iVx4kPQHonTRGAVmGw1/listenInfo/10-6-2024",
  lastModifiedTimestamp: 1718066316552,
  listenDetails: {
    BG: 0,                     // Seconds in Bhagavad-gītā
    CC: 0,                     // Seconds in Caitanya-caritāmṛta
    SB: 0,                     // Seconds in Śrīmad-Bhāgavatam
    Seminars: 0,               // Seconds in Seminars
    VSN: 0,                    // Seconds in Viṣṇu-sahasranāma
    others: 7320               // Seconds in other content
  },
  playedBy: ["00RqKvXf8iVx4kPQHonTRGAVmGw1"],
  playedIds: [14387, 37270, 30511],  // Unique audio IDs played today
  videoListen: 0
}
```

## Implementation Details

### Core Functions Added

#### 1. `updateUserListeningActivity(lectureInput, userId)`
- **Purpose**: Core function that handles the listening activity tracking
- **Logic**: 
  - Checks if daily document exists
  - Updates existing document or creates new one
  - Adds 60 seconds to total and category-specific listening time
  - Tracks unique audio IDs to prevent duplicates
  - Updates user list in playedBy array

#### 2. `trackUserListeningActivity(lectures)`
- **Purpose**: Public function for external services to call
- **Parameters**: Single lecture/ID or array of lectures/IDs
- **Error Handling**: Continues processing even if individual items fail

#### 3. Helper Functions
- `mapLectureCategoryToListenCategory()`: Maps lecture categories to tracking categories
- `formatDateToDDMMYYYY()`: Formats dates to required string format
- `getDateComponents()`: Extracts day/month/year from Date object

### Integration Points

The tracking system is automatically integrated into:

1. **Audio Player Activity Service**
   - `updateLastPlayedPoint()` - Tracks when audio progress updates

2. **Favourite Service** 
   - `addToFavourite()` - Tracks when lectures are favorited
   - `removeFromFavourite()` - Tracks when lectures are unfavorited

3. **Complete Service**
   - `markAsCompleted()` - Tracks when lectures are marked complete
   - `removeFromCompleted()` - Tracks when completion is removed

4. **Audio Context**
   - `playAudio()` - Tracks when audio playback starts

## Files Modified

### 1. `src/services/audioPlayerActivity.service.ts`
- Added interfaces for data structures
- Added helper functions for category mapping and date formatting
- Added core tracking functionality
- Modified `updateLastPlayedPoint()` to include tracking
- Added public `trackUserListeningActivity()` function

### 2. `src/services/services/favouriteLecture.service.ts`
- Added import for tracking function
- Modified `addToFavourite()` to call tracking
- Modified `removeFromFavourite()` to call tracking

### 3. `src/services/lectureCompletion.service.ts`
- Added import for tracking function  
- Modified `markAsCompleted()` to call tracking
- Modified `removeFromCompleted()` to call tracking

### 4. `src/context/audio.context.js`
- Added import for tracking function
- Modified `playAudio()` to call tracking when audio starts

## Usage Examples

### Direct Usage
```javascript
import { trackUserListeningActivity } from './audioPlayerActivity.service';

// Track single lecture
await trackUserListeningActivity(lectureObject);

// Track multiple lectures
await trackUserListeningActivity([lecture1, lecture2]);

// Track by ID
await trackUserListeningActivity("12345");
```

### Automatic Usage
The tracking happens automatically when users:
- Play audio content
- Add/remove favorites
- Mark lectures as complete/incomplete
- Update audio progress (every 60 seconds)

## Error Handling
- Graceful failure: If tracking fails, it doesn't interrupt main functionality
- Logging: Errors are logged but don't throw exceptions
- Continuation: Processing continues for other items if one fails

## Benefits
1. **Comprehensive Tracking**: Captures all user audio interactions
2. **Category Insights**: Provides breakdown by content type
3. **Daily Aggregation**: Easy to analyze daily listening patterns
4. **Duplicate Prevention**: Avoids counting same audio multiple times per day
5. **Automatic Integration**: Works seamlessly with existing functionality
6. **Scalable**: Handles single items or bulk operations efficiently

## Testing
A test file `audioPlayerActivity.test.js` is provided to demonstrate the functionality and expected data structures.
