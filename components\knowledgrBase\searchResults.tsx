"use client";
import React, { useState, useEffect } from "react";
import FilterWithModal from "../helperComponents/filterWithModal";
import BookFilterWithModal from "../helperComponents/bookFilterWithModal";
import SearchResultItem from "./searchResultItem";
import SearchResultSkeleton from "./searchResultSkeleton";
import { Poppins } from "next/font/google";
import InfiniteScroll from "react-infinite-scroll-component";
import { SearchResult } from "@/src/api/knowledge-base.api";
import { useBookFilterContext } from "@/src/context/book-filter.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface SearchResultsProps {
    searchQuery: string;
    results: SearchResult[];
    totalResults: number;
    isLoading: boolean;
    hasMore: boolean;
    loadMore: () => void;
    resultType: "transcription" | "book";
    handleFilterChange?: (filters: any) => void;
}

const SearchResults = ({
    searchQuery,
    results = [],
    totalResults = 0,
    isLoading = false,
    hasMore = false,
    loadMore,
    resultType = "transcription",
    handleFilterChange,
}: SearchResultsProps) => {
    // State for filtered results
    const [filteredResults, setFilteredResults] =
        useState<SearchResult[]>(results);

    // Get book filter context for book results
    const { selectedBooks, clearSelectedBooks } = useBookFilterContext();

    // Update filtered results when results changes
    useEffect(() => {
        setFilteredResults(results);
    }, [results, hasMore, totalResults]);

    // Function to handle filter changes for transcriptions
    const onFilterChange = (filters: any) => {
        console.log("Filter applied in SearchResults:", filters);
        // In a real implementation, this would filter the results based on the selected filters
        setFilteredResults(results);

        // Call the external handler if provided
        if (handleFilterChange) {
            handleFilterChange(filters);
        }
    };

    // Function to handle book filter changes
    const onBookFilterChange = (selectedBooks: string[]) => {
        console.log("Book filter applied in SearchResults:", selectedBooks);
        // In a real implementation, this would filter the results based on the selected books
        setFilteredResults(results);

        // Call the external handler if provided
        if (handleFilterChange) {
            // Convert to the format expected by the parent component
            const bookFilters = { BOOKS: selectedBooks };
            handleFilterChange(bookFilters);
        }
    };

    // Render skeleton loaders for initial loading
    if (isLoading && results.length === 0) {
        return (
            <div className={`w-full ${poppins.className}`}>
                {/* Fixed header with count and filters */}
                <div className="sticky top-0 bg-white z-10 pb-4 shadow-sm px-3 sm:px-6">
                    <div className="flex flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 pt-2 mb-4 sm:mb-6">
                        <div className="text-sm text-gray-700">
                            <span className="font-medium text-[14px] sm:text-[15px]">
                                Loading results...
                            </span>
                        </div>
                        <div>
                            {resultType === "transcription" ? (
                                <FilterWithModal
                                    onFilterChange={onFilterChange}
                                    handleFilterChange={handleFilterChange}
                                />
                            ) : (
                                <BookFilterWithModal
                                    onFilterChange={onBookFilterChange}
                                    handleFilterChange={onBookFilterChange}
                                />
                            )}
                        </div>
                    </div>
                </div>

                {/* Skeleton loaders */}
                <div className="px-3 sm:px-6">
                    <div className="space-y-3 sm:space-y-4">
                        {Array(4)
                            .fill(0)
                            .map((_, index) => (
                                <SearchResultSkeleton key={`skeleton-${index}`} />
                            ))}
                    </div>
                </div>
            </div>
        );
    }

    // Render no results message
    if (!isLoading && results.length === 0) {
        return (
            <div className={`w-full px-3 sm:px-6 ${poppins.className}`}>
                {/* Fixed header with count and filters */}
                <div className="sticky top-0 bg-white z-10 pb-4 shadow-sm">
                    <div className="flex flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 pt-2 mb-4 sm:mb-6">
                        <div className="text-sm text-gray-700">
                            <span className="font-medium text-[14px] sm:text-[15px]">
                                Result Count: 0
                            </span>
                        </div>
                        <div>
                            {resultType === "transcription" ? (
                                <FilterWithModal
                                    onFilterChange={onFilterChange}
                                    handleFilterChange={handleFilterChange}
                                />
                            ) : (
                                <BookFilterWithModal
                                    onFilterChange={onBookFilterChange}
                                    handleFilterChange={onBookFilterChange}
                                />
                            )}
                        </div>
                    </div>
                </div>

                {/* No results message */}
                <div className="text-center py-8">
                    <p className="text-lg text-gray-600 mb-2">
                        No results found for "{searchQuery}"
                    </p>
                    <p className="text-sm text-gray-500">
                        Try different keywords or check your spelling
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className={`w-full ${poppins.className}`}>
            {/* Results header with count and filters - Fixed at the top */}
            <div className="sticky top-0 bg-white z-10 pb-4 sm:pb-6 px-3 sm:px-6">
                <div className="flex flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 pt-2">
                    <div className="text-sm text-gray-700">
                        <span className="font-medium text-[14px] sm:text-[15px]">
                            Result Count: {totalResults}
                        </span>
                    </div>
                    <div>
                        {resultType === "transcription" ? (
                            <FilterWithModal
                                onFilterChange={onFilterChange}
                                handleFilterChange={handleFilterChange}
                            />
                        ) : (
                            <BookFilterWithModal
                                onFilterChange={onBookFilterChange}
                                handleFilterChange={onBookFilterChange}
                            />
                        )}
                    </div>
                </div>
            </div>

            {/* Results list with infinite scroll - This part will be scrollable */}
            <div className="overflow-auto px-3 sm:px-6">
                <InfiniteScroll
                    dataLength={filteredResults.length}
                    next={() => {
                        console.log("InfiniteScroll next function called");
                        if (!isLoading && hasMore) {
                            loadMore();
                        }
                    }}
                    hasMore={hasMore}
                    loader={
                        <div className="space-y-3 sm:space-y-4 mt-4 pb-4">
                            {Array(1)
                                .fill(0)
                                .map((_, index) => (
                                    <SearchResultSkeleton
                                        key={`scroll-skeleton-${index}`}
                                    />
                                ))}
                        </div>
                    }
                    scrollableTarget="knowledgeBaseScrollable"
                    scrollThreshold={0.8}
                    className="space-y-3 sm:space-y-4"
                    endMessage={
                        <div className="text-center py-4 text-gray-500">
                            {/* {filteredResults.length > 0 &&
                                "No more results to load"} */}
                        </div>
                    }
                >
                    {filteredResults.map((result) => (
                        <SearchResultItem
                            key={result.id}
                            result={result}
                            searchQuery={searchQuery}
                            resultType={resultType}
                        />
                    ))}
                </InfiniteScroll>
            </div>
        </div>
    );
};

export default SearchResults;
