"use client";
import React from "react";
import { Poppins } from "next/font/google";
import Image from "next/image";
import { TranscriptionDetailItem } from "@/src/api/knowledge-base.api";
import { formatSecondsToHHMMSS } from "@/src/utils/timeFormat";
import { FiBookmark } from "react-icons/fi";
import { GrLocation } from "react-icons/gr";
import { MdOutlineDateRange } from "react-icons/md";
import { FaPlay } from "react-icons/fa";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface TranscriptionHeaderProps {
    transcription: TranscriptionDetailItem;
    onPlayAudio: () => void;
}

const TranscriptionHeader = ({ transcription, onPlayAudio }: TranscriptionHeaderProps) => {
    const hasAudio = transcription.resources_audios && transcription.resources_audios.length > 0;

    return (
        <div className={`mb-6 ${poppins.className}`}>
            <div className="flex flex-col md:flex-row gap-4 md:gap-6">
                {/* Thumbnail and audio player */}
                <div className="relative w-full md:w-[280px] h-[160px] md:h-[180px] flex-shrink-0">
                    <Image
                        src={transcription.thumbnail || "/sample image.jpeg"}
                        alt={transcription.title}
                        fill
                        className="object-cover rounded-lg"
                    />
                    {hasAudio && (
                        <button
                            onClick={onPlayAudio}
                            className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-40 transition-all rounded-lg"
                            aria-label="Play audio"
                        >
                            <div className="bg-primary text-white rounded-full p-4">
                                <FaPlay size={24} />
                            </div>
                        </button>
                    )}
                </div>

                {/* Transcription details */}
                <div className="flex-1">
                    <h1 className="text-xl sm:text-2xl md:text-3xl font-semibold text-[#343A40] mb-3">
                        {transcription.title}
                    </h1>

                    {/* Metadata */}
                    <div className="flex flex-wrap gap-y-2 text-sm text-gray-600 mb-4">
                        {transcription.category && (
                            <div className="flex items-center mr-4">
                                <FiBookmark className="mr-1" />
                                <span>{transcription.category}</span>
                            </div>
                        )}
                        {transcription.place && (
                            <div className="flex items-center mr-4">
                                <GrLocation className="mr-1" />
                                <span>{transcription.place}</span>
                            </div>
                        )}
                        {transcription.dateOfRecording && (
                            <div className="flex items-center mr-4">
                                <MdOutlineDateRange className="mr-1" />
                                <span>{transcription.dateOfRecording}</span>
                            </div>
                        )}
                        {transcription.length && (
                            <div className="flex items-center">
                                <span className="text-gray-600">
                                    {formatSecondsToHHMMSS(transcription.length)}
                                </span>
                            </div>
                        )}
                    </div>

                    {/* Description */}
                    {transcription.description && (
                        <p className="text-gray-700 text-sm">
                            {transcription.description}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TranscriptionHeader;
