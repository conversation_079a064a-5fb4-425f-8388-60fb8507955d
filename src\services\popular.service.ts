import { getAllLectures } from "./indexedDB.service";
import { getDeepSearchResults } from "../api/mediaLibrary.api";
import { applyFilters, applySorting } from "../utils/helperFunctions";
import { getPopularLectureIds } from "../api/popular.api";

export interface FetchPopularParams {
  searchQuery?: string;
  sortBy?: number;
  allFilter?: { [key: string]: string[] };
  playbackMode?: number;
  popularPeriod?: string;
}

export interface FetchDeepSearchPopularParams {
  searchQuery: string;
  playbackMode?: number;
  sortBy?: number;
  allFilter?: { [key: string]: string[] };
}

// Helper function to apply search filtering
const applySearchFilter = (lectures: any[], searchQuery: string) => {
  if (!searchQuery.trim()) return lectures;

  // Convert search query to lowercase for case-insensitive comparison
  const query = searchQuery.toLowerCase().trim();

  // Split the query into words for full name search
  const queryWords = query
    .split(/\s+/)
    .filter((word: string) => word.length > 0);

  // Filter lectures by comparing search query with title array elements
  return lectures.filter((lecture: any) => {
    // Check if title exists and is an array
    if (!lecture.title || !Array.isArray(lecture.title)) return false;

    // Check for exact match first (highest priority)
    if (
      lecture.title.some(
        (titlePart: string) => titlePart.toLowerCase() === query
      )
    ) {
      return true;
    }

    // Check if any element in the title array contains the full search query
    if (
      lecture.title.some((titlePart: string) =>
        titlePart.toLowerCase().includes(query)
      )
    ) {
      return true;
    }

    // If the query has multiple words, check if all words appear in any title part
    if (queryWords.length > 1) {
      // Join all title parts into a single string for multi-word search
      const fullTitle = lecture.title.join(" ").toLowerCase();

      // Check if all query words are present in the full title
      const allWordsPresent = queryWords.every((word: string) =>
        fullTitle.includes(word)
      );

      if (allWordsPresent) {
        return true;
      }
    }

    return false;
  });
};

/**
 * Fetch and process favorite lectures based on provided parameters
 * @param params - Object containing search query, sort option, and filter values
 * @returns Promise<any[]> - Array of processed favorite lectures
 */
export const fetchPopularLectures = async (params: FetchPopularParams = {}): Promise<any[]> => {
  const {
    searchQuery = "",
    sortBy = 1,
    allFilter = {},
    playbackMode = 1,
    popularPeriod = "allTime",
  } = params;

  try {

    const res = await getPopularLectureIds(popularPeriod)

    // Get all lectures from IndexedDB
    const allLectures = await getAllLectures();

    const popularLectureObjects = popularPeriod === "allTime" ? res?.allTime : popularPeriod === "thisWeek" ? res?.thisWeek : popularPeriod === "lastWeek" ? res?.lastWeek : popularPeriod === "thisMonth" ? res?.thisMonth : res?.lastMonth || [] ;

    let historyLectures = popularLectureObjects
      .map((poplecture: any) =>
        allLectures.find((lecture: any) => lecture.id === poplecture?.playId)
      )
      .filter((lecture: any) => lecture !== undefined);

    if (historyLectures.length === 0) {
      return [];
    }

    // Apply search filtering if search query is provided
    if (searchQuery.trim()) {
      historyLectures = applySearchFilter(historyLectures, searchQuery);
    }

    // Apply filters (including playback mode)
    const hasActiveFilters = Object.values(allFilter).some(
      (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
    );

    if (hasActiveFilters || playbackMode !== 1) {
      historyLectures = applyFilters(historyLectures, allFilter, playbackMode);
    }

    // Apply sorting
    if (sortBy !== 1) {
      historyLectures = applySorting(historyLectures, sortBy);
    }

    console.log(`Processed ${historyLectures.length} favorite lectures`);
    return historyLectures;

  } catch (error) {
    console.error("Error in fetchFavorites:", error);
    throw error;
  }
};

/**
 * Fetch and process deep search favorite lectures based on provided parameters
 * @param params - Object containing search query, playback mode, sort option, and filter values
 * @returns Promise<any[]> - Array of processed favorite lectures from deep search
 */
export const fetchDeepSearchPopular = async (params: FetchDeepSearchPopularParams): Promise<any[]> => {
  const {
    searchQuery,
    playbackMode = 1,
    sortBy = 1,
    allFilter = {}
  } = params;

  if (!searchQuery.trim()) {
    return [];
  }

  try {
    // Prepare deep search parameters
    const searchParams = {
      query: searchQuery,
      size: 9000,
      from: 0,
    };

    // Get all lectures from IndexedDB and perform deep search
    const [allLectures, response] = await Promise.all([
      getAllLectures(),
      getDeepSearchResults(searchParams)
    ]);

    if (!response?.data?.transcriptions) {
      return [];
    }

    // Extract lecture IDs from deep search response
    const responseIds = response.data.transcriptions.map((lecture: any) => lecture.id) || [];

    // Get matching lectures from IndexedDB and filter for favorites only
    let deepSearchFavorites = responseIds
      .map((id: any) =>
        allLectures.find((lecture: any) => lecture.id === id)
      )
      .filter((lecture: any) => lecture !== undefined && lecture?.isFavourite);

    // Apply filters (including playback mode)
    const hasActiveFilters = Object.values(allFilter).some(
      (filterValues: any) => Array.isArray(filterValues) && filterValues.length > 0
    );

    if (hasActiveFilters || playbackMode !== 1) {
      deepSearchFavorites = applyFilters(deepSearchFavorites, allFilter, playbackMode);
    }

    // Apply sorting
    if (sortBy !== 1) {
      deepSearchFavorites = applySorting(deepSearchFavorites, sortBy);
    }

    console.log(`Processed ${deepSearchFavorites.length} deep search favorite lectures`);
    return deepSearchFavorites;

  } catch (error) {
    console.error("Error in fetchDeepSearchFavorites:", error);
    throw error;
  }
};
