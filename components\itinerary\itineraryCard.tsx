"use client";

import { <PERSON><PERSON> } from "next/font/google";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import { FiPhoneCall } from "react-icons/fi";
interface ItineraryCardProps {
  stop: {
    id: string;
    title: { en: string };
    images: string[];
    period: { startDate: string; endDate: string };
    contactPerson: { name: { en: string }; phoneNumber: string };
    venue: { address: { en: string } };
  };
  onClick: () => void;
  onZoomClick: () => void;
  isSelected?: boolean;
  isPast?:boolean
}

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const ItineraryCard = ({
  stop,
  onClick,
  onZoomClick,
  isSelected = false,
  isPast
}: ItineraryCardProps) => {
  return (
    // <div className={`flex w-full h-[120px] bg-white rounded-xl shadow-md overflow-hidden flex-shrink-0 ${roboto.className}`}>
    <div
      className={`
  flex w-full h-[120px]
  rounded-xl overflow-hidden flex-shrink-0
  ${roboto.className}
  ${
    isSelected
      ? "bg-[#FFF4EB] border-2 border-[#ff6f018a] shadow-[0_4px_12px_rgba(255,113,1,0.15)]"
      // : "bg-white border border-transparent shadow-md"
      : isPast
        ? "bg-gray-100 border border-gray-200 text-gray-400"
        : "bg-white border border-transparent text-gray-800 shadow-md"
  }
`}
    >
      {/* Image - 30% of card width */}
      <div className="relative w-[30%] h-full p-3 flex-shrink-0">
        {/* <img
          src={stop.images[0]}
          alt={stop.title.en}
          className="w-full h-full object-cover rounded-lg"
        /> */}
        <Swiper
          modules={[Autoplay, Pagination]}
          autoplay={{ delay: 2500, disableOnInteraction: false }}
          pagination={{
            clickable: true,
            renderBullet: (index, className) =>
              `<span class="custom-bullet ${className}"></span>`,
          }}
          loop={true}
          className="w-full h-full rounded-lg"
        >
          {stop.images.map((img, index) => (
            <SwiperSlide key={index}>
              <img
                src={img}
                alt={`${stop.title.en}-${index}`}
                className="w-full h-full object-cover rounded-lg"
              />
            </SwiperSlide>
          ))}
        </Swiper>

        <Image
          src="/images/itinerary/zoom.svg"
          alt="Zoom"
          width={24}
          height={24}
          onClick={onZoomClick}
          className="absolute top-3 right-3 cursor-pointer z-10"
        />
      </div>

      {/* Content - 70% */}
      <div className={`w-[70%] py-3 flex flex-col justify-center text-sm  ${isPast? "text-gray-400" :"text-gray-800"} overflow-hidden`}>
        <p className="text-base font-medium text-[16px] leading-5 sm:mb-1 mb-[2px] truncate whitespace-nowrap">
          {stop.title.en}
        </p>

        <div className="flex items-center gap-2 mb-[2px] sm:mb-[4px] overflow-hidden">
          <Image
            src="/images/itinerary/person.svg"
            alt="Person"
            width={16}
            height={16}
          />
          <span className="truncate whitespace-nowrap font-normal text-[12px] text-[#959698]">
            {stop.contactPerson.name.en}
          </span>
        </div>

        <div className="flex items-center gap-2 mb-[2px] sm:mb-[4px] overflow-hidden">
          <Image
            src="/images/itinerary/location.svg"
            alt="Location"
            width={16}
            height={16}
          />
          <span className="truncate whitespace-nowrap font-normal text-[12px] text-[#959698]">
            {stop.venue.address.en}
          </span>
        </div>

        <div className={`flex items-center gap-2 font-semibold overflow-hidden ${isPast? "text-[#959698]" :"text-primary"}`}>
          {/* <Image
            src="/images/itinerary/phone.svg"
            alt="Phone"
            width={14}
            height={14}
          /> */}
          <FiPhoneCall size={14} className={`${isPast? "text-[#959698]" :"text-primary"}`}></FiPhoneCall>
          <span className="truncate whitespace-nowrap font-normal text-[12px]">
            {stop.contactPerson.phoneNumber}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ItineraryCard;

