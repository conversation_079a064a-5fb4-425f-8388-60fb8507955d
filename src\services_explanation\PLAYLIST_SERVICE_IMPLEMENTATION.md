# Playlist Service Implementation

## Overview
This implementation provides a comprehensive playlist management service that handles creation, retrieval, and modification of both public and private playlists. It manages user-specific playlist operations with proper authentication and data organization in Firebase Firestore.

## Features Implemented

### 1. Playlist Management
- **Public Playlists**: Manage publicly accessible playlists
- **Private Playlists**: Handle user-specific private playlists
- **Playlist Creation**: Create new playlists with metadata
- **Content Addition**: Add lectures to existing playlists

### 2. User-Specific Operations
- **Authentication Integration**: User ID and email-based operations
- **Permission Management**: Proper access control for private playlists
- **Data Organization**: Structured storage in user-specific collections

### 3. Metadata Management
- **Automatic Timestamps**: Creation and modification time tracking
- **Document Path Management**: Automatic path and ID generation
- **Author Attribution**: Automatic author information assignment

## Data Structures

### Playlist Document Structure
```javascript
{
  listID: "auto-generated-id",           // Unique playlist identifier
  docPath: "collection/path/document",   // Full document path
  authorEmail: "<EMAIL>",       // C<PERSON>'s email
  creationTime: 1718042567936,           // Creation timestamp
  lastUpdate: 1718066316552,             // Last modification timestamp
  name: "My Playlist",                   // Playlist name
  description: "Playlist description",   // Playlist description
  lectures: [12345, 67890],             // Array of lecture IDs
  isPublic: true,                        // Public/private flag
  tags: ["spiritual", "education"],      // Playlist tags
  // Additional custom fields as needed
}
```

### Collection Paths
- **Public Playlists**: `PublicPlaylists/{playlistId}`
- **Private Playlists**: `PrivatePlaylists/{userId}/{email}/{playlistId}`

## Implementation Details

### Core Functions

#### 1. `fetchUserPublicPlaylists()`
- **Purpose**: Retrieve user's public playlists
- **Authentication**: Uses email from localStorage
- **Query**: Filters by authorEmail field
- **Returns**: Array of public playlists created by the user

#### 2. `fetchUserPrivatePlaylists()`
- **Purpose**: Retrieve user's private playlists
- **Authentication**: Uses userId and email from localStorage
- **Collection Path**: User-specific private collection
- **Returns**: Array of private playlists for the user

#### 3. `addToPublicPlaylist(playlistId, payload)`
- **Purpose**: Add content to existing public playlist
- **Parameters**: Playlist ID and update payload
- **Operation**: Updates existing public playlist document
- **Returns**: Success boolean

#### 4. `addToPrivatePlaylist(playlistId, payload)`
- **Purpose**: Add content to existing private playlist
- **Parameters**: Playlist ID and update payload
- **Authentication**: Validates user ID and email
- **Operation**: Updates user-specific private playlist
- **Returns**: Success boolean

#### 5. `createPlaylist(type, payload)`
- **Purpose**: Create new playlist (public or private)
- **Parameters**: Playlist type ("PUBLIC" or "PRIVATE") and playlist data
- **Features**: 
  - Automatic metadata assignment
  - Document ID and path generation
  - User authentication validation

### Playlist Creation Process

#### Public Playlist Creation
1. **Authentication Check**: Validate user ID and email
2. **Collection Reference**: Get PublicPlaylists collection
3. **Metadata Assignment**: Add author, timestamps, and IDs
4. **Document Creation**: Create new document with auto-generated ID
5. **Path Assignment**: Set document path in payload

#### Private Playlist Creation
1. **Authentication Check**: Validate user ID and email
2. **Collection Reference**: Get user-specific private collection
3. **Metadata Assignment**: Add author, timestamps, and IDs
4. **Document Creation**: Create new document in private collection
5. **Path Assignment**: Set document path in payload

### Authentication Integration

#### Required Authentication Data
```javascript
const userId = localStorage.getItem("firebaseUid");
const email = localStorage.getItem("email");

if (!userId || !email) {
  throw new Error("User ID or email is missing from localStorage.");
}
```

## Integration Points

The playlist service integrates with:

1. **Authentication System**
   - localStorage for user credentials
   - User ID and email validation

2. **Firebase Firestore**
   - Document creation and updates
   - Collection management
   - Query operations

3. **UI Components**
   - Playlist creation forms
   - Playlist management interfaces
   - Content addition workflows

4. **Media Library**
   - Lecture ID management
   - Content organization

## Usage Examples

### Fetch User Playlists
```javascript
import { 
  fetchUserPublicPlaylists, 
  fetchUserPrivatePlaylists 
} from './playlist.service';

// Get user's public playlists
const publicPlaylists = await fetchUserPublicPlaylists();

// Get user's private playlists
const privatePlaylists = await fetchUserPrivatePlaylists();
```

### Create New Playlist
```javascript
import { createPlaylist } from './playlist.service';

// Create public playlist
const publicPlaylistData = {
  name: "My Public Playlist",
  description: "A collection of spiritual lectures",
  lectures: [12345, 67890],
  tags: ["spiritual", "education"]
};

await createPlaylist("PUBLIC", publicPlaylistData);

// Create private playlist
const privatePlaylistData = {
  name: "Personal Study",
  description: "My personal study collection",
  lectures: [11111, 22222]
};

await createPlaylist("PRIVATE", privatePlaylistData);
```

### Add Content to Playlist
```javascript
import { addToPublicPlaylist, addToPrivatePlaylist } from './playlist.service';

// Add lectures to public playlist
const updatePayload = {
  lectures: arrayUnion(33333, 44444),  // Add new lecture IDs
  lastUpdate: Date.now()
};

await addToPublicPlaylist("playlistId123", updatePayload);

// Add lectures to private playlist
await addToPrivatePlaylist("privatePlaylistId456", updatePayload);
```

### Complete Playlist Creation Example
```javascript
const createNewPlaylist = async (type, name, description, lectureIds) => {
  try {
    const playlistData = {
      name: name,
      description: description,
      lectures: lectureIds,
      tags: ["custom"],
      isPublic: type === "PUBLIC"
    };

    const success = await createPlaylist(type, playlistData);
    
    if (success) {
      console.log(`${type} playlist created successfully`);
    }
  } catch (error) {
    console.error("Error creating playlist:", error);
  }
};
```

## Error Handling

### Authentication Errors
- **Missing Credentials**: Validates user ID and email presence
- **Invalid User**: Handles authentication failures gracefully

### Database Errors
- **Network Issues**: Handles Firestore connectivity problems
- **Permission Errors**: Manages access control violations
- **Data Validation**: Ensures proper data structure

### User Feedback
```javascript
catch (error) {
  console.error("Error creating playlist: ", error);
  throw new Error("We couldn't create playlist at this moment.");
}
```

## Benefits
1. **User Organization**: Separate public and private playlist management
2. **Authentication Integration**: Secure user-specific operations
3. **Automatic Metadata**: Timestamps and author information management
4. **Flexible Content**: Support for various playlist types and content
5. **Scalability**: Efficient document organization and querying
6. **Error Resilience**: Comprehensive error handling and user feedback
7. **Data Integrity**: Proper validation and structure enforcement

## Security Features
1. **User Isolation**: Private playlists are user-specific
2. **Authentication Required**: All operations require valid user credentials
3. **Access Control**: Proper permission management for different playlist types
4. **Data Validation**: Input validation and sanitization
