import {
    createUserWithEmailAndPassword,
    getAuth,
    GoogleAuthProvider,
    OAuthProvider,
    sendPasswordResetEmail,
    signInWithEmailAndPassword,
    signInWithPopup,
    signOut,
} from "firebase/auth";
import { auth, db, messaging } from "../config/firebase.config";
import { doc, getDoc, setDoc } from "firebase/firestore";
import axios from "axios";
import { fetch } from "../libs/helper";

export const signInWithFirebase = async (
    email: string,
    password: string
): Promise<any> => {
    const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
    );
    const idToken = await userCredential.user.getIdToken();
    return { userCredential, idToken };
};

export const signUpWithFirebase = async (email: string, password: string) => {
    const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
    );
    const user = userCredential.user;
    return user;
};

export const loginWithApple = async () => {
    const auth: any = getAuth();
    const provider = new OAuthProvider("apple.com");
    const result: any = await signInWithPopup(auth, provider);

    const user = result.user;

    const credential: any = OAuthProvider.credentialFromResult(result);
    const accessToken = credential.accessToken;
    const idToken = credential.idToken;

    return { user, idToken };
};

export const forgotPasswordWithFirebase = async (
    email: string
): Promise<any> => {
    await sendPasswordResetEmail(auth, email);
};

export const loginWithGoogle = async () => {
    const provider = new GoogleAuthProvider();
    const result = await signInWithPopup(auth, provider);
    return result.user;
};

export const signOutUser = async () => {
    const auth = getAuth();
    await signOut(auth);
};

export const createUserInUserCollection = async (userId: string, settings: any) => {
    try {
        const docRef = doc(db, `users/${userId}/Settings/userSettings`);
        await setDoc(docRef, settings, { merge: true });
    } catch (error) {
        console.error("Error updating user settings:", error);
        throw error;
    }
}