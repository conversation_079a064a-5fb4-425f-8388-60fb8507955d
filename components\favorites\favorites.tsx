"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON> } from "next/font/google";
import PlaybackMode from "../helperComponents/playbackMode";
import SortBy from "../helperComponents/sortBy";
import Filter from "../helperComponents/filter";
import SelectFiles from "../helperComponents/selectFile";
import AddToPlaylist from "../helperComponents/addToPlaylist";
import MarkAsComplete from "../helperComponents/markAsComplete";
import { CloseOutlined } from "@ant-design/icons";
import { useSearchContext } from "@/src/context/search.context";
import { useFilterContext } from "@/src/context/filter.context";
import { message } from "antd";
import LectureCard from "../lectureCard/lectureCard";
import InfiniteScroll from "react-infinite-scroll-component";
import LectureCardSkeleton from "../lectureCard/lectureCardSkeleton";
import { sortByOptions } from "@/src/libs/constant";
import RemoveFavorites from "../helperComponents/removeFavourite";
import { fetchFavorites, fetchDeepSearchFavorites } from "@/src/services/favorites.service";
import AudioToggleSwitch from "../helperComponents/audioToggleSwitch";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Favorites = () => {
  const { searchQuery, search, deepSearch, isSearching, setIsSearching } =
    useSearchContext();
  const {
    selectedFilterValues,
    sortBy,
    isFiltering,
    setIsFiltering,
    playbackMode,
  } = useFilterContext();

  const [isSelectFileOpen, setIsSelectFileOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  const [allLectures, setAllLectures] = useState<any[]>([]);
  const [displayedLectures, setDisplayedLectures] = useState<any[]>([]);
  const [isDeepSearchLoading, setIsDeepSearchLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const resultsPerPage = 20;

  const isFirstRender = useRef(true);
  const isSearchInitialMount = useRef(true);
  const isDeepSearchInitialMount = useRef(true);

  // Load initial favorites data
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      loadFavoritesData();
    }
  }, []);

  // Handle search
  useEffect(() => {
    if (isSearchInitialMount.current) {
      isSearchInitialMount.current = false;
      return;
    }
    handleSearch();
  }, [search]);

  // Handle deep search
  useEffect(() => {
    if (isDeepSearchInitialMount.current) {
      isDeepSearchInitialMount.current = false;
      return;
    }
    handleDeepSearch();

    return () => {
      setIsDeepSearchLoading(false);
    };
  }, [deepSearch]);

  // Handle filter/sort changes
  useEffect(() => {
    if (!isFirstRender.current) {
      if (isSearching) {
        // Re-apply current search with new filters/sort
        if (deepSearch) {
          handleDeepSearch();
        } else {
          handleSearch();
        }
      } else {
        // Re-load favorites with new filters/sort
        loadFavoritesData();
      }
    }
  }, [sortBy, selectedFilterValues, playbackMode]);

  const loadFavoritesData = async () => {
    try {
      setLoading(true);

      const lectures = await fetchFavorites({
        searchQuery: isSearching ? searchQuery : "",
        sortBy,
        allFilter: selectedFilterValues,
        playbackMode
      });

      setAllLectures(lectures);

      // Reset pagination and load first page
      setPage(1);
      const firstPageLectures = lectures.slice(0, resultsPerPage);
      setDisplayedLectures(firstPageLectures);
      setHasMore(lectures.length > resultsPerPage);

    } catch (error) {
      console.error("Error loading favorites:", error);
      message.error(
        "We couldn't fetch favorites at this moment, please try again later."
      );
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  const resetSelectionState = () => {
    setIsSelectFileOpen(false);
    setSelectedFiles([]);
  };

  const handleSearch = async () => {
    resetSelectionState();

    if (!searchQuery.trim()) {
      setIsSearching(false);
      await loadFavoritesData();
      return;
    }

    try {
      setIsLoadingMore(true);
      setIsSearching(true);

      const lectures = await fetchFavorites({
        searchQuery,
        sortBy,
        allFilter: selectedFilterValues,
        playbackMode
      });

      setAllLectures(lectures);

      // Reset pagination and load first page
      setPage(1);
      const firstPageLectures = lectures.slice(0, resultsPerPage);
      setDisplayedLectures(firstPageLectures);
      setHasMore(lectures.length > resultsPerPage);

    } catch (error) {
      console.error("Error searching favorites:", error);
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setTimeout(() => {
        setIsLoadingMore(false);
      }, 800);
    }
  };

  const handleDeepSearch = async () => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      await loadFavoritesData();
      return;
    }

    resetSelectionState();

    try {
      setIsLoadingMore(true);
      setIsSearching(true);
      setIsDeepSearchLoading(true);
      setDisplayedLectures([]); // Clear previous results to show skeleton

      const lectures = await fetchDeepSearchFavorites({
        searchQuery,
        playbackMode,
        sortBy,
        allFilter: selectedFilterValues
      });

      setAllLectures(lectures);

      // Reset pagination and load first page
      setPage(1);
      const firstPageLectures = lectures.slice(0, resultsPerPage);
      setDisplayedLectures(firstPageLectures);
      setHasMore(lectures.length > resultsPerPage);

    } catch (error) {
      console.error("Error deep searching favorites:", error);
      setAllLectures([]);
      setDisplayedLectures([]);
      setHasMore(false);
    } finally {
      setTimeout(() => {
        setIsLoadingMore(false);
        setIsDeepSearchLoading(false);
      }, 800);
    }
  };

  const handleFilterChange = (values: any) => {
    resetSelectionState();

    if (!values || typeof values !== "object") {
      console.error("Invalid filter values:", values);
      return;
    }

    const hasActiveFilters = Object.values(values).some(
      (filterValues: any) =>
        Array.isArray(filterValues) && filterValues.length > 0
    );
    setIsFiltering(hasActiveFilters);
  };

  // Function to load more results for infinite scroll
  const loadMoreResults = useCallback(() => {
    if (!hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    setTimeout(() => {
      const nextPage = page + 1;
      const startIndex = (nextPage - 1) * resultsPerPage;
      const endIndex = startIndex + resultsPerPage;

      const nextResults = allLectures.slice(startIndex, endIndex);

      if (nextResults.length > 0) {
        setDisplayedLectures((prev) => [...prev, ...nextResults]);
        setPage(nextPage);
      }

      // Check if we've loaded all results
      if (endIndex >= allLectures.length) {
        setHasMore(false);
      }

      setIsLoadingMore(false);
    }, 500);
  }, [hasMore, isLoadingMore, page, allLectures, resultsPerPage]);

  // Function to handle favorite updates
  const handleLectureUpdated = async (
    updatedIds: (string | number)[],
    field: string,
    value: boolean
  ) => {
    if (updatedIds.length === 0) return;

    if (field === "isFavourite") {
      // Update in allLectures
      setAllLectures((prev) =>
        prev.filter((lecture: any) => !updatedIds.includes(lecture.id))
      );

      // If removing from favorites, remove from displayed
      setDisplayedLectures((prevResults) =>
        prevResults.filter((lecture: any) => !updatedIds.includes(lecture.id))
      );
      setSelectedFiles([]); // Optional: only if required when removing
    } else {
      // Update in allLectures
      setAllLectures((prev: any) =>
        prev.map((lecture: any) => {
          if (updatedIds.includes(lecture.id)) {
            return { ...lecture, [field]: value };
          }
          return lecture;
        })
      );

      // Otherwise, just update the field in displayedLectures
      setDisplayedLectures((prevResults) =>
        prevResults.map((lecture: any) => {
          if (updatedIds.includes(lecture.id)) {
            return { ...lecture, [field]: value };
          }
          return lecture;
        })
      );
    }
  };


  return (
    <div className={`w-full h-full ${roboto.className}`}>
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        {/* Result and selected count */}
        <div className="hidden md:flex gap-4 md:absolute top-5 min-[460px]:top-2 right-4">
          {isSelectFileOpen && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary`}
            >
              <span className="font-[500]">Selected:</span>{" "}
              {selectedFiles.length}
            </h2>
          )}
          {(isSearching || isFiltering) && (
            <h2
              className={`text-[12px] leading-5 font-[400] text-text-primary `}
            >
              <span className="font-[500]">Result count:</span>{" "}
              {allLectures.length.toString()}
            </h2>
          )}
        </div>

        {/* Heading */}
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Favourites
        </h1>
        <div className="flex gap-2 items-end">
          {!isSelectFileOpen ? (
            <>
            <AudioToggleSwitch />
              <PlaybackMode />
              <SortBy />
              <Filter {...{ handleFilterChange }} />
              <SelectFiles {...{ setIsSelectFileOpen }} />
            </>
          ) : (
            <>
              <AddToPlaylist {...{ selectedFiles }} />
              <RemoveFavorites
                selectedFiles={selectedFiles}
                onFavoritesUpdated={handleLectureUpdated}
              />
              <MarkAsComplete
                {...{ selectedFiles, onCompleteUpdated: handleLectureUpdated }}
              />
              <div
                className="h-[32px] flex gap-2 items-center pt-[2px] px-2.5 md:px-3 text-[13px] text-left bg-[#E0E0E0] rounded-[12px] hover:opacity-85 cursor-pointer transition-all"
                onClick={resetSelectionState}
              >
                <h2 className="text-[13px] leading-5 font-[400] text-text-primary">
                  <span className="sm:block hidden">Cancel</span>{" "}
                  <span className="block sm:hidden">
                    <CloseOutlined />
                  </span>
                </h2>
              </div>
            </>
          )}
        </div>
      </div>

      <div
        id="scrollableDiv"
        className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
      >
        {!isSearching && !isFiltering ? (
          <div className="w-full flex flex-col gap-4">
            {loading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                {Array(15)
                  .fill(0)
                  .map((_, index) => (
                    <LectureCardSkeleton key={`favorites-skeleton-${index}`} />
                  ))}
              </div>
            ) : allLectures.length === 0 ? (
              <div className="text-center py-20">
                <p className="text-lg text-gray-500">No favorites found</p>
                <p className="text-sm text-gray-400 mt-2">
                  You haven't added any lectures to favorites yet
                </p>
              </div>
            ) : (
              <InfiniteScroll
                dataLength={displayedLectures.length}
                next={loadMoreResults}
                hasMore={hasMore}
                loader={
                  <div className="w-full grid grid-cols-5 gap-5 mt-4">
                    {Array(5)
                      .fill(0)
                      .map((_, index) => (
                        <LectureCardSkeleton
                          key={`favorites-skeleton-${index}`}
                        />
                      ))}
                  </div>
                }
                endMessage={null}
                scrollableTarget="scrollableDiv"
                scrollThreshold={0.9}
                className="!overflow-x-hidden"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                  {displayedLectures.map((lecture: any) => (
                    <LectureCard
                      key={lecture.id}
                      lectureData={lecture}
                      {...{
                        isSelectFileOpen,
                        selectedFiles,
                        setSelectedFiles,
                        onLectureUpdated: handleLectureUpdated,
                      }}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            )}
          </div>
        ) : (
          <div className="w-full flex flex-col gap-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                {isSearching
                  ? `Search Results for "${searchQuery}"`
                  : "Filtered Results"}
              </h1>
              {(() => {
                // Check if any filters are applied
                const hasFilters = Object.values(selectedFilterValues).some(
                  (values: any) => Array.isArray(values) && values.length > 0
                );

                if (hasFilters) {
                  return (
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <span className="text-sm text-gray-600">
                        Filters applied:
                      </span>
                      {Object.entries(selectedFilterValues).map(
                        ([filterType, values]: [string, any]) => {
                          if (Array.isArray(values) && values.length > 0) {
                            return (
                              <span
                                key={filterType}
                                className="text-[10px] bg-primary-light text-primary px-2 py-1 rounded-md"
                              >
                                {filterType}: {values.length}
                              </span>
                            );
                          }
                          return null;
                        }
                      )}
                    </div>
                  );
                }
                return null;
              })()}
              {/* {sortBy !== 1 && (
                <div className="text-sm text-gray-600 mb-2">
                  Sorted by:{" "}
                  {sortByOptions.find((option: any) => option.value === sortBy)
                    ?.label || "Default view"}
                </div>
              )} */}
            </div>

            {isDeepSearchLoading ? (
              // Show skeleton loading during deep search
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                {Array(15)
                  .fill(0)
                  .map((_, index) => (
                    <LectureCardSkeleton
                      key={`deep-search-skeleton-${index}`}
                    />
                  ))}
              </div>
            ) : displayedLectures.length > 0 ? (
              <InfiniteScroll
                dataLength={displayedLectures.length}
                next={loadMoreResults}
                hasMore={hasMore}
                loader={
                  <div className="w-full grid grid-cols-5 gap-5 mt-4">
                    {Array(5)
                      .fill(0)
                      .map((_, index) => (
                        <LectureCardSkeleton key={`search-skeleton-${index}`} />
                      ))}
                  </div>
                }
                endMessage={null}
                scrollableTarget="scrollableDiv"
                scrollThreshold={0.9}
                className="!overflow-x-hidden"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                  {displayedLectures.map((lecture: any) => (
                    <LectureCard
                      key={lecture.id}
                      lectureData={lecture}
                      {...{
                        isSelectFileOpen,
                        selectedFiles,
                        setSelectedFiles,
                        onLectureUpdated: handleLectureUpdated,
                      }}
                    />
                  ))}
                </div>
              </InfiniteScroll>
            ) : (
              <div className="text-center py-10">
                <p className="text-lg text-gray-500">
                  {isSearching
                    ? `No results found for "${searchQuery}"${
                        isFiltering ? " with the applied filters" : ""
                      }`
                    : "No results match the applied filters"}
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  {isSearching
                    ? "Try different keywords or check your spelling"
                    : "Try adjusting your filter criteria"}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Favorites;
