"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { message, Switch } from "antd";
import {
  fetchUserSettings,
  subscribeTopic,
  unsubscribeTopic,
  updateUserSettings,
} from "@/src/api/settings.api";

const open_Sans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const inter = Inter({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Settings = () => {
  const router = useRouter();

  const [langSettings, setLangSettings] = useState({
    english: false,
    hindi: false,
    bengali: false,
  });
  const [loading, setLoading] = useState(true);
  const [loadingSwitch, setLoadingSwitch] = useState("");

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const settings = await fetchUserSettings();
      const fcmToken = localStorage.getItem("fcmToken");

      if (settings) {
        setLangSettings(
          settings?.notification || {
            english: true,
            hindi: true,
            bengali: true,
          }
        );
      }

      if (settings?.notification &&   fcmToken && fcmToken !== "ios-safari-not-supported") {
        const appName = process.env.APP;
        ["english", "hindi", "bengali"].forEach((lang) => {
          if (settings.notification[lang]) {
            subscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
          } else {
            unsubscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
          }
        });
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = async (lang: keyof typeof langSettings) => {
    const previousSettings = { ...langSettings };
    try {
      const payload = {
        lastModificationTime: Date.now(),
        notification: {
          ...langSettings,
          [lang]: !langSettings[lang],
        },
      };

      const appName = process.env.APP;
      const fcmToken = localStorage.getItem("fcmToken");

      // message.success(`Settings updated successfully ${fcmToken}`);
      if (fcmToken) {
        // Check if this is the iOS Safari placeholder token
        if (fcmToken === "ios-safari-not-supported") {
          // For iOS Safari, we'll just update the settings without actually subscribing
          // since push notifications aren't fully supported
          console.warn(
            "iOS Safari detected - updating settings without FCM subscription"
          );
          await updateUserSettings(payload);
          setLangSettings((prev) => ({
            ...prev,
            [lang]: !prev[lang],
          }));
          setLoadingSwitch("");
          return;
        }

        setLoadingSwitch(lang);
        if (payload.notification[lang]) {
          const res: any = await subscribeTopic(
            fcmToken,
            `${appName}_${lang.toUpperCase()}`
          );

          if (res?.success) {
            await updateUserSettings(payload);
            setLangSettings((prev) => ({
              ...prev,
              [lang]: !prev[lang],
            }));
          }
        } else {
          const res: any = await unsubscribeTopic(
            fcmToken,
            `${appName}_${lang.toUpperCase()}`
          );
          if (res?.success) {
            await updateUserSettings(payload);
            setLangSettings((prev) => ({
              ...prev,
              [lang]: !prev[lang],
            }));
          }
        }
      } else {
        setLoadingSwitch(lang);
        await updateUserSettings(payload);
        setLangSettings((prev) => ({
          ...prev,
          [lang]: !prev[lang],
        }));
      }
    } catch (error) {
      console.error("Error updating settings:", error);
      setLangSettings(previousSettings); // Revert to previous settings on error
    } finally {
      setLoadingSwitch("");
    }
  };

  return (
    <div className={`w-full h-full ${roboto.className}`}>
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        {/* Heading */}
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Settings
        </h1>
      </div>
      <div className="flex flex-col py-6 px-4">
        <p>Choose the language(s) you wish to get notifications for:</p>
        <div className="space-y-3 ">
          <div className="flex items-center mt-2 space-x-2">
            <Switch
              checked={langSettings.english}
              onChange={() => handleToggle("english")}
              style={{
                backgroundColor: langSettings.english ? "#f97316" : undefined, // Tailwind orange-500
              }}
              loading={loadingSwitch === "english"}
            />
            <span>English</span>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              checked={langSettings.hindi}
              onChange={() => handleToggle("hindi")}
              style={{
                backgroundColor: langSettings.hindi ? "#f97316" : undefined,
              }}
              loading={loadingSwitch === "hindi"}
            />
            <span>Hindi</span>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              checked={langSettings.bengali}
              onChange={() => handleToggle("bengali")}
              style={{
                backgroundColor: langSettings.bengali ? "#f97316" : undefined,
              }}
              loading={loadingSwitch === "bengali"}
            />{" "}
            <span>Bengali</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
