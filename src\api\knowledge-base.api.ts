import { fetch } from "@/src/libs/helper";

export interface SearchQueryParams {
    query: string;
    from?: number;
    size?: number;
    language?: string;
    year?: string;
    country?: string;
    place?: string;
    category?: string;
    month?: string;
    sort?: string;
}

export interface TranscriptionDetailParams {
    id: number | string;
    query?: string;
}

export interface TranscriptionItem {
    id: number;
    count: number;
    title: string;
    transcription: string[];
    thumbnail: string;
    length: number;
    description: string;
    language_main: string;
    tags: string;
    lengthType: string;
    dateOfRecording: string;
    place: string;
    category: string;
}

export interface TranscriptionDetailItem {
    id: number;
    title: string;
    transcription: string[];
    thumbnail: string;
    length: number;
    description: string;
    language_main: string;
    tags: string;
    lengthType: string;
    dateOfRecording: string;
    place: string;
    category: string;
    resources_audios?: {
        downloads: number;
        creationTimestamp: any;
        lastModifiedTimestamp: any;
        views: number;
        url: string;
    }[];
    hasTranscription: boolean;
}

export interface BookItem {
    id: string;
    title: string;
    highlights: string[];
}

export interface BookParagraph {
    type: string;
    text: string;
    paragraphNumber: number;
    chapterTitle: string;
    bookTitle: string;
    topicTitle: string | null;
    subtopicTitle: string | null;
}

export interface TranscriptionSearchResponse {
    status: number;
    data: {
        transcriptions: TranscriptionItem[];
        total: number;
    };
    error: null | string;
}

export interface BookSearchResponse {
    status: number;
    data: {
        books: BookItem[];
        total: number;
    };
    error: null | string;
}

export type SearchResult = TranscriptionItem | BookItem;

// GET /search
export const getTranscriptionSearchResults = async (
    queryParams: SearchQueryParams,
    authorization: string | null
): Promise<TranscriptionSearchResponse> => {
    return fetch({
        url: "/search",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

// GET /books/search
export const getBooksSearchResults = async (
    queryParams: SearchQueryParams,
    authorization: string | null
): Promise<BookSearchResponse> => {
    return fetch({
        url: "/books/search",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export interface TranscriptionDetailResponse {
    status: number;
    data: TranscriptionDetailItem;
    error: null | string;
}

export interface BookDetailParams {
    id: string;
    query?: string;
}

export interface BookDetailResponse {
    status: number;
    data: BookParagraph[];
    error: null | string;
}

// GET /transcription/{id}
export const getTranscriptionDetail = async (
    params: TranscriptionDetailParams,
    authorization: string | null
): Promise<TranscriptionDetailResponse> => {
    return fetch({
        url: `/transcription/${params.id}`,
        method: "GET",
        params: params.query ? { query: params.query } : undefined,
        headers: {
            Authorization: authorization,
        },
    });
};

// GET /books/{book-id}/paragraphs
export const getBookParagraphs = async (
    params: BookDetailParams,
    authorization: string | null
): Promise<BookDetailResponse> => {
    return fetch({
        url: `/books/${params.id}/paragraphs`,
        method: "GET",
        params: params.query ? { query: params.query } : undefined,
        headers: {
            Authorization: authorization,
        },
    });
};

export interface SuggestedEditPayload {
    transcriptionId: number;
    timestamp: string;
    originalText: string;
    suggestedText: string;
    reason?: string;
}

export interface SuggestedEditResponse {
    status: number;
    data: {
        message: string;
        id?: number;
    };
    error: null | string;
}

// POST /transcriptions/{transcription-id}/suggested-edits
export const submitSuggestedEdit = async (
    payload: SuggestedEditPayload,
    authorization: string | null
): Promise<SuggestedEditResponse> => {
    return fetch({
        url: `/transcriptions/${payload.transcriptionId}/suggested-edits`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};
