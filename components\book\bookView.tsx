"use client";
import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Poppin<PERSON> } from "next/font/google";
import { BookParagraph } from "@/src/api/knowledge-base.api";
import BookContent from "./bookContent";
import { IoArrowBack } from "react-icons/io5";
import { FiShare2 } from "react-icons/fi";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface BookViewProps {
    bookParagraphs: BookParagraph[];
    bookTitle: string;
    searchQuery?: string;
}

const BookView = ({
    bookParagraphs,
    bookTitle,
    searchQuery,
}: BookViewProps) => {
    const router = useRouter();
    const searchParams = useSearchParams();

    // Get search query from URL if not provided as prop
    const query = searchQuery || searchParams.get("search") || "";

    // Handle back button click
    const handleBackClick = () => {
        router.push(
            `/knowledge-base${
                query ? `?search=${encodeURIComponent(query)}&activeTab=books` : "?activeTab=books"
            }`
        );
    };

    // Handle share button click
    const handleShare = () => {
        if (navigator.share) {
            navigator
                .share({
                    title: bookTitle,
                    url: window.location.href,
                })
                .then(() => console.log("Successful share"))
                .catch((error) => console.log("Error sharing", error));
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            alert("Link copied to clipboard!");
        }
    };

    return (
        <div className={`w-full h-full ${poppins.className}`}>
            {/* Book content */}
            <div className="pt-4">
                {/* Title with back arrow */}
                <div className="flex items-center mb-14 px-4 sm:px-8">
                    <button
                        type="button"
                        onClick={handleBackClick}
                        className="mr-3 text-primary text-opacity-90 hover:text-opacity-100 transition-colors"
                        aria-label="Go back"
                    >
                        <IoArrowBack size={24} />
                    </button>
                    <h1 className="text-2xl sm:text-3xl font-bold text-primary">
                        {bookTitle}
                    </h1>
                </div>

                {/* Metadata pills */}
                <div className="h-[calc(100vh-170px)] overflow-y-auto scrollbar px-4 sm:px-8">
                    <BookContent
                        bookParagraphs={bookParagraphs}
                        searchQuery={query}
                    />
                </div>
            </div>
        </div>
    );
};

export default BookView;
