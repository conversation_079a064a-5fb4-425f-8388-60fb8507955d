import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Modal, Button, Input, Select, message, Segmented } from "antd";
import { Poppins } from "next/font/google";
import SearchIcon from "../ui/searchIcon";
import { PlaylistType } from "@/src/libs/constant";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import { addToPrivatePlaylist, addToPublicPlaylist, createPlaylist, fetchUserPrivatePlaylists, fetchUserPublicPlaylists } from "@/src/services/playlist.service";
import IconCheck from "../ui/iconCheck";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const { TextArea } = Input;

const AddToPlaylistModal = ({ isModalOpen, setIsModalOpen, selectedFiles }: any) => {
  const [search, setSearch] = useState("");
  const [isCreatePlaylistOpen, setIsCreatePlaylistOpen] = useState(false);
  const [playlistName, setPlaylistName] = useState("");
  const [category, setCategory] = useState("");
  const [description, setDescription] = useState("");
  const [listType, setListType] = useState("PRIVATE");
  const [selectOpen, setSelectOpen] = useState(false);
  const [openedPlaylistType, setOpenedPlaylistType] = useState("Public");
  const [isLoadingPlaylists, setIsLoadingPlaylists] = useState(true);
  const [PublicPlaylists, setPublicPlaylists] = useState<any>([]);
  const [privatePlaylists, setPrivatePlaylists] = useState<any>([]);
  const [selectedList, setSelectedList] = useState<any>(null);
  const [btnLoading, setBtnLoading] = useState(false);

  const handleCancel = () => {
    setOpenedPlaylistType("Public");
    setSelectedList(null);
    setIsCreatePlaylistOpen(false);
    setIsModalOpen(false);
    setPlaylistName("")
    setCategory("")
    setDescription("")
  };

  const handleChange = (value: string) => {
    setListType(value);
  };

  // Create Select options from PlaylistType
  const listTypeOptions = PlaylistType.map((item) => ({
    value: item.value,
    label: item.label,
  }));

  useEffect(() => {
    if (isModalOpen) {
      fetchPlaylists();
    }
  }, [isModalOpen]);

  const fetchPlaylists = async () => {
    setIsLoadingPlaylists(true);
    await Promise.all([fetchPublicPlaylists(), fetchPrivatePlaylists()]);
    setIsLoadingPlaylists(false);
  };

  const fetchPublicPlaylists = async () => {
    try {
      const res = await fetchUserPublicPlaylists();
      if (res) {
        setPublicPlaylists(res);
      }
    } catch (error) {
      console.error("Error fetching public playlists: ", error);
    }
  };

  const fetchPrivatePlaylists = async () => {
    try {
      const res = await fetchUserPrivatePlaylists();
      if (res) {
        setPrivatePlaylists(res);
      }
    } catch (error) {
      console.error("Error fetching private playlists: ", error);
    }
  };

  const handlePlaylistTypeChange = (value: string) => {
    setSearch("");
    setOpenedPlaylistType(value);
  };

  const addToPlaylist = async () => {
    try {
      setBtnLoading(true);
      const filesArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles];
      const selectedFileIds = filesArray.map((file: any) => file?.id);

      const playlist =
        openedPlaylistType === "Public"
          ? PublicPlaylists.find((list: any) => list.listID === selectedList)
          : privatePlaylists.find((list: any) => list.listID === selectedList);

      const lectureIds = Array.from(
        new Set(playlist?.lectureIds?.concat(selectedFileIds))
      );

      const payload = {
        lastUpdate: Date.now(),
        lectureIds: lectureIds,
        lectureCount: lectureIds.length,
      };

      const res: any =
        openedPlaylistType === "Public"
          ? await addToPublicPlaylist(selectedList, payload)
          : await addToPrivatePlaylist(selectedList, payload);

      if (res) {
        message.success("Successfully added lectures to the playlist.");
        setIsModalOpen(false)
      }
    } catch (error) {
      message.error(
        "There was a problem adding the lecture(s) to the playlist. Try again in a moment."
      );
    } finally {
      setBtnLoading(false);
    }
  };

  const createPlaylistAndAddLectures = async () => {
    try {
      const missingFields = [];
      if (!playlistName.trim()) missingFields.push("playlist name");
      if (!category.trim()) missingFields.push("category");
      if (!description.trim()) missingFields.push("description");
      if (missingFields.length > 0) {
        message.warning(
          `Please enter ${missingFields.join(", ")} to continue.`
        );
        return;
      }

      const filesArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles];

      setBtnLoading(true);
      const payload = {
        title: playlistName,
        lecturesCategory: category,
        discription: description,
        lectureCount: filesArray.length,
        lectureIds: filesArray.map((file: any) => file?.id),
        listType: listType === "PUBLIC" ? "Public" : "Private",
        thumbnail: "",
      };

      const res: any = await createPlaylist(listType, payload);

      if (res) {
        message.success("Successfully added lectures to the playlist.");
        handleCancel()
      }
    } catch (error) {
      message.error(
        "There was a problem creating the playlist. Try again in a moment."
      );
    } finally {
      setBtnLoading(false);
    }
  };

  const filteredPublicPlaylists = PublicPlaylists.filter((list: any) =>
    list?.title?.toLowerCase().includes(search.toLowerCase())
  );

  const filteredPrivatePlaylists = privatePlaylists.filter((list: any) =>
    list?.title?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <Modal
        title={
          <h1
            className={`text-[24px] leading-8 font-[600] ${poppins.className}`}
          >
            {!isCreatePlaylistOpen ? "Add to playlist" : "Create new playlist"}
          </h1>
        }
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        centered
        width={346}
      >
        {!isCreatePlaylistOpen ? (
          <>
            <Segmented
              options={["Public", "Private"]}
              className="mt-4 rounded-[12px] playlist-popup"
              block
              value={openedPlaylistType}
              onChange={(value) => handlePlaylistTypeChange(value)}
            />
            <Input
              className="h-[34px] w-full my-4 !rounded-medium !text-sm !shadow-none !outline-none hover:!border-primary focus:!border-primary"
              placeholder="Search"
              onChange={(e) => setSearch(e.target.value)}
              value={search}
              prefix={
                <div className="pr-1">
                  <SearchIcon />
                </div>
              }
              disabled={
                (openedPlaylistType === "Public" &&
                  PublicPlaylists.length === 0) ||
                (openedPlaylistType === "Private" &&
                  privatePlaylists.length === 0)
              }
              allowClear
            />
            {isLoadingPlaylists && (
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary my-4"></div>
              </div>
            )}
            {!isLoadingPlaylists &&
              openedPlaylistType === "Public" &&
              filteredPublicPlaylists.length === 0 && (
                <p
                  className={`text-[13px] leading-6 font-[500] text-left pl-2 text-text-primary pb-[2px] mb-2 ${poppins.className}`}
                >
                  {search
                    ? "No public playlists found."
                    : "You don't have any public playlists yet. "}
                  <br />
                  {!search && "Create one now!"}
                </p>
              )}
            {!isLoadingPlaylists &&
              openedPlaylistType === "Public" &&
              filteredPublicPlaylists.length > 0 && (
                <div className="flex flex-col gap-1 max-h-[150px] overflow-y-auto scrollbar-mini">
                  {filteredPublicPlaylists.map((list: any) => {
                    return (
                      <div
                        key={list?.listID}
                        onClick={() => setSelectedList(list?.listID)}
                        className={`px-4 py-2 rounded-[10px] cursor-pointer transition-all flex justify-between ${
                          selectedList === list?.listID
                            ? "bg-primary-light"
                            : "hover:bg-primary-hover"
                        }`}
                      >
                        <p>{list?.title}</p>
                        {selectedList === list?.listID && (
                          <IconCheck color="var(--primary-color)" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            {!isLoadingPlaylists &&
              openedPlaylistType === "Private" &&
              filteredPrivatePlaylists.length === 0 && (
                <p
                  className={`text-[13px] leading-6 font-[500] text-left pl-2 text-text-primary pb-[2px] mb-2 ${poppins.className}`}
                >
                  {search
                    ? "No private playlists found."
                    : "You don't have any private playlists yet. "}
                  <br />
                  {!search && "Create one now!"}
                </p>
              )}{" "}
            {!isLoadingPlaylists &&
              openedPlaylistType === "Private" &&
              filteredPrivatePlaylists.length > 0 && (
                <div className="flex flex-col gap-1 max-h-[150px] overflow-y-auto scrollbar">
                  {filteredPrivatePlaylists.map((list: any) => {
                    return (
                      <div
                        key={list?.listID}
                        onClick={() => setSelectedList(list?.listID)}
                        className={`px-4 py-2 rounded-[10px] cursor-pointer transition-all flex justify-between ${
                          selectedList === list?.listID
                            ? "bg-primary-light"
                            : "hover:bg-primary-hover"
                        }`}
                      >
                        <p>{list?.title}</p>
                        {selectedList === list?.listID && (
                          <IconCheck color="var(--primary-color)" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            <div
              className="w-[160px] flex gap-1 items-center cursor-pointer hover:bg-primary-hover px-2 py-1 rounded-[12px] transition-all duration-200 my-2"
              onClick={() => {
                if (openedPlaylistType === "Public") {
                  setListType("PUBLIC")
                } else {
                  setListType("PRIVATE")
                }
                setIsCreatePlaylistOpen(true)
              }}
            >
              <Image
                src="/images/helperComponents/IconAdd.svg"
                width={22}
                height={22}
                alt=""
                className={`w-[22px] h-[22px]`}
              />
              <h2 className="text-[13px] leading-6 font-[400] text-text-primary pb-[2px]">
                Create New Playlist
              </h2>
            </div>
            <div className="flex justify-end">
              <Button
                className="text-[15px] leading-6 font-[500] !text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50"
                disabled={
                  (!PublicPlaylists && !privatePlaylists) || !selectedList
                }
                loading={btnLoading}
                onClick={addToPlaylist}
              >
                Add
              </Button>
            </div>
          </>
        ) : (
          <div className="flex flex-col gap-4 mt-6">
            <Input
              className="h-[40px] w-full !rounded-medium !text-sm !shadow-none !outline-none hover:!border-primary focus:!border-primary"
              placeholder="Playlist name"
              onChange={(e) => setPlaylistName(e.target.value)}
              value={playlistName}
            />

            <div className="flex gap-4 items-center">
              <Input
                className="h-[40px] w-[50%] !rounded-medium !text-sm !shadow-none !outline-none hover:!border-primary focus:!border-primary"
                placeholder="Category"
                onChange={(e) => setCategory(e.target.value)}
                value={category}
              />
              <Select
                className="h-[40px] w-[50%]"
                value={listType}
                onChange={handleChange}
                options={listTypeOptions}
                onDropdownVisibleChange={(open) => setSelectOpen(open)}
                suffixIcon={
                  <div className="flex items-center">
                    <Image
                      src="/images/helperComponents/arrow.svg"
                      width={16}
                      height={16}
                      alt=""
                      className={`cursor-pointer transform transition-transform duration-300 ${
                        selectOpen ? "rotate-180" : "rotate-0"
                      }`}
                    />
                  </div>
                }
                popupClassName={`${poppins.className}`}
              />
            </div>

            <TextArea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Description"
              style={{ height: 120, resize: "none", borderRadius: "12px" }}
              className="py-2 px-3 !shadow-none !outline-none hover:!border-primary focus:!border-primary"
            />

            <div className="flex justify-end gap-1">
              <Button
                className="text-[15px] leading-6 font-[500] !text-text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50"
                onClick={() => setIsCreatePlaylistOpen(false)}
              >
                Back
              </Button>
              <Button
                className="text-[15px] leading-6 font-[500] !text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50"
                onClick={createPlaylistAndAddLectures}
                loading={btnLoading}
              >
                Add
              </Button>
            </div>
          </div>
        )}
      </Modal>
  );
};

export default AddToPlaylistModal;
