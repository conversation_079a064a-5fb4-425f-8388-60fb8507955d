"use client";
import React, { createContext, useContext, useState } from "react";

const SearchContext = createContext();

export const SearchContextProvider = ({ children }) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [search, setSearch] = useState(false);
    const [deepSearch, setDeepSearch] = useState(false);
    const [isSearching, setIsSearching] = useState(false);

    const resetSearchStates = () => {
        setSearchQuery("");
        setSearch(false);
        setDeepSearch(false);
        setIsSearching(false);
    };

    return (
        <SearchContext.Provider
            value={{
                searchQuery,
                setSearchQuery,
                search,
                setSearch,
                deepSearch,
                setDeepSearch,
                isSearching,
                setIsSearching,
                resetSearchStates,
            }}
        >
            {children}
        </SearchContext.Provider>
    );
};

export const useSearchContext = () => {
    return useContext(SearchContext);
};
