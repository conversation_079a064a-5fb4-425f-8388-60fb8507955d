import { collection, getDocs } from "firebase/firestore";
import { db } from "../config/firebase.config";

// export const fetchItinerary = async (): Promise<any[]> => {
//   try {
//     const querySnapshot = await getDocs(collection(db, "itinerary"));
//     const items = querySnapshot.docs.map((doc) => ({
//       id: doc.id,
//       ...doc.data(),
//     }));

//     const now = new Date().getTime();

//     const futureItems = items
//       .filter((item: any) => item.period.endDate >= now)
//       .sort((a: any, b: any) => a.period.startDate - b.period.startDate);

//     console.log("Fetched Itinerary:", futureItems);
//     return futureItems;
//   } catch (error) {
//     console.error("Error fetching itinerary:", error);
//     throw error;
//   }
// };

export const fetchItinerary = async (): Promise<any[]> => {
  try {
    const querySnapshot = await getDocs(collection(db, "itinerary"));
    const items = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    // Sort all items by startDate (past + future)
    const sortedItems = items.sort(
      (a: any, b: any) => a.period.startDate - b.period.startDate
    );

    console.log("Fetched All Itinerary:", sortedItems);
    return sortedItems;
  } catch (error) {
    console.error("Error fetching itinerary:", error);
    throw error;
  }
};

