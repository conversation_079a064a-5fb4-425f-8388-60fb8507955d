import { collection, doc, getDocs, query, setDoc, where, addDoc } from "firebase/firestore";
import { db } from "../config/firebase.config";
import { getLectureById, updateLecture, Lecture } from "./indexedDB.service";

/**
 * Process a single lecture to Mark lecture as completed
 *
 * @param lectureInput - Either a lecture ID or a lecture object to add to favourites
 * @param userId - The current user ID
 * @returns The lecture ID if successful, null if failed
 */
const processSingleLectureMarkAsCompleted = async (
  lectureInput: number | string | Lecture,
  userId: string
): Promise<number | string | null> => {
  try {
    // Step 1: Get the lecture object
    let lecture: Lecture | undefined;
    let lectureId: number | string;

    if (typeof lectureInput === 'object') {
      // If a lecture object was provided, use it directly
      lecture = lectureInput;
      lectureId = lecture.id;
    } else {
      // If only an ID was provided, fetch the lecture from IndexedDB
      lectureId = lectureInput;
      lecture = await getLectureById(lectureId);
      if (!lecture) {
        // console.warn(`Lecture with ID ${lectureId} not found in IndexedDB`);
        return null;
      }
    }

    const currentTimestamp = Date.now();
    let documentPath: string;

    // Step 2: Check if the lecture has a documentId
    if (lecture.documentId) {
      // Case 1: documentId exists - Update both IndexedDB and Firestore
      documentPath = lecture.documentPath || `users/${userId}/lectureInfo/${lecture.documentId}`;

      // Update in IndexedDB
      await updateLecture({
        ...lecture,
        isCompleted: true,
        lastModifiedTimestamp: currentTimestamp
      });

      // Update in Firestore
      const docRef = doc(db, documentPath);
      await setDoc(docRef, {
        isCompleted: true,
        lastModifiedTimestamp: currentTimestamp
      }, { merge: true });

      return lectureId;
    } else {
      // Case 2: No documentId - Check Firestore for existing document
      const subCollectionRef = collection(db, `users/${userId}/lectureInfo`);
      const q = query(subCollectionRef, where("id", "==", lectureId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        // Found document in Firestore - Update it and merge with IndexedDB
        const docRef = querySnapshot.docs[0];
        documentPath = docRef.ref.path;
        const documentId = docRef.id;

        // Update in Firestore
        await setDoc(docRef.ref, {
          isCompleted: true,
          lastModifiedTimestamp: currentTimestamp
        }, { merge: true });

        // Merge Firestore document with IndexedDB
        const firestoreData = docRef.data();
        await updateLecture({
          ...lecture,
          ...firestoreData,
          documentId,
          documentPath,
          isCompleted: true,
          lastModifiedTimestamp: currentTimestamp
        });

        return lectureId;
      } else {
        // No document in Firestore - Create a new one
        // First, create a new document reference (but don't add data yet)
        const newDocRef = doc(subCollectionRef);

        // Prepare the payload with documentId and documentPath
        const payload = {
          id: lectureId,
          completed: false,
          creationTimestamp: currentTimestamp,
          downloadPlace: 0,
          downloaded: false,
          // favourite: true,
          isFavourite: false,
          isCompleted: true,
          favouritePlace: 0,
          inPrivateList: false,
          inPublicList: false,
          lastModifiedTimestamp: currentTimestamp,
          lastPlayedPoint: 0,
          totalPlayedNo: 0,
          totalPlayedTime: 0,
          totallength: lecture.length || 0,
          documentId: newDocRef.id,
          documentPath: newDocRef.path
        };

        // Add the payload to Firestore using setDoc
        await setDoc(newDocRef, payload);

        documentPath = newDocRef.path;
        const documentId = newDocRef.id;

        // Update in IndexedDB with the new document info
        await updateLecture({
          ...lecture,
          documentId,
          documentPath,
          completed: lecture.completed || false,
          creationTimestamp: lecture.creationTimestamp || currentTimestamp,
          downloadPlace: lecture.downloadPlace || 0,
          downloaded: lecture.downloaded || false,
          isCompleted: true,
          isFavourite: false,
          favouritePlace: lecture.favouritePlace || 0,
          inPrivateList: lecture.inPrivateList || false,
          inPublicList: lecture.inPublicList || false,
          lastModifiedTimestamp: currentTimestamp,
          lastPlayedPoint: lecture.totallength,
          totalPlayedNo: lecture.totalPlayedNo || 0,
          totalPlayedTime: lecture.totalPlayedTime || 0,
          totallength: lecture.length || 0
        });

        return lectureId;
      }
    }
  } catch (error) {
    console.error(`Error removing lecture from complete:`, error);
    return null;
  }
};

/**
 * Mark lecture as completed in parallel, syncing between IndexedDB and Firestore
 *
 * Flow for each lecture:
 * 1. If lecture object is provided, use it directly; otherwise fetch from IndexedDB by ID
 * 2. If the lecture has a 
 * , update both IndexedDB and Firestore
 * 3. If no documentId, check Firestore for an existing document
 * 4. If found in Firestore, update it and merge with IndexedDB
 * 5. If not found in Firestore, create a new document and update IndexedDB
 *
 * @param lectures - Array of lecture objects, lecture IDs, or a single lecture object/ID to add to favourites
 * @returns Array of successfully processed lecture IDs
 */
export const markAsCompleted = async (
  lectures: (Lecture | number | string)[] | Lecture | number | string
): Promise<(number | string)[]> => {
  try {
    const userId = localStorage.getItem("firebaseUid");
    if (!userId) {
      throw new Error("User not logged in");
    }

    // Convert single lecture/ID to array if needed
    const lecturesArray = Array.isArray(lectures) ? lectures : [lectures];

    // Process all lectures in parallel
    const results = await Promise.all(
      lecturesArray.map(lecture => processSingleLectureMarkAsCompleted(lecture, userId))
    );

    // Filter out null values (failed operations) and return successful IDs
    return results.filter((id): id is number | string => id !== null);
  } catch (error) {
    console.error("Error removing from completed: ", error);
    throw new Error("Failed to remove from complted");
  }
};

/**
 * Process a single lecture to Mark lecture as not completed
 *
 * @param lectureInput - Either a lecture ID or a lecture object to add to favourites
 * @param userId - The current user ID
 * @returns The lecture ID if successful, null if failed
 */
const processSingleLectureToRemoveFromCompleted = async (
  lectureInput: number | string | Lecture,
  userId: string
): Promise<number | string | null> => {
  try {
    // Step 1: Get the lecture object
    let lecture: Lecture | undefined;
    let lectureId: number | string;

    if (typeof lectureInput === 'object') {
      // If a lecture object was provided, use it directly
      lecture = lectureInput;
      lectureId = lecture.id;
    } else {
      // If only an ID was provided, fetch the lecture from IndexedDB
      lectureId = lectureInput;
      lecture = await getLectureById(lectureId);
      if (!lecture) {
        // console.warn(`Lecture with ID ${lectureId} not found in IndexedDB`);
        return null;
      }
    }

    const currentTimestamp = Date.now();
    let documentPath: string;

    // Step 2: Check if the lecture has a documentId
    if (lecture.documentId) {
      // Case 1: documentId exists - Update both IndexedDB and Firestore
      documentPath = lecture.documentPath || `users/${userId}/lectureInfo/${lecture.documentId}`;

      // Update in IndexedDB
      await updateLecture({
        ...lecture,
        isCompleted: false,
        lastPlayedPoint: 0,
        lastModifiedTimestamp: currentTimestamp
      });

      // Update in Firestore
      const docRef = doc(db, documentPath);
      await setDoc(docRef, {
        isCompleted: false,
        lastPlayedPoint: 0,
        lastModifiedTimestamp: currentTimestamp
      }, { merge: true });

      return lectureId;
    } else {
      // Case 2: No documentId - Check Firestore for existing document
      const subCollectionRef = collection(db, `users/${userId}/lectureInfo`);
      const q = query(subCollectionRef, where("id", "==", lectureId));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        // Found document in Firestore - Update it and merge with IndexedDB
        const docRef = querySnapshot.docs[0];
        documentPath = docRef.ref.path;
        const documentId = docRef.id;

        // Update in Firestore
        await setDoc(docRef.ref, {
          isCompleted: false,
          lastPlayedPoint: 0,
          lastModifiedTimestamp: currentTimestamp
        }, { merge: true });

        // Merge Firestore document with IndexedDB
        const firestoreData = docRef.data();
        await updateLecture({
          ...lecture,
          ...firestoreData,
          documentId,
          documentPath,
          isCompleted: false,
          lastPlayedPoint: 0,
          lastModifiedTimestamp: currentTimestamp
        });

        return lectureId;
      } else {
        // No document in Firestore - Create a new one
        // First, create a new document reference (but don't add data yet)
        const newDocRef = doc(subCollectionRef);

        // Prepare the payload with documentId and documentPath
        const payload = {
          id: lectureId,
          completed: false,
          creationTimestamp: currentTimestamp,
          downloadPlace: 0,
          downloaded: false,
          // favourite: true,
          isFavourite: false,
          isCompleted: false,
          favouritePlace: 0,
          inPrivateList: false,
          inPublicList: false,
          lastModifiedTimestamp: currentTimestamp,
          lastPlayedPoint: 0,
          totalPlayedNo: 0,
          totalPlayedTime: 0,
          totallength: lecture.length || 0,
          documentId: newDocRef.id,
          documentPath: newDocRef.path
        };

        // Add the payload to Firestore using setDoc
        await setDoc(newDocRef, payload);

        documentPath = newDocRef.path;
        const documentId = newDocRef.id;

        // Update in IndexedDB with the new document info
        await updateLecture({
          ...lecture,
          documentId,
          documentPath,
          completed: lecture.completed || false,
          creationTimestamp: lecture.creationTimestamp || currentTimestamp,
          downloadPlace: lecture.downloadPlace || 0,
          downloaded: lecture.downloaded || false,
          isCompleted: false,
          isFavourite: false,
          favouritePlace: lecture.favouritePlace || 0,
          inPrivateList: lecture.inPrivateList || false,
          inPublicList: lecture.inPublicList || false,
          lastModifiedTimestamp: currentTimestamp,
          lastPlayedPoint: 0,
          totalPlayedNo: lecture.totalPlayedNo || 0,
          totalPlayedTime: lecture.totalPlayedTime || 0,
          totallength: lecture.length || 0
        });

        return lectureId;
      }
    }
  } catch (error) {
    console.error(`Error marking lecture as completed:`, error);
    return null;
  }
};

/**
 * Mark lecture as not completed in parallel, syncing between IndexedDB and Firestore
 *
 * Flow for each lecture:
 * 1. If lecture object is provided, use it directly; otherwise fetch from IndexedDB by ID
 * 2. If the lecture has a documentId, update both IndexedDB and Firestore
 * 3. If no documentId, check Firestore for an existing document
 * 4. If found in Firestore, update it and merge with IndexedDB
 * 5. If not found in Firestore, create a new document and update IndexedDB
 *
 * @param lectures - Array of lecture objects, lecture IDs, or a single lecture object/ID to add to favourites
 * @returns Array of successfully processed lecture IDs
 */
export const removeFromCompleted = async (
  lectures: (Lecture | number | string)[] | Lecture | number | string
): Promise<(number | string)[]> => {
  try {
    const userId = localStorage.getItem("firebaseUid");
    if (!userId) {
      throw new Error("User not logged in");
    }

    // Convert single lecture/ID to array if needed
    const lecturesArray = Array.isArray(lectures) ? lectures : [lectures];

    // Process all lectures in parallel
    const results = await Promise.all(
      lecturesArray.map(lecture => processSingleLectureToRemoveFromCompleted(lecture, userId))
    );

    // Filter out null values (failed operations) and return successful IDs
    return results.filter((id): id is number | string => id !== null);
  } catch (error) {
    console.error("Error mark as completed: ", error);
    throw new Error("Failed to mark as completed");
  }
};