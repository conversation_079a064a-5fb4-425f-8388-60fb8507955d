import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Poppins } from "next/font/google";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const PlaylistCardSkeleton = () => {
  return (
    <div className="cursor-default">
      <div className={`w-full transition-all duration-500 relative`}>
        {/* Thumbnail skeleton */}
        <Skeleton 
          height={150} 
          width="100%" 
          borderRadius={10}
        />
      </div>
      <div className="flex gap-2">
        <div className="w-[calc(100%-26px)] flex flex-col gap-1 py-2">
          <Skeleton 
              width="90%" 
              height={18}
            />
          {/* Author skeleton */}
          <Skeleton 
            width="70%" 
            height={16}
          />
        </div>
      </div>
    </div>
  );
};

export default PlaylistCardSkeleton;
