<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_3508_9657)">
<rect x="10" y="10" width="24" height="24" rx="12" fill="white"/>
<circle cx="22" cy="22" r="6" fill="#FF7101"/>
</g>
<defs>
<filter id="filter0_d_3508_9657" x="0" y="0" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_3508_9657"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3508_9657"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3508_9657" result="shape"/>
</filter>
</defs>
</svg>
