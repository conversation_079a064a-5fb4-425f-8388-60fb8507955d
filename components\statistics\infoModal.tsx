"use client";

import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const InfoModal = ({ isOpen, onClose }: any) => {
  if (!isOpen) return null;

  const items = [
    { color: "#8BC34A", label: "Śrīmad-Bhāgavatam", short: "SB" },
    { color: "#EC4899", label: "Bhagavad-gītā", short: "BG" },
    { color: "#F59E0B", label: "Śrī C<PERSON>a-caritāmṛta", short: "CC" },
    { color: "#06B6D4", label: "Viṣṇu-sahasranāma", short: "VSN" },
    { color: "#6366F1", label: "Seminars", short: "SEMINARS" },
    { color: "#3B82F6", label: "Others", short: "OTHERS" },
  ];

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 ${poppins.className}`}>
      <div className="bg-white p-6 rounded-2xl w-[90%] max-w-sm shadow-lg">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Info</h2>
        <ul className="space-y-4">
          {items.map((item, i) => (
            <li key={i} className="flex items-center gap-3">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              ></span>
              <div className="flex flex-col">
                <span className="text-gray-500 text-xs">{item.short}</span>
                <span className="text-gray-900 font-medium">{item.label}</span>
              </div>
            </li>
          ))}
        </ul>
        <div className="text-right mt-6">
          <button
            onClick={onClose}
            className="text-primary font-semibold cursor-pointer"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default InfoModal;
