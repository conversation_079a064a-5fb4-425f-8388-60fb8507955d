"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { FiBookmark } from "react-icons/fi";
import { GrLocation } from "react-icons/gr";
import { MdOutlineDateRange } from "react-icons/md";
import { BookItem, TranscriptionItem } from "@/src/api/knowledge-base.api";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface SearchResultItemProps {
    result: TranscriptionItem | BookItem;
    searchQuery?: string;
    resultType: "transcription" | "book";
}

const SearchResultItem = ({
    result,
    searchQuery,
    resultType,
}: SearchResultItemProps) => {
    const router = useRouter();

    // Handle click on search result item
    const handleItemClick = () => {
        if (resultType === "transcription" && isTranscription(result)) {
            // Navigate to transcription detail page with search query
            router.push(`/knowledge-base/transcription/${result.id}${searchQuery ? `?search=${encodeURIComponent(searchQuery)}` : ""}`);
        } else if (resultType === "book" && isBook(result)) {
            // Navigate to book detail page with search query
            router.push(`/knowledge-base/book/${result.id}${searchQuery ? `?search=${encodeURIComponent(searchQuery)}` : ""}`);
        }
    };
    const highlightSearchTerm = (html: string, query: string) => {
        if (!query || !html) return html;
        try {
            const styledHtml = html.replace(
                /<em>(.*?)<\/em>/g,
                '<span class="bg-yellow-200 italic font-medium text-primary">$1</span>'
            );

            return styledHtml;
        } catch (error) {
            console.error("Error highlighting search term:", error);
            return html;
        }
    };

    // Check if result is a TranscriptionItem
    const isTranscription = (item: any): item is TranscriptionItem => {
        return "transcription" in item;
    };

    // Check if result is a BookItem
    const isBook = (item: any): item is BookItem => {
        return "highlights" in item;
    };

    return (
        <div
            onClick={handleItemClick}
            className={`p-3 sm:p-4 md:p-5 bg-primary bg-opacity-10 rounded-lg border border-primary border-opacity-50 border-b-primary cursor-pointer hover:shadow-md transition-shadow ${poppins.className}`}
        >
            {/* Title */}
            <h3 className="text-base sm:text-lg font-semibold text-primary mb-2">
                {result.title}
            </h3>

            {/* Category, Place and Date - Only for transcription results */}
            {resultType === "transcription" && isTranscription(result) && (
                <div className="flex flex-wrap items-center gap-1 sm:gap-2 mb-2 sm:mb-3 text-xs sm:text-sm">
                    {result.category && (
                        <div className="flex items-center">
                            <span className="inline-block mr-1">
                                <FiBookmark className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span className="text-gray-700">
                                {result.category}
                            </span>
                        </div>
                    )}

                    {result.place && (
                        <div className="flex items-center ml-1 sm:ml-2">
                            <span className="inline-block mr-1">
                                <GrLocation className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span className="text-gray-700">
                                {result.place}
                            </span>
                        </div>
                    )}

                    {result.dateOfRecording && (
                        <div className="flex items-center ml-1 sm:ml-2">
                            <span className="inline-block mr-1">
                                <MdOutlineDateRange className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span className="text-gray-700">
                                {result.dateOfRecording}
                            </span>
                        </div>
                    )}
                </div>
            )}

            {/* Content based on result type */}
            {resultType === "transcription" && isTranscription(result) && (
                <p className="text-gray-800 text-[13px] sm:text-[14px] md:text-[15px] leading-5 sm:leading-6">
                    {result.transcription &&
                        result.transcription.length > 0 && (
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: highlightSearchTerm(
                                        result.transcription[0],
                                        searchQuery || ""
                                    ),
                                }}
                            />
                        )}
                    ...
                </p>
            )}

            {resultType === "book" && isBook(result) && (
                <div className="text-gray-800 text-[13px] sm:text-[14px] md:text-[15px] leading-5 sm:leading-6">
                    {result.highlights && result.highlights.length > 0 && (
                        <ul className="list-disc pl-5 space-y-1">
                            {result.highlights.map((highlight, index) => (
                                <li key={index}>
                                    <span
                                        dangerouslySetInnerHTML={{
                                            __html: highlightSearchTerm(
                                                highlight,
                                                searchQuery || ""
                                            ),
                                        }}
                                    />
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            )}
        </div>
    );
};

export default SearchResultItem;
