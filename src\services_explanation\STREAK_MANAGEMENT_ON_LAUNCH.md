# Streak Management on Web App Launch

## Overview
This implementation adds comprehensive streak management functionality that runs when the web application launches (specifically when the navbar initializes). The system ensures streak accuracy by checking for gaps in user activity and handles edge cases for proper streak continuation.

## Key Features Implemented

### 1. Launch-Time Streak Validation
- **Function**: `checkAndResetStreakOnLaunch()`
- **Trigger**: Called when navbar component mounts (web app launch)
- **Purpose**: Validates current streak against time gaps and resets if necessary

### 2. Enhanced Streak Logic
- **Zero Streak Handling**: If currentStreak is 0 and user listens today, streak updates to 1
- **Gap Detection**: Automatically resets streak to 0 if more than 2 days have passed
- **Same-Day Activity**: Proper handling of multiple activities on the same day

### 3. Improved Sync and Caching
- **Statistics Cache**: 30-second cache to reduce Firebase calls
- **Cache Invalidation**: Automatic cache clearing when statistics are updated
- **Force Refresh**: Option to bypass cache when fresh data is needed

## Implementation Details

### Core Functions Added/Modified

#### 1. `checkAndResetStreakOnLaunch()`
```javascript
// Location: src/services/userStatistics.service.ts
// Purpose: Check and reset streak when web app launches
// Logic: If daysDifference > 2, reset currentStreak to 0
```

**Key Logic:**
- Calculates days between current date and lastUpdatedTime
- If gap > 2 days: Reset currentStreak to 0, update lastUpdatedTime
- If gap ≤ 2 days: Return current statistics unchanged
- Always preserves bestStreak value

#### 2. Enhanced `updateUserStatistics()`
```javascript
// Added special case for zero streak handling
if (daysDifference === 0) {
    if (currentStats.currentStreak === 0) {
        // If currentStreak is 0 and user is listening today, set streak to 1
        updatedStats = {
            ...currentStats,
            currentStreak: 1,
            bestStreak: Math.max(currentStats.bestStreak, 1),
            lastUpdatedTime: currentTimestamp,
        };
    } else {
        // Same day - just update timestamp, no streak changes
        updatedStats = {
            ...currentStats,
            lastUpdatedTime: currentTimestamp,
        };
    }
}
```

#### 3. Enhanced `fetchUserStatistics()` with Caching
```javascript
// Location: src/api/userStatistics.api.ts
// Added caching mechanism with 30-second duration
// Added forceRefresh parameter to bypass cache when needed
```

### Integration Points

#### 1. Navbar Component Integration
```javascript
// Location: components/navbar/navbar.tsx
// Added import: checkAndResetStreakOnLaunch
// Modified useEffect to call streak check on component mount

useEffect(() => {
    const initializeStreakAndStats = async () => {
        // First, check and reset streak if needed
        const checkedStats = await checkAndResetStreakOnLaunch();
        
        // Then fetch latest statistics with force refresh
        const stats = await fetchUserStatistics(true);
        // ... handle results
    };
    initializeStreakAndStats();
}, []);
```

#### 2. IndexedDB Context Enhancement
```javascript
// Location: src/context/indexedDB.context.tsx
// Added syncStatistics function for statistics data sync
// Integrated with existing sync functionality
```

#### 3. Statistics Dropdown Sync
```javascript
// Location: components/navbar/statisticsDropdown.tsx
// Enhanced backgroundSync to include data synchronization
// Ensures statistics are kept in sync with other data
```

## Usage Scenarios

### Scenario 1: User Returns After 3+ Days
1. **Launch**: Web app starts, navbar initializes
2. **Check**: `checkAndResetStreakOnLaunch()` detects 3-day gap
3. **Reset**: currentStreak set to 0, lastUpdatedTime updated
4. **Display**: UI shows streak as 0 with gray flame icon
5. **Activity**: When user listens to audio, streak updates from 0 to 1

### Scenario 2: User Returns Within 2 Days
1. **Launch**: Web app starts, navbar initializes
2. **Check**: `checkAndResetStreakOnLaunch()` detects 1-day gap
3. **Preserve**: Streak remains unchanged
4. **Display**: UI shows current streak with appropriate flame icon
5. **Activity**: Normal streak continuation logic applies

### Scenario 3: Daily Active User
1. **Launch**: Web app starts multiple times per day
2. **Check**: `checkAndResetStreakOnLaunch()` detects same-day activity
3. **Maintain**: No changes to streak
4. **Cache**: Subsequent calls use cached data for performance
5. **Activity**: Same-day activities only update timestamp

## Error Handling

### 1. Network Failures
- Graceful degradation when Firebase is unavailable
- Fallback to cached data when possible
- Non-blocking errors that don't break app functionality

### 2. Authentication Issues
- Proper handling when user is not logged in
- Returns null without throwing errors
- Logs warnings for debugging purposes

### 3. Data Inconsistencies
- Handles edge cases like negative day differences
- Validates data structure before processing
- Creates default statistics when none exist

## Performance Optimizations

### 1. Caching Strategy
- **Duration**: 30-second cache for statistics data
- **Invalidation**: Automatic clearing when data is updated
- **Bypass**: Force refresh option for critical updates

### 2. Efficient Firebase Usage
- Minimal writes only when streak changes occur
- Batch operations where possible
- Proper error handling to avoid unnecessary retries

### 3. UI Responsiveness
- Non-blocking async operations
- Loading states for better user experience
- Fallback data to prevent empty states

## Testing

### Test Coverage
- **Unit Tests**: Core streak logic functions
- **Integration Tests**: Complete flow from launch to activity
- **Edge Cases**: Error conditions and boundary values
- **Performance Tests**: Cache behavior and Firebase optimization

### Test File Location
```
src/services/__tests__/userStatistics.test.js
```

## Configuration

### Cache Settings
```javascript
const CACHE_DURATION = 30000; // 30 seconds
```

### Streak Reset Threshold
```javascript
// Reset streak if more than 2 days have passed
if (daysDifference > 2) {
    // Reset logic
}
```

## Audio/Video Player Integration for Statistics Sync

### Enhanced 60-Second Sync Integration
The statistics sync functionality has been integrated with the existing audio and video player services that already update every 60 seconds during playback.

#### Audio Player Integration
```javascript
// Location: src/services/audioPlayerServices/audioPlayerActivity.service.ts
// Enhanced updateUserListeningActivity functions

// Update user statistics (streak tracking) and sync statistics data every 60 seconds
try {
    await trackUserStatistics();
    // Also fetch latest statistics to keep data in sync
    await fetchUserStatistics();
} catch (error) {
    console.error("Error updating user statistics:", error);
    // Don't throw error here to avoid breaking the main listening activity tracking
}
```

#### Video Player Integration
```javascript
// Location: src/services/videoPlayerServices/videoPlayerActivity.service.ts
// Enhanced updateUserVideoWatchingActivity functions

// Update user statistics (streak tracking) and sync statistics data every 60 seconds
try {
    await trackUserStatistics();
    // Also fetch latest statistics to keep data in sync
    await fetchUserStatistics();
} catch (error) {
    console.error("Error updating user statistics:", error);
    // Don't throw error here to avoid breaking the main video watching activity tracking
}
```

#### Context-Level Sync Functions
Both audio and video contexts now provide manual sync capabilities:

```javascript
// Audio Context
const { syncStatistics } = useAudioContext();
await syncStatistics(); // Manually sync statistics

// Video Context
const { syncStatistics } = useVideoContext();
await syncStatistics(); // Manually sync statistics
```

### Sync Flow Integration

#### Automatic Sync (Every 60 Seconds)
1. **Audio/Video Playback**: User plays audio or video content
2. **Activity Tracking**: Service updates listening/watching activity
3. **Streak Update**: `trackUserStatistics()` updates streak logic
4. **Statistics Sync**: `fetchUserStatistics()` ensures fresh data
5. **UI Update**: Components automatically reflect updated statistics

#### Manual Sync (On Demand)
1. **Context Method**: Call `syncStatistics()` from audio/video context
2. **Direct API**: Call `fetchUserStatistics()` directly
3. **IndexedDB Context**: Use `syncStatistics()` from IndexedDB context

### Benefits of This Integration

#### 1. Real-Time Data Consistency
- Statistics are synced every 60 seconds during active usage
- No additional network calls - leverages existing update intervals
- Ensures UI always shows current streak information

#### 2. Performance Optimization
- Piggybacks on existing 60-second intervals
- No separate timers or intervals needed
- Minimal performance impact

#### 3. Error Resilience
- Statistics sync errors don't break audio/video functionality
- Graceful degradation when sync fails
- Non-blocking error handling

#### 4. Multiple Sync Points
- Automatic sync during playback
- Manual sync via context methods
- Launch-time sync via navbar initialization
- Background sync via statistics dropdown

## Future Enhancements

### Potential Improvements
1. **Configurable Reset Threshold**: Allow customization of 2-day limit
2. **Streak Recovery**: Grace period for streak recovery
3. **Offline Support**: Handle streak updates when offline
4. **Analytics**: Track streak reset patterns for insights
5. **Notifications**: Remind users before streak expires
6. **Smart Sync**: Only sync when data has actually changed
7. **Batch Sync**: Combine multiple sync operations

### Performance Optimizations
1. **Background Sync**: Update streaks in background
2. **Batch Updates**: Combine multiple statistics operations
3. **Local Storage**: Cache statistics locally for offline access
4. **Service Worker**: Handle streak updates in service worker
5. **Debounced Sync**: Prevent excessive sync calls
6. **Conditional Sync**: Only sync when necessary

## Monitoring and Debugging

### Console Logs
- Streak reset events with day gap information
- Audio/Video context sync confirmations
- Error conditions with detailed context
- Sync timing and frequency information

### Debug Information
- Current streak values in navbar component
- Sync success/failure rates from audio/video contexts
- Firebase operation success/failure rates
- Performance metrics for sync operations

### Integration Points Summary
1. **Launch Check**: Navbar initialization with streak validation
2. **Playback Sync**: 60-second intervals during audio/video playback
3. **Manual Sync**: Context methods for on-demand synchronization
4. **Background Sync**: Statistics dropdown background updates
5. **Error Handling**: Graceful degradation across all sync points

This comprehensive integration ensures robust streak management with multiple sync points, real-time data consistency, and excellent user experience while maintaining optimal performance.
