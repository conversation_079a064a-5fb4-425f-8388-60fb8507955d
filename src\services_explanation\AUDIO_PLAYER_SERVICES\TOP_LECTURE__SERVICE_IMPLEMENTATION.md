# TopLectureUpdate Service Test Documentation

## Overview
This document describes how to test the TopLectureUpdate service implementation.

## Service Functionality

### What it does:
1. **Tracks daily lecture plays** in the `TopLectures` collection
2. **Document format**: `DD-MM-YYYY` (e.g., "01-01-2023")
3. **Collection path**: `TopLectures/{documentId}`
4. **Triggers**: Only when audio starts playing (not every 60 seconds)

### Data Structure:
```javascript
{
  audioPlayedTime: 0,
  createdDay: {
    day: 1,
    month: 1,
    year: 2023
  },
  creationTimestamp: 1672511788879,
  documentId: "01-01-2023",
  documentPath: "TopLectures/01-01-2023",
  lastModifiedTimestamp: 1672571249750,
  playedBy: ["userId1", "userId2"], // Unique user IDs
  playedIds: [15858, 14353, 14353], // Lecture IDs (duplicates allowed)
  videoPlayedTime: 0
}
```

## How to Test

### 1. Manual Testing
1. **Login** to the application
2. **Play any lecture** from:
   - Media Library
   - Transcription page
   - Any component using `playAudio` function
3. **Check Firebase Console**:
   - Navigate to `TopLectures` collection
   - Look for today's date document (DD-MM-YYYY format)
   - Verify your user ID is in `playedBy` array
   - Verify lecture ID is in `playedIds` array

### 2. Testing Different Scenarios

#### Scenario A: First play of the day
- **Expected**: New document created with current date
- **playedBy**: [your_user_id]
- **playedIds**: [lecture_id]

#### Scenario B: Same user plays different lecture
- **Expected**: Document updated
- **playedBy**: [your_user_id] (no duplicate)
- **playedIds**: [first_lecture_id, second_lecture_id]

#### Scenario C: Same user plays same lecture again
- **Expected**: Document updated
- **playedBy**: [your_user_id] (no duplicate)
- **playedIds**: [lecture_id, lecture_id] (duplicate allowed)

#### Scenario D: Different user plays lecture
- **Expected**: Document updated
- **playedBy**: [first_user_id, second_user_id]
- **playedIds**: [lecture_id, lecture_id]

### 3. Console Logs
Check browser console for:
- ✅ "Playing audio - Lecture ID: {id}"
- ✅ No errors from TopLectureUpdate service
- ⚠️ "User not logged in - skipping TopLectures update" (if not logged in)

### 4. Error Handling
The service handles these cases gracefully:
- User not logged in (skips update)
- Lecture not found in IndexedDB (logs warning)
- Firebase connection issues (logs error)
- Invalid lecture data (logs error)

## Integration Points

### Components that trigger TopLectureUpdate:
1. **MediaLibrary** - When clicking play on any lecture card
2. **TranscriptionView** - When auto-playing or manually playing audio
3. **Any component** using `playAudio` function with lecture ID

### Components that DON'T trigger TopLectureUpdate:
1. **AudioPlayerDemo** - No lecture ID provided
2. **SampleLectureCard** - No lecture ID provided
3. **Direct audio controls** - Only triggers on new audio start

## Troubleshooting

### Common Issues:
1. **No document created**: Check if user is logged in
2. **User ID not added**: Check Firebase authentication
3. **Lecture ID not added**: Check if lecture has valid ID
4. **Service not called**: Check if component uses `playAudio` function

### Debug Steps:
1. Check browser console for errors
2. Verify Firebase authentication status
3. Check if lecture object has valid ID
4. Verify Firebase rules allow write access to TopLectures collection
