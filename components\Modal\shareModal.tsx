"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Button, Input, message, Tooltip } from "antd";
import { Poppins } from "next/font/google";
import {
    FaFacebook,
    FaWhatsapp,
    FaLinkedin,
    FaTelegram,
    FaEnvelope,
    FaCopy,
    FaCheck,
    FaShareAlt,
} from "react-icons/fa";

import { FaXTwitter, FaThreads, FaInstagram } from "react-icons/fa6";
import Image from "next/image";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface ShareModalProps {
    isModalOpen: boolean;
    setIsModalOpen: (isOpen: boolean) => void;
    title: string;
    url: string;
}

const ShareModal = ({
    isModalOpen,
    setIsModalOpen,
    title,
    url,
}: ShareModalProps) => {
    const [isCopied, setIsCopied] = useState(false);
    const [activeIndex, setActiveIndex] = useState<number | null>(null);
    const [showAnimation, setShowAnimation] = useState(false);

    // Reset animation state when modal opens
    useEffect(() => {
        if (isModalOpen) {
            setShowAnimation(true);
            const timer = setTimeout(() => {
                setShowAnimation(false);
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [isModalOpen]);

    // Handle modal close
    const handleCancel = () => {
        setIsModalOpen(false);
    };

    // Handle copy link to clipboard
    const handleCopyLink = () => {
        navigator.clipboard
            .writeText(url)
            .then(() => {
                setIsCopied(true);
                message.success({
                    content: "Link copied to clipboard!",
                    className: `${poppins.className}`,
                });
                setTimeout(() => setIsCopied(false), 3000);
            })
            .catch((err) => {
                message.error({
                    content: "Failed to copy link",
                    className: `${poppins.className}`,
                });
                console.error("Could not copy text: ", err);
            });
    };

    // Social media sharing functions
    const shareToFacebook = () => {
        const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
            url
        )}&quote=${encodeURIComponent(title)}`;
        window.open(shareUrl, "_blank");
    };

    const shareToTwitter = () => {
        const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
            title
        )}&url=${encodeURIComponent(url)}`;
        window.open(shareUrl, "_blank");
    };

    const shareToWhatsApp = () => {
        const shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(
            `${title} ${url}`
        )}`;
        window.open(shareUrl, "_blank");
    };

    const shareToLinkedIn = () => {
        const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
            url
        )}`;
        window.open(shareUrl, "_blank");
    };

    const shareToTelegram = () => {
        const shareUrl = `https://t.me/share/url?url=${encodeURIComponent(
            url
        )}&text=${encodeURIComponent(title)}`;
        window.open(shareUrl, "_blank");
    };

    const shareToInstagram = () => {
        // Instagram doesn't have a direct web sharing API like other platforms
        // We'll show a message to the user about how to share on Instagram
        message.info({
            content:
                "Instagram doesn't support direct sharing. Copy the link and share it manually in Instagram.",
            className: `${poppins.className}`,
            duration: 4,
        });

        // Copy the link to clipboard for convenience
        navigator.clipboard.writeText(url);
    };

    const shareToThreads = () => {
        // Threads doesn't have a direct web sharing API yet
        // We'll open Threads in a new tab and let the user paste the link
        message.info({
            content:
                "Threads doesn't support direct sharing. Copy the link and share it manually in Threads.",
            className: `${poppins.className}`,
            duration: 4,
        });

        // Copy the link to clipboard for convenience
        navigator.clipboard.writeText(url);

        // Open Threads website
        window.open("https://www.threads.net", "_blank");
    };

    const shareViaEmail = () => {
        const shareUrl = `mailto:?subject=${encodeURIComponent(
            title
        )}&body=${encodeURIComponent(`Check out this link: ${url}`)}`;
        window.open(shareUrl, "_blank");
    };

    // Social media platforms array
    const socialPlatforms = [
        {
            name: "Facebook",
            icon: <FaFacebook size={22} />,
            color: "#1877F2",
            hoverColor: "#0e5fc7",
            action: shareToFacebook,
        },
        {
            name: "X",
            icon: <FaXTwitter size={22} />,
            color: "#000000",
            hoverColor: "#333333",
            action: shareToTwitter,
        },
        {
            name: "WhatsApp",
            icon: <FaWhatsapp size={22} />,
            color: "#25D366",
            hoverColor: "#1da851",
            action: shareToWhatsApp,
        },
        {
            name: "LinkedIn",
            icon: <FaLinkedin size={22} />,
            color: "#0A66C2",
            hoverColor: "#084e96",
            action: shareToLinkedIn,
        },
        {
            name: "Telegram",
            icon: <FaTelegram size={22} />,
            color: "#0088CC",
            hoverColor: "#0069a0",
            action: shareToTelegram,
        },
        {
            name: "Instagram",
            icon: <FaInstagram size={22} />,
            color: "#E4405F",
            hoverColor: "#c13584",
            action: shareToInstagram,
        },
        {
            name: "Threads",
            icon: <FaThreads size={22} />,
            color: "#000000",
            hoverColor: "#333333",
            action: shareToThreads,
        },
        {
            name: "Email",
            icon: <FaEnvelope size={22} />,
            color: "#D44638",
            hoverColor: "#b33a2e",
            action: shareViaEmail,
        },
    ];

    return (
        <Modal
            title={
                <div className="flex items-center">
                    <FaShareAlt className="text-text-primary mr-2" size={18} />
                    <h2 className="text-[20px] font-[600] text-text-primary">
                        Share
                    </h2>
                </div>
            }
            open={isModalOpen}
            onCancel={handleCancel}
            footer={null}
            width={400}
            centered
            maskClosable={true}
            destroyOnClose={true}
            className={`${poppins.className} share-modal`}
        >
            <div className="relative">
                {/* Decorative elements */}
                <div className="absolute -top-4 -left-4 w-20 h-20 bg-primary bg-opacity-40 rounded-full opacity-20 -z-10"></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-primary bg-opacity-40 rounded-full opacity-20 -z-10"></div>

                {/* Title of content being shared */}
                <div className="mb-5 p-3 bg-gray-50 rounded-lg border-l-4 border-primary">
                    <p className="text-[12px] text-gray-500 mb-1">Sharing</p>
                    <p className="text-[14px] font-medium text-gray-700 line-clamp-2">
                        {title}
                    </p>
                </div>

                {/* Copy Link Section */}
                <div className="mb-7">
                    <p className="text-[15px] font-[500] mb-3 flex items-center">
                        <FaCopy className="mr-2 text-gray-500" size={14} />
                        Copy Link
                    </p>
                    <div className="flex items-center">
                        <Input
                            value={url}
                            readOnly
                            className={`flex-1 mr-2 rounded-l-lg pointer-events-none ${poppins.className}`}
                            style={{
                                borderColor: "#e0e0e0",
                                boxShadow: "none",
                                fontSize: "13px",
                            }}
                        />
                        <Tooltip
                            title={isCopied ? "Copied!" : "Copy to clipboard"}
                        >
                            <Button
                                type="primary"
                                onClick={handleCopyLink}
                                className="flex items-center justify-center h-[32px] rounded-r-lg !bg-primary !border-primary"
                                style={{
                                    transition: "all 0.3s ease",
                                    boxShadow: "none",
                                }}
                            >
                                {isCopied ? (
                                    <FaCheck size={16} />
                                ) : (
                                    <FaCopy size={16} />
                                )}
                            </Button>
                        </Tooltip>
                    </div>
                </div>

                {/* Social Media Sharing */}
                <div>
                    <p className="text-[15px] font-[500] mb-4 flex items-center">
                        <FaShareAlt className="mr-2 text-gray-500" size={14} />
                        Share on Social Media
                    </p>
                    <div
                        className="grid grid-cols-4 gap-5"
                        style={{
                            animation: showAnimation
                                ? "fadeIn 0.5s ease-out"
                                : "none",
                        }}
                    >
                        {socialPlatforms.map((platform, index) => (
                            <div
                                key={platform.name}
                                className="flex flex-col items-center cursor-pointer transform transition-all duration-200 hover:scale-95 "
                                onClick={() => {
                                    setActiveIndex(index);
                                    platform.action();
                                }}
                                style={{
                                    animation: showAnimation
                                        ? `popIn 0.5s ease-out ${index * 0.05}s`
                                        : "none",
                                }}
                            >
                                <div
                                    className="w-14 h-14 rounded-full flex items-center justify-center mb-2 shadow-md transition-all duration-300 hover:shadow-lg"
                                    style={{
                                        backgroundColor: platform.color,
                                        transform:
                                            activeIndex === index
                                                ? "scale(1.1)"
                                                : "scale(1)",
                                    }}
                                >
                                    <span className="text-white">
                                        {platform.icon}
                                    </span>
                                </div>
                                <span className="text-[11px] font-medium text-center text-gray-700">
                                    {platform.name}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </Modal>
    );
};

export default ShareModal;
