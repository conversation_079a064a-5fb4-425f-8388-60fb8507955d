# Seek Functionality Test Plan

## Overview
This document outlines the test plan for the new seek functionality that updates the last played point when users seek to different positions in audio/video players.

## Test Scenarios

### 1. Basic Seek Functionality
**Test Case**: User seeks to a specific position
- **Steps**:
  1. Start playing a video/audio
  2. Seek to 10 minutes (600 seconds)
  3. Verify lastPlayedPoint is updated to 600 seconds in Firebase
  4. Verify tracking restarts from the new position

**Expected Result**: 
- lastPlayedPoint should be set to 600 seconds
- Next 60-second interval should update to 660 seconds

### 2. Seek with Existing Progress
**Test Case**: User has already played 5 minutes, then seeks to 10 minutes
- **Steps**:
  1. Play video/audio for 5 minutes (300 seconds)
  2. Seek to 10 minutes (600 seconds)
  3. Continue playing for 1 minute
  4. Verify lastPlayedPoint progression

**Expected Result**:
- After seek: lastPlayedPoint = 600 seconds
- After 1 minute of play: lastPlayedPoint = 660 seconds

### 3. Seek Backward
**Test Case**: User seeks backward from current position
- **Steps**:
  1. Play video/audio for 10 minutes (600 seconds)
  2. Seek backward to 5 minutes (300 seconds)
  3. Continue playing
  4. Verify tracking restarts from 300 seconds

**Expected Result**:
- After seek: lastPlayedPoint = 300 seconds
- Tracking continues from 300 seconds

### 4. Multiple Seeks
**Test Case**: User performs multiple seeks in quick succession
- **Steps**:
  1. Start playing
  2. Seek to 2 minutes (120 seconds)
  3. Immediately seek to 5 minutes (300 seconds)
  4. Immediately seek to 8 minutes (480 seconds)
  5. Continue playing

**Expected Result**:
- Final lastPlayedPoint should be 480 seconds
- Only one tracking interval should be active
- Next update should be at 540 seconds (480 + 60)

### 5. Seek During Pause
**Test Case**: User seeks while video/audio is paused
- **Steps**:
  1. Start playing video/audio
  2. Pause at 2 minutes
  3. Seek to 5 minutes while paused
  4. Resume playing

**Expected Result**:
- lastPlayedPoint updated to 300 seconds (5 minutes)
- When resumed, tracking starts from 300 seconds

### 6. YouTube Video Seek
**Test Case**: Seek functionality works with YouTube videos
- **Steps**:
  1. Play a YouTube video
  2. Seek to 10 minutes
  3. Verify YouTube player tracking is reset
  4. Continue playing

**Expected Result**:
- YouTube tracking interval is reset
- lastPlayedPoint updated correctly
- Subsequent tracking works from new position

## Manual Testing Steps

### Setup
1. Open the application
2. Navigate to a video or audio lecture
3. Open browser developer tools to monitor console logs
4. Open Firebase console to monitor database updates

### Test Execution
1. **Start Playing**: Begin playback of any lecture
2. **Monitor Initial Tracking**: Wait for first 60-second update to confirm normal tracking
3. **Perform Seek**: Use progress bar or seek controls to jump to different position
4. **Verify Update**: Check console logs for "Seek completed" message
5. **Check Firebase**: Verify lastPlayedPoint field is updated in Firebase
6. **Continue Playing**: Let video/audio play for another minute
7. **Verify Continued Tracking**: Confirm next 60-second update works from new position

### Expected Console Messages
```
Seek completed - Updated lastPlayedPoint to 600s for video/audio [ID]
```

### Expected Firebase Updates
- `lastPlayedPoint` field should be updated immediately after seek
- `lastModifiedTimestamp` should be updated
- Subsequent 60-second intervals should continue from the new position

## Edge Cases to Test

### 1. Seek Beyond Duration
- Seek to a position beyond the total length
- Should be clamped to maximum duration

### 2. Seek to Negative Position
- Attempt to seek to negative time
- Should be clamped to 0

### 3. Network Issues
- Seek while offline
- Should handle gracefully and retry when connection restored

### 4. Rapid Seeking
- Multiple seeks within a few seconds
- Should handle without creating multiple intervals

## Success Criteria
✅ Seeking updates lastPlayedPoint immediately
✅ Tracking intervals are properly reset after seek
✅ Subsequent 60-second updates work from new position
✅ Works for both audio and video players
✅ Works for both regular videos and YouTube videos
✅ No duplicate tracking intervals are created
✅ Firebase updates are consistent and reliable

## Troubleshooting

### Common Issues
1. **Multiple Intervals**: Check that old intervals are cleared before creating new ones
2. **Incorrect Position**: Verify time is converted to seconds and floored
3. **No Updates**: Check that currentVideo.id or currentAudio.id exists
4. **Firebase Errors**: Check user authentication and permissions

### Debug Commands
```javascript
// Check active intervals
console.log('Video tracking interval:', trackingIntervalRef.current);
console.log('YouTube tracking interval:', youtubeTrackingIntervalRef.current);

// Check current state
console.log('Current video:', currentVideo);
console.log('Is playing:', isPlaying);
console.log('Current time:', currentTime);
```
