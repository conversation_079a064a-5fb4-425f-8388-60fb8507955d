"use client";
import React from "react";
import Image from "next/image";
import { Poppins } from "next/font/google";
import {
    getProgressMaxValue,
} from "@/src/utils/chartTimeFormat";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface Category {
    name: string;
    fullName: string;
    hours: number;
    color: string;
}

interface CircularProgressProps {
    categories: Category[];
    totalListened: string;
    size?: number;
    scale?: number;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
    categories,
    totalListened,
    size = 200,
    scale = 1,
}) => {
    const center = 280 / 2;
    const maxHours = getProgressMaxValue(categories);

    // Calculate ring properties
    const ringWidth = 8;
    const ringGap = 8;
    const startRadius = 45;

    const rings = categories.map((category, index) => {
        const radius = startRadius + index * (ringWidth + ringGap);
        const circumference = 2 * Math.PI * radius;
        const percentage = (category.hours / maxHours) * 100;
        const strokeDasharray = circumference;
        const strokeDashoffset =
            circumference - (percentage / 100) * circumference;

        return {
            ...category,
            radius,
            circumference,
            strokeDasharray,
            strokeDashoffset,
            percentage,
        };
    });

    // Check if there's any data
    const hasData = categories.some((category) => category.hours > 0);

    return (
        <div
            className={`relative flex items-center justify-center ${poppins.className}`}
        >
            <svg width={280} height={280} className="transform -rotate-90" style={{ scale: scale}}>
                {rings.map((ring, index) => {
                    // Light color version of the main color for uncovered part
                    const lightColor = `${ring.color}30`; // 30 is for 30% opacity in hex

                    return (
                        <g key={`ring-${index}`}>
                            {/* Full background ring (light color) */}
                            <circle
                                cx={center}
                                cy={center}
                                r={ring.radius}
                                fill="none"
                                stroke={lightColor}
                                strokeWidth={ringWidth}
                                strokeLinecap="round"
                            />

                            {/* Progress ring (covered part in full color) */}
                            {hasData && ring.hours > 0 && (
                                <circle
                                    cx={center}
                                    cy={center}
                                    r={ring.radius}
                                    fill="none"
                                    stroke={ring.color}
                                    strokeWidth={ringWidth}
                                    strokeLinecap="round"
                                    strokeDasharray={ring.strokeDasharray}
                                    strokeDashoffset={ring.strokeDashoffset}
                                    className="transition-all duration-1500 ease-out"
                                    style={{
                                        filter: "drop-shadow(0 1px 3px rgba(0,0,0,0.1))",
                                        animationDelay: `${index * 200}ms`,
                                    }}
                                />
                            )}
                        </g>
                    );
                })}
            </svg>

            {/* Center content */}
            <div className="absolute inset-0 flex items-center justify-center">
                <Image
                    src="/images/center-icon.svg"
                    alt="Center Icon"
                    width={40}
                    height={40}
                    className="w-8 h-8 md:w-10 md:h-10"
                />
            </div>
        </div>
    );
};

export default CircularProgress;
