"use client";
import { <PERSON><PERSON>, Mo<PERSON> } from "antd";
import Image from "next/image";
import React, { useState, useMemo } from "react";
import { bookFilterOptions, BOOKS } from "@/src/libs/constant";
import { Poppins } from "next/font/google";
import Checkbox from "@mui/material/Checkbox";
import CheckboxSelected from "../ui/checkboxSelected";
import CheckboxUnselected from "../ui/checkboxUnselected";
import { useBookFilterContext } from "@/src/context/book-filter.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface FilterProps {
    onFilterChange?: (selectedBooks: string[]) => void;
    handleFilterChange?: (selectedBooks: string[]) => void;
}

const BookFilterWithModal = ({
    onFilterChange,
    handleFilterChange,
}: FilterProps) => {
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [selectedFilter, setSelectedFilter] = useState("BOOKS");

    const { selectedBooks, setSelectedBooks, isFiltering, setIsFiltering } =
        useBookFilterContext();

    // Track temporary selections before applying
    const [tempSelectedBooks, setTempSelectedBooks] = useState<string[]>([]);

    // Calculate total number of selected filters
    const totalSelectedFilters = useMemo(() => {
        return selectedBooks.length;
    }, [selectedBooks]);

    // Calculate if any temporary filters are selected
    const hasTempSelectedFilters = useMemo(() => {
        return tempSelectedBooks.length > 0;
    }, [tempSelectedBooks]);

    const handleCheckboxChange = (value: string) => {
        setTempSelectedBooks((prev) => {
            if (prev.includes(value)) {
                return prev.filter((item) => item !== value);
            } else {
                return [...prev, value];
            }
        });
    };

    const handleApply = () => {
        // Create a deep copy of the temporary values to avoid reference issues
        const newSelectedBooks = [...tempSelectedBooks];

        // Update the context state
        setSelectedBooks(newSelectedBooks);
        setIsFilterOpen(false);

        // Call the handler function with the new filter values if provided
        if (handleFilterChange) {
            handleFilterChange(newSelectedBooks);
        }

        // Set filtering flag
        setIsFiltering(true);

        // Call the onFilterChange callback if provided
        if (onFilterChange) {
            onFilterChange(newSelectedBooks);
        }
    };

    const handleClearAll = () => {
        // Update the context state
        setSelectedBooks([]);
        setTempSelectedBooks([]);
        setIsFilterOpen(false);

        // Call the handler function with empty filters if provided
        if (handleFilterChange) {
            handleFilterChange([]);
        }

        // Reset filtering flag
        setIsFiltering(false);

        // Call the onFilterChange callback if provided
        if (onFilterChange) {
            onFilterChange([]);
        }
    };

    const handleOpenModal = () => {
        // When opening, initialize temp selections with current selections
        setTempSelectedBooks([...selectedBooks]);
        setIsFilterOpen(true);
    };

    const handleCloseModal = () => {
        setIsFilterOpen(false);
    };

    const modalContent = (
        <div className={`w-full h-[340px] ${poppins.className}`}>
            <div className="w-full h-[280px] flex">
                <div className="w-[110px] max-[440px]:w-[120px] min-[440px]:w-[130px] border-r flex flex-col">
                    {bookFilterOptions.map((item: any) => (
                        <div
                            key={item.value}
                            className={`w-full flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 sm:py-2.5 cursor-pointer transition-all duration-200 ${
                                selectedFilter === item.value
                                    ? "bg-zinc-200"
                                    : "hover:bg-gray-100"
                            }`}
                            onClick={() => setSelectedFilter(item.value)}
                        >
                            <h2 className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-text-primary">
                                {item.label}
                                {tempSelectedBooks.length > 0
                                    ? ` (${tempSelectedBooks.length})`
                                    : ""}
                            </h2>
                        </div>
                    ))}
                </div>
                <div className="flex-1 overflow-y-auto scrollbar">
                    <div className="flex flex-col gap-1">
                        {BOOKS.map((item: any) => (
                            <div
                                key={item.value}
                                className="flex items-center gap-1 px-2 cursor-pointer"
                            >
                                <Checkbox
                                    size="small"
                                    icon={<CheckboxUnselected />}
                                    checkedIcon={<CheckboxSelected />}
                                    checked={tempSelectedBooks.includes(item.value)}
                                    onChange={() =>
                                        handleCheckboxChange(item.value)
                                    }
                                    color="primary"
                                />
                                <label
                                    className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-textPrimary cursor-pointer"
                                    onClick={() =>
                                        handleCheckboxChange(item.value)
                                    }
                                >
                                    {item.label}
                                </label>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="flex justify-end gap-2 items-center my-3 sm:my-3.5 mx-3 sm:mx-4">
                <Button
                    className={`${poppins.className} text-[13px] sm:text-[15px] leading-6 font-[500] !text-[#1B1F3BCC] !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50`}
                    onClick={handleClearAll}
                >
                    Clear all
                </Button>
                <Button
                    className={`${poppins.className} text-[13px] sm:text-[15px] leading-6 font-[500] !text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50`}
                    onClick={handleApply}
                    disabled={!hasTempSelectedFilters}
                >
                    Apply
                </Button>
            </div>
        </div>
    );

    return (
        <div className="flex flex-col gap-1">
            <Button
                onClick={handleOpenModal}
                className={`h-[30px] sm:h-[32px] flex gap-1 sm:gap-2 items-center pt-[2px] px-1 md:px-3 text-[12px] sm:text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[10px] sm:rounded-[12px] hover:!border-primary cursor-pointer transition-all relative ${
                    (isFilterOpen || totalSelectedFilters > 0) &&
                    "!border-primary"
                }`}
            >
                <Image
                    src="/images/helperComponents/filter.svg"
                    width={20}
                    height={20}
                    alt=""
                    className={`w-[18px] h-[18px] sm:w-[20px] sm:h-[20px]`}
                />
                <h2 className="text-[12px] sm:text-[13px] leading-5 font-[400] text-text-primary md:block hidden">
                    Filter
                </h2>
                {totalSelectedFilters > 0 && (
                    <div className="absolute -top-2 -right-2 w-4 h-4 sm:w-5 sm:h-5 bg-primary text-white text-[9px] sm:text-[10px] font-bold rounded-full flex items-center justify-center">
                        {totalSelectedFilters}
                    </div>
                )}
            </Button>

            <Modal
                title={<h2 className="text-[16px] sm:text-[18px] leading-5 font-[500] text-text-primary">Filters</h2>}
                open={isFilterOpen}
                onCancel={handleCloseModal}
                footer={null}
                width={360}
                centered
                maskClosable={true}
                destroyOnClose={true}
                className={`${poppins.className} filter-modal`}
            >
                {modalContent}
            </Modal>
        </div>
    );
};

export default BookFilterWithModal;
