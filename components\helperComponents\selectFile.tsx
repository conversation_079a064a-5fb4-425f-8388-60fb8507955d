import React from "react";
import RuleIcon from '@mui/icons-material/Rule';

const SelectFiles = ({ setIsSelectFileOpen }: any) => {
  return (
    <div
      className="h-[32px] flex gap-2 items-center pt-[2px] md:px-3 text-[13px] text-left max-[771px]:border-none min-[771px]:border border-[#E0E0E0] rounded-[12px] hover:border-primary cursor-pointer transition-all"
      onClick={() => setIsSelectFileOpen(true)}
    >
      <h2 className="hidden min-[771px]:block text-[12px] leading-5 font-[400] text-text-primary">
        Select
      </h2>
      <div className={`max-[771px]:block hidden !text-text-primary`}>
            <RuleIcon />
          </div>
    </div>
  );
};

export default SelectFiles;
