"use client";
import React from "react";
import { Poppins } from "next/font/google";
import { BookParagraph } from "@/src/api/knowledge-base.api";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface BookContentProps {
    bookParagraphs: BookParagraph[];
    searchQuery?: string;
}

const BookContent = ({ bookParagraphs, searchQuery }: BookContentProps) => {
    // Function to highlight search terms in the text
    const highlightSearchTerm = (text: string, query: string) => {
        if (!query || !text) return text;

        try {
            // Check if the text already contains HTML (like <em> tags)
            if (text.includes("<em>")) {
                // If it does, we need to be careful not to break the HTML
                return text;
            }

            // Create a regex that matches the query (case insensitive)
            const regex = new RegExp(`(${query})`, "gi");

            // Replace matches with highlighted spans
            return text.replace(
                regex,
                '<span class="bg-yellow-200 italic font-medium text-primary">$1</span>'
            );
        } catch (error) {
            console.error("Error highlighting search term:", error);
            return text;
        }
    };

    // Process text with HTML tags (like <em> tags)
    const processHtmlTags = (text: string) => {
        if (!text) return text;

        // Replace <em> tags with styled spans
        return text.replace(
            /<em>(.*?)<\/em>/g,
            '<span class="italic font-medium text-primary">$1</span>'
        );
    };

    // Process newline characters in text
    const processNewlines = (text: string) => {
        if (!text) return text;

        // Replace double newlines with paragraph breaks
        let processed = text.replace(
            /\n\n/g,
            '</p><p class="text-gray-800 text-base leading-relaxed mt-4">'
        );

        // Replace single newlines with line breaks
        processed = processed.replace(/\n/g, "<br />");

        return processed;
    };

    // Combine highlighting, HTML processing, and newline processing
    const processText = (text: string) => {
        let processed = processHtmlTags(text);

        if (searchQuery) {
            processed = highlightSearchTerm(processed, searchQuery);
        }

        processed = processNewlines(processed);

        // Wrap in paragraph tag to work with the replacements
        return `<p class="text-gray-800 text-base leading-relaxed">${processed}</p>`;
    };

    // Group paragraphs by chapter
    const groupedParagraphs: { [key: string]: BookParagraph[] } = {};
    bookParagraphs.forEach((paragraph) => {
        const chapterKey = `${paragraph.chapterTitle}${
            paragraph.topicTitle
                ? ` under the topic ${paragraph.topicTitle}`
                : ""
        }`;
        if (!groupedParagraphs[chapterKey]) {
            groupedParagraphs[chapterKey] = [];
        }
        groupedParagraphs[chapterKey].push(paragraph);
    });

    return (
        <div className={`${poppins.className} pb-32`}>
            <div className="bg-white rounded-lg">
                {Object.entries(groupedParagraphs).map(
                    ([_, paragraphs], chapterIndex) => (
                        <div key={chapterIndex} className="mb-8">
                            <div className="space-y-4">
                                {paragraphs.map((paragraph, index) => (
                                    <div
                                        key={index}
                                        className="bg-primary bg-opacity-10 p-4 rounded-lg border border-primary"
                                    >
                                        <h2 className="text-xl font-semibold text-primary mb-4">
                                            Paragraph{" "}
                                            {paragraph.paragraphNumber} in
                                            Chapter {paragraph.chapterTitle}
                                            {paragraph.topicTitle
                                                ? ` under the topic ${paragraph.topicTitle}`
                                                : ""}
                                        </h2>
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: processText(
                                                    paragraph.text
                                                ),
                                            }}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    )
                )}
            </div>
        </div>
    );
};

export default BookContent;
