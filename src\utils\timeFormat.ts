/**
 * Converts seconds to HH:MM:SS format
 * @param seconds Total seconds to format
 * @returns Formatted time string in HH:MM:SS format
 */
export const formatSecondsToHHMMSS = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return "00:00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(remainingSeconds).padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
};

/**
 * Formats date object or string to DD/MM/YYYY format
 * @param dateObj Object containing year, month, and day or a date string in YYYY-MM-DD format
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatDateObjToDDMMYYYY = (dateObj: any): string => {
  // Handle string date format (YYYY-MM-DD) from deep search results
  if (typeof dateObj === 'string') {
    try {
      const date = new Date(dateObj);
      if (!isNaN(date.getTime())) {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
      }
    } catch (error) {
      console.error('Error parsing date string:', error);
      return '';
    }
  }

  // Handle object date format from original structure
  if (dateObj && typeof dateObj === 'object') {
    if (!dateObj.year || !dateObj.month) {
      return '';
    }

    const year = dateObj.year;
    const month = dateObj.month;
    const day = dateObj.day || '01'; // Default to 1st if day is not provided

    return `${day}/${month}/${year}`;
  }

  return '';
};

/**
 * Converts a timestamp to DD/MM/YYYY format
 * @param timestamp Unix timestamp in milliseconds
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatTimestampToDDMMYYYY = (timestamp: number): string => {
  if (!timestamp || isNaN(timestamp)) return '';

  const date = new Date(timestamp);

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

