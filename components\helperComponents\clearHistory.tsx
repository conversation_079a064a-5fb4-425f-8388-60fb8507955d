import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { Inter } from "next/font/google";
import { RiDeleteBin2Line } from "react-icons/ri";

const inter = Inter({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const ClearHistory = ({ handleClearHistory }: any) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleClear: any = async () => {
    setLoading(true)
    await handleClearHistory();
    setLoading(false)
    setIsModalOpen(false)
  }

  return (
    <div className={`flex flex-col gap-1`}>
      <Button
        className={`flex gap-2 items-center justify-between pt-[2px] px-1 md:px-3 text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[12px] hover:!border-primary cursor-pointer transition-all`}
        onClick={() => setIsModalOpen(true)}
      >
        <h2
          className={`text-[13px] leading-5 font-[400] text-text-primary hidden min-[845px]:block`}
        >
          Clear History
        </h2>
        <div className={`max-[845px]:block hidden !text-text-primary`}>
          <RiDeleteBin2Line className="text-[18px]" />
        </div>
      </Button>
      <Modal
        centered
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null} // Remove default buttons
        width={380}
      >
        <h1
          className={`w-full text-[24px] mt-4 leading-[22.4px] font-bold ${inter.className}`}
        >
          Clear History
        </h1>
        <p
          className={`w-full text-[16px] mt-4 leading-[22.4px] font-[400] ${inter.className}`}
        >
          Are you sure you want to clear the history?
        </p>
        {/* Custom Buttons */}
        <div className="flex gap-4 mt-4">
          <Button
            onClick={handleClear}
            className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
            style={{
              background: `var(--primary-color)`,
              color: "white",
              borderColor: `var(--primary-color)`,
            }}
            loading={loading}
          >
            Clear
          </Button>
          <Button
            onClick={() => setIsModalOpen(false)}
            className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
            style={{
              background: `var(--primary-color)`,
              color: "white",
              borderColor: `var(--primary-color)`,
            }}
          >
            Cancel
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ClearHistory;
