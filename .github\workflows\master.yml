on:
  push:
    branches:
      - master
concurrency:
  group: master
  cancel-in-progress: true
name: 🚀 Deploy master to DigitalOcean Server
jobs:
  deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 SSH Deploy
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_KEY }}
        port: ${{ secrets.SERVER_PORT }}
        script: |
            cd /usr/share/nginx/bvks-web-react/
            sudo git reset --hard
            sudo git checkout master
            sudo git pull origin master
            sudo yarn
            sudo npm install
            export NODE_ENV=DEV
            sudo yarn run build
            pm2 restart "bvks-react-app"
            pm2 save
