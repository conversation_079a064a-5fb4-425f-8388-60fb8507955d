"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON>, Poppins } from "next/font/google";
import { useRouter } from "next/navigation";
import ResultCount from "../helperComponents/resultCount";
import PlaybackMode from "../helperComponents/playbackMode";
import SortBy from "../helperComponents/sortBy";
import Filter from "../helperComponents/filter";
import SelectFiles from "../helperComponents/selectFile";
import AddToPlaylist from "../helperComponents/addToPlaylist";
import AddToFavorites from "../helperComponents/addToFavorite";
import MarkAsComplete from "../helperComponents/markAsComplete";
import { Button, message } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import ChecklistIcon from "@mui/icons-material/Checklist";
import { useSearchContext } from "@/src/context/search.context";
import { useFilterContext } from "@/src/context/filter.context";
import { fetchPopularLectures } from "@/src/services/popular.service";
import PopularPeriod from "../helperComponents/popularPeriod";
import LectureCardSkeleton from "../lectureCard/lectureCardSkeleton";
import InfiniteScroll from "react-infinite-scroll-component";
import LectureCard from "../lectureCard/lectureCard";
import { popularPeriodOptions } from "@/src/libs/constant";
import AudioToggleSwitch from "../helperComponents/audioToggleSwitch";
import ResetAsComplete from "../helperComponents/resetAsComplete";
import RemoveFavorites from "../helperComponents/removeFavourite";

const poppins = Poppins({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const inter = Inter({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const roboto = Roboto({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

const Popular = () => {
    const { searchQuery, search, deepSearch, isSearching, setIsSearching } =
        useSearchContext();
    const {
        selectedFilterValues,
        sortBy,
        popularPeriod,
        playbackMode,
        isFiltering,
    } = useFilterContext();

    const router = useRouter();
    const [Mode, setMode] = useState(1);
    const [sort, setSort] = useState(1);
    const [isSelectFileOpen, setIsSelectFileOpen] = useState(false);

    const [selectedFiles, setSelectedFiles] = useState<any>([]);
    const [allLectures, setAllLectures] = useState<any[]>([]);
    const [displayedLectures, setDisplayedLectures] = useState<any[]>([]);
    const [isDeepSearchLoading, setIsDeepSearchLoading] = useState(false);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const resultsPerPage = 20;

    const isFirstRender = useRef(true);
    const isSearchInitialMount = useRef(true);
    const isDeepSearchInitialMount = useRef(true);

    // Load initial favorites data
    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            loadPopularData();
        }
    }, []);

    useEffect(() => {
        if (!isFirstRender.current && !deepSearch) {
            loadPopularData();
        }
    }, [popularPeriod]);

    // Handle search
    useEffect(() => {
        if (isSearchInitialMount.current) {
            isSearchInitialMount.current = false;
            return;
        }
        handleSearch();
    }, [search]);

    // Handle filter/sort changes
    useEffect(() => {
        if (!isFirstRender.current) {
            if (isSearching) {
                // Re-apply current search with new filters/sort
                if (deepSearch) {
                    // handleDeepSearch();
                } else {
                    handleSearch();
                }
            } else {
                // Re-load favorites with new filters/sort
                loadPopularData();
            }
        }
    }, [sortBy, selectedFilterValues, playbackMode]);

    const loadPopularData = async () => {
        try {
            setIsDeepSearchLoading(true);
            setLoading(true);

            const lectures = await fetchPopularLectures({
                searchQuery: isSearching ? searchQuery : "",
                sortBy,
                allFilter: selectedFilterValues,
                playbackMode,
                popularPeriod,
            });

            setAllLectures(lectures);

            // Reset pagination and load first page
            setPage(1);
            const firstPageLectures = lectures.slice(0, resultsPerPage);
            setDisplayedLectures(firstPageLectures);
            setHasMore(lectures.length > resultsPerPage);
        } catch (error) {
            console.error("Error loading popular lectures:", error);
            message.error(
                "We couldn't fetch popular lectures at this moment, please try again later."
            );
            setAllLectures([]);
            setDisplayedLectures([]);
            setHasMore(false);
        } finally {
            setIsDeepSearchLoading(false);
            setLoading(false);
        }
    };

    const resetSelectionState = () => {
        setIsSelectFileOpen(false);
        setSelectedFiles([]);
    };

    const handleSearch = async () => {
        setIsDeepSearchLoading(true);
        resetSelectionState();

        if (!searchQuery.trim()) {
            setIsSearching(false);
            await loadPopularData();
            return;
        }

        try {
            setIsLoadingMore(true);
            setIsSearching(true);

            const lectures = await fetchPopularLectures({
                searchQuery,
                sortBy,
                allFilter: selectedFilterValues,
                playbackMode,
                popularPeriod,
            });

            setAllLectures(lectures);

            // Reset pagination and load first page
            setPage(1);
            const firstPageLectures = lectures.slice(0, resultsPerPage);
            setDisplayedLectures(firstPageLectures);
            setHasMore(lectures.length > resultsPerPage);
        } catch (error) {
            console.error("Error searching favorites:", error);
            setAllLectures([]);
            setDisplayedLectures([]);
            setHasMore(false);
        } finally {
            setIsDeepSearchLoading(false);
            setTimeout(() => {
                setIsLoadingMore(false);
            }, 800);
        }
    };

    const loadMoreResults = useCallback(() => {
        if (!hasMore || isLoadingMore) return;

        setIsLoadingMore(true);

        setTimeout(() => {
            const nextPage = page + 1;
            const startIndex = (nextPage - 1) * resultsPerPage;
            const endIndex = startIndex + resultsPerPage;

            const nextResults = allLectures.slice(startIndex, endIndex);

            if (nextResults.length > 0) {
                setDisplayedLectures((prev) => [...prev, ...nextResults]);
                setPage(nextPage);
            }

            // Check if we've loaded all results
            if (endIndex >= allLectures.length) {
                setHasMore(false);
            }

            setIsLoadingMore(false);
        }, 500);
    }, [hasMore, isLoadingMore, page, allLectures, resultsPerPage]);

    const handleLectureUpdated = async (
        updatedIds: (string | number)[],
        field: string,
        value: boolean
    ) => {
        if (updatedIds.length === 0) return;

        setAllLectures((prev: any) =>
            prev.map((lecture: any) => {
                if (updatedIds.includes(lecture.id)) {
                    return { ...lecture, [field]: value };
                }
                return lecture;
            })
        );

        setDisplayedLectures((prevResults) =>
            prevResults.map((lecture: any) => {
                if (updatedIds.includes(lecture.id)) {
                    return { ...lecture, [field]: value };
                }
                return lecture;
            })
        );
    };

    return (
        <div className={`w-full h-full ${poppins.className} tracking-normal`}>
            <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
                {/* Result and selected count */}
                <div className="hidden md:flex gap-4 md:absolute top-5 min-[460px]:top-2 right-4">
                    {selectedFiles.length > 0 && (
                        <h2
                            className={`text-[12px] leading-5 font-[400] text-text-primary`}
                        >
                            <span className="font-[500]">Selected:</span>{" "}
                            {selectedFiles.length}
                        </h2>
                    )}
                    {(isSearching || isFiltering) && !loading && (
                        <h2
                            className={`text-[12px] leading-5 font-[400] text-text-primary `}
                        >
                            <span className="font-[500]">Result count:</span>{" "}
                            {allLectures.length}
                        </h2>
                    )}
                </div>

                {/* Heading */}
                <h1 className="max-[392px]:text-[20px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
                    Popular
                </h1>

                {/* Options */}
                {!isSelectFileOpen ? (
                    <div className="flex gap-2 xl:gap-6 items-end">
                        <AudioToggleSwitch />
                        <PopularPeriod />
                        <PlaybackMode />
                        <SortBy />
                        <Filter />
                        <SelectFiles {...{ setIsSelectFileOpen }} />
                    </div>
                ) : (
                    <div className="flex gap-2 xl:gap-6 items-end relative">
                        <AddToPlaylist {...{ selectedFiles }} />
                        <AddToFavorites
                            selectedFiles={selectedFiles}
                            onFavoritesUpdated={handleLectureUpdated}
                        />
                        <RemoveFavorites
                            selectedFiles={selectedFiles}
                            onFavoritesUpdated={handleLectureUpdated}
                        />
                        <MarkAsComplete
                            {...{
                                selectedFiles,
                                onCompleteUpdated: handleLectureUpdated,
                            }}
                        />
                        <ResetAsComplete
                            {...{
                                selectedFiles,
                                onCompleteUpdated: handleLectureUpdated,
                            }}
                        />
                        {/* <Button
                              className="h-[32px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left border border-[#E0E0E0] hover:!border-[#fd7d14] rounded-[12px]  cursor-pointer transition-all"
                              onClick={() => setIsSelectFileOpen(true)}
                            >
                              <h2 className="text-[13.5px] leading-5 font-[400] text-text-primary">
                                <span className="hidden sm:block">Select All</span>{" "}
                                <span className="block sm:hidden">
                                  <ChecklistIcon />
                                </span>
                              </h2>
                            </Button> */}
                        <div
                            className="h-[32px] flex gap-2 items-center pt-[2px] px-2.5 md:px-3 text-[13px] text-left bg-[#E0E0E0] rounded-[12px] hover:opacity-85 cursor-pointer transition-all"
                            onClick={() => {
                                setSelectedFiles([]);
                                setIsSelectFileOpen(false);
                            }}
                        >
                            <h2 className="text-[13px] leading-5 font-[400] text-text-primary">
                                <span className="sm:block hidden">Cancel</span>{" "}
                                <span className="block sm:hidden">
                                    <CloseOutlined />
                                </span>
                            </h2>
                        </div>
                    </div>
                )}
            </div>

            <div
                id="scrollableDiv"
                className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
            >
                {!isSearching && !isFiltering ? (
                    <div className="w-full flex flex-col gap-4">
                        {/* Show period and sorting indicators */}
                        {/* <div className="flex flex-col gap-1">
                              {historyPeriod !== 1 && (
                                <div className="text-sm text-gray-600 mb-1">
                                  <span className="font-bold">Period:</span>{" "}
                                  {historyPeriodOptions.find(
                                    (option: any) => option.value === historyPeriod
                                  )?.label || "All History"}
                                </div>
                              )}
                              {sortBy !== 1 && (
                                <div className="text-sm text-gray-600 mb-2">
                                  <span className="font-bold">Sorted by:</span>{" "}
                                  {sortByOptions.find((option: any) => option.value === sortBy)
                                    ?.label || "Default view"}
                                </div>
                              )}
                            </div> */}

                        {loading ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <LectureCardSkeleton
                                            key={`history-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : allLectures.length === 0 ? (
                            <div className="text-center py-20">
                                <p className="text-lg text-gray-500">
                                    No history found
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    {popularPeriod === 1
                                        ? "You haven't listened to any lectures yet"
                                        : `No lectures found for the selected period: ${
                                              popularPeriodOptions.find(
                                                  (option: any) =>
                                                      option.value ===
                                                      popularPeriod
                                              )?.label || "Selected period"
                                          }`}
                                </p>
                            </div>
                        ) : (
                            <InfiniteScroll
                                dataLength={displayedLectures.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <LectureCardSkeleton
                                                    key={`history-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedLectures.map((lecture: any) => (
                                        <LectureCard
                                            key={lecture.id}
                                            lectureData={lecture}
                                            {...{
                                                isSelectFileOpen:
                                                    isSelectFileOpen,
                                                selectedFiles: selectedFiles,
                                                setSelectedFiles:
                                                    setSelectedFiles,
                                                onLectureUpdated:
                                                    handleLectureUpdated,
                                            }}
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        )}
                    </div>
                ) : (
                    <div className="w-full flex flex-col gap-4">
                        <div className="flex flex-col gap-1">
                            <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                                {isSearching
                                    ? `Search Results for "${searchQuery}"`
                                    : "Filtered Results"}
                            </h1>
                            {(() => {
                                // Check if any filters are applied
                                const hasFilters = Object.values(
                                    selectedFilterValues
                                ).some(
                                    (values: any) =>
                                        Array.isArray(values) &&
                                        values.length > 0
                                );

                                if (hasFilters) {
                                    return (
                                        <div className="flex flex-wrap items-center gap-2 mb-2">
                                            <span className="text-sm text-gray-600">
                                                Filters applied:
                                            </span>
                                            {Object.entries(
                                                selectedFilterValues
                                            ).map(
                                                ([filterType, values]: [
                                                    string,
                                                    any
                                                ]) => {
                                                    if (
                                                        Array.isArray(values) &&
                                                        values.length > 0
                                                    ) {
                                                        return (
                                                            <span
                                                                key={filterType}
                                                                className="text-[10px] bg-primary-light text-primary px-2 py-1 rounded-md"
                                                            >
                                                                {filterType}:{" "}
                                                                {values.length}
                                                            </span>
                                                        );
                                                    }
                                                    return null;
                                                }
                                            )}
                                        </div>
                                    );
                                }
                                return null;
                            })()}
                        </div>

                        {isDeepSearchLoading ? (
                            // Show skeleton loading during deep search
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <LectureCardSkeleton
                                            key={`deep-search-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : displayedLectures.length > 0 ? (
                            <InfiniteScroll
                                dataLength={displayedLectures.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <LectureCardSkeleton
                                                    key={`search-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedLectures.map((lecture: any) => (
                                        <LectureCard
                                            key={lecture.id}
                                            lectureData={lecture}
                                            {...{
                                                isSelectFileOpen:
                                                    isSelectFileOpen,
                                                selectedFiles: selectedFiles,
                                                setSelectedFiles:
                                                    setSelectedFiles,
                                                onLectureUpdated:
                                                    handleLectureUpdated,
                                            }}
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        ) : (
                            <div className="text-center py-10">
                                <p className="text-lg text-gray-500">
                                    {isSearching
                                        ? `No results found for "${searchQuery}"${
                                              isFiltering
                                                  ? " with the applied filters"
                                                  : ""
                                          }`
                                        : "No results match the applied filters"}
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    {isSearching
                                        ? "Try different keywords or check your spelling"
                                        : "Try adjusting your filter criteria"}
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Popular;
