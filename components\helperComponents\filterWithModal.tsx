"use client";
import { <PERSON><PERSON>, Mo<PERSON> } from "antd";
import Image from "next/image";
import React, { useState, useMemo } from "react";
import {
    filterOptions,
    LANGUAGE,
    COUNTRY,
    PLACE,
    YEAR,
    MONTH,
    CATEGORY,
    TRANSLATION,
} from "@/src/libs/constant";
import { Poppins } from "next/font/google";
import Checkbox from "@mui/material/Checkbox";
import CheckboxSelected from "../ui/checkboxSelected";
import CheckboxUnselected from "../ui/checkboxUnselected";
import { useFilterContext } from "@/src/context/filter.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface FilterProps {
    onFilterChange?: (selectedFilters: { [key: string]: string[] }) => void;
    handleFilterChange?: (selectedFilters: { [key: string]: string[] }) => void;
}

const FilterWithModal = ({
    onFilterChange,
    handleFilterChange,
}: FilterProps) => {
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [selectedFilter, setSelectedFilter] = useState("LANGUAGE");

    const { selectedFilterValues, setSelectedFilterValues, setIsFiltering } =
        useFilterContext();

    // Track temporary selections before applying
    const [tempSelectedValues, setTempSelectedValues] = useState<{
        [key: string]: string[];
    }>({
        LANGUAGE: [],
        COUNTRY: [],
        PLACE: [],
        YEAR: [],
        MONTH: [],
        CATEGORY: [],
        TRANSLATION: [],
    });

    // Calculate total number of selected filters
    const totalSelectedFilters: any = useMemo(() => {
        return Object.values(selectedFilterValues).reduce(
            (total, current: any) => total + current.length,
            0
        );
    }, [selectedFilterValues]);

    // Calculate if any temporary filters are selected
    const hasTempSelectedFilters: any = useMemo(() => {
        return Object.values(tempSelectedValues).some(
            (filterValues) => filterValues.length > 0
        );
    }, [tempSelectedValues]);

    const handleCheckboxChange = (value: string) => {
        setTempSelectedValues((prev) => {
            const currentSelected = [...prev[selectedFilter]];
            if (currentSelected.includes(value)) {
                return {
                    ...prev,
                    [selectedFilter]: currentSelected.filter(
                        (item) => item !== value
                    ),
                };
            } else {
                return {
                    ...prev,
                    [selectedFilter]: [...currentSelected, value],
                };
            }
        });
    };

    const handleApply = () => {
        // Create a deep copy of the temporary values to avoid reference issues
        const newFilterValues = JSON.parse(JSON.stringify(tempSelectedValues));

        // Debug the filter values being applied
        console.log("Applying filter values:", newFilterValues);

        // Update the context state
        setSelectedFilterValues(newFilterValues);
        setSelectedFilter("LANGUAGE");
        setIsFilterOpen(false);

        // Call the handler function with the new filter values if provided
        if (handleFilterChange) {
            handleFilterChange(newFilterValues);
        }

        // Set filtering flag
        setIsFiltering(true);

        // Call the onFilterChange callback if provided
        if (onFilterChange) {
            onFilterChange(newFilterValues);
        }
    };

    const handleClearAll = () => {
        const emptyFilters = {
            LANGUAGE: [],
            COUNTRY: [],
            PLACE: [],
            YEAR: [],
            MONTH: [],
            CATEGORY: [],
            TRANSLATION: [],
        };

        console.log("Clearing all filters");

        // Update the context state
        setSelectedFilterValues(emptyFilters);
        setTempSelectedValues(emptyFilters);
        setSelectedFilter("LANGUAGE");
        setIsFilterOpen(false);

        // Call the handler function with empty filters if provided
        if (handleFilterChange) {
            handleFilterChange(emptyFilters);
        }

        // Reset filtering flag
        setIsFiltering(false);

        // Call the onFilterChange callback if provided
        if (onFilterChange) {
            onFilterChange(emptyFilters);
        }
    };

    const getFilterData = (filterType: string) => {
        switch (filterType) {
            case "LANGUAGE":
                return LANGUAGE;
            case "COUNTRY":
                return COUNTRY;
            case "PLACE":
                return PLACE;
            case "YEAR":
                return YEAR;
            case "MONTH":
                return MONTH;
            case "CATEGORY":
                return CATEGORY;
            case "TRANSLATION":
                return TRANSLATION;
            default:
                return [];
        }
    };

    const handleOpenModal = () => {
        // When opening, initialize temp selections with current selections
        setTempSelectedValues({ ...selectedFilterValues });
        setIsFilterOpen(true);
    };

    const handleCloseModal = () => {
        setIsFilterOpen(false);
    };

    const modalContent = (
        <div className={`w-full h-[340px] ${poppins.className}`}>
            <div className="w-full h-[280px] flex">
                <div className="w-[110px] max-[440px]:w-[120px] min-[440px]:w-[130px] border-r flex flex-col">
                    {filterOptions.map((item: any) => (
                        <div
                            key={item.value}
                            className={`w-full flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 sm:py-2.5 cursor-pointer transition-all duration-200 ${
                                selectedFilter === item.value
                                    ? "bg-zinc-200"
                                    : "hover:bg-gray-100"
                            }`}
                            onClick={() => setSelectedFilter(item.value)}
                        >
                            <h2 className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-text-primary">
                                {item.label}
                                {tempSelectedValues[item.value]?.length > 0
                                    ? ` (${
                                          tempSelectedValues[item.value]?.length
                                      })`
                                    : ""}
                            </h2>
                        </div>
                    ))}
                </div>
                <div className="flex-1 overflow-y-auto scrollbar">
                    <div className="flex flex-col gap-1">
                        {getFilterData(selectedFilter).map((item: any) => (
                            <div
                                key={item.value}
                                className="flex items-center gap-1 px-2 cursor-pointer"
                            >
                                <Checkbox
                                    size="small"
                                    icon={<CheckboxUnselected />}
                                    checkedIcon={<CheckboxSelected />}
                                    checked={tempSelectedValues[
                                        selectedFilter
                                    ].includes(item.value)}
                                    onChange={() =>
                                        handleCheckboxChange(item.value)
                                    }
                                    color="primary"
                                />
                                <label
                                    className="text-[11px] sm:text-[12px] min-[440px]:text-[13px] leading-5 font-[400] text-textPrimary cursor-pointer"
                                    onClick={() =>
                                        handleCheckboxChange(item.value)
                                    }
                                >
                                    {item.label}
                                </label>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            <div className="flex justify-end gap-2 items-center my-3 sm:my-3.5 mx-3 sm:mx-4">
                <Button
                    className={`${poppins.className} text-[13px] sm:text-[15px] leading-6 font-[500] !text-[#1B1F3BCC] !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50`}
                    onClick={handleClearAll}
                    // disabled={!hasTempSelectedFilters}
                >
                    Clear all
                </Button>
                <Button
                    className={`${poppins.className} text-[13px] sm:text-[15px] leading-6 font-[500] !text-primary !border-none cursor-pointer transition-all shadow-none disabled:!bg-white disabled:opacity-50`}
                    onClick={handleApply}
                    disabled={!hasTempSelectedFilters}
                >
                    Apply
                </Button>
            </div>
        </div>
    );

    return (
        <div className="flex flex-col gap-1">
            <Button
                onClick={handleOpenModal}
                className={`h-[30px] sm:h-[32px] flex gap-1 sm:gap-2 items-center pt-[2px] px-1 md:px-3 text-[12px] sm:text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[10px] sm:rounded-[12px] hover:!border-primary cursor-pointer transition-all relative ${
                    (isFilterOpen || totalSelectedFilters > 0) &&
                    "!border-primary"
                }`}
            >
                <Image
                    src="/images/helperComponents/filter.svg"
                    width={20}
                    height={20}
                    alt=""
                    className={`w-[18px] h-[18px] sm:w-[20px] sm:h-[20px]`}
                />
                <h2 className="text-[12px] sm:text-[13px] leading-5 font-[400] text-text-primary md:block hidden">
                    Filter
                </h2>
                {totalSelectedFilters > 0 && (
                    <div className="absolute -top-2 -right-2 w-4 h-4 sm:w-5 sm:h-5 bg-primary text-white text-[9px] sm:text-[10px] font-bold rounded-full flex items-center justify-center">
                        {totalSelectedFilters}
                    </div>
                )}
            </Button>

            <Modal
                title={<h2 className="text-[16px] sm:text-[18px] leading-5 font-[500] text-text-primary">Filter</h2>}
                open={isFilterOpen}
                onCancel={handleCloseModal}
                footer={null}
                width={360}
                centered
                maskClosable={true}
                destroyOnClose={true}
                className={`${poppins.className} filter-modal`}
            >
                {modalContent}
            </Modal>
        </div>
    );
};

export default FilterWithModal;
