"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useVideoContext } from "@/src/context/video.context";
import VideoPlayerControls from "./videoPlayerControls";
import VideoPlayerSidebar from "./videoPlayerSidebar";
import VideoPlayerInfo from "./videoPlayerInfo";
import { IoArrowBack } from "react-icons/io5";
import { Poppins } from "next/font/google";
import { useAudioContext } from "@/src/context/audio.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface VideoPlayerProps {
    lectureData: any;
}

declare global {
    interface Window {
        YT: any;
        onYouTubeIframeAPIReady: () => void;
    }
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ lectureData }) => {
    const router = useRouter();
    const {
        videoRef,
        playVideo,
        currentVideo,
        showControlsTemporarily,
        isFullscreen,
        isYouTubeVideo,
    } = useVideoContext();

    const { isPlaying, closePlayer } = useAudioContext();

    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkMobile();
        window.addEventListener("resize", checkMobile);

        return () => window.removeEventListener("resize", checkMobile);
    }, []);

    // useEffect(() => {
    //     if (isPlaying) {
    //         closePlayer();
    //     }
    // }, []);

    useEffect(() => {
        if (lectureData) {
            const videoData = {
                title: Array.isArray(lectureData.title)
                    ? lectureData.title.join(" ")
                    : lectureData.title,
                subtitle: Array.isArray(lectureData.category)
                    ? lectureData.category.join(", ")
                    : lectureData.category,
                videoSrc: lectureData.resources?.videos?.[0]?.url || "",
                audioSrc:
                    lectureData.resources?.audios?.[0]?.url ||
                    lectureData.audioUrl,
                thumbnailUrl: lectureData.thumbnail || lectureData.thumbnailUrl,
                id: lectureData.id,
                description: lectureData.description || "",
                dateOfRecording: lectureData.dateOfRecording,
                place: lectureData.place || "",
                category: Array.isArray(lectureData.category)
                    ? lectureData.category.join(", ")
                    : lectureData.category,
                language: lectureData.language?.main || "",
                totallength: lectureData.totallength || 0,
            };

            if (isPlaying) {
                closePlayer();
            }

            playVideo(videoData);
        }
    }, [lectureData]);

    const handleBackClick = () => {
        router.back();
    };

    const handleVideoClick = () => {
        if (!isFullscreen) {
            showControlsTemporarily();
        }
    };

    // YouTube player is now handled by the video context
    // No need for duplicate tracking logic here

    if (!lectureData) {
        return null;
    }

    return (
        <div className={`video-player-container ${poppins.className}`}>
            {!isMobile ? (
                <div className="flex gap-6 pt-6 px-6">
                    <div className="flex-1">
                        <div className="h-[calc(100vh-82px)] overflow-y-scroll scrollbar-hide">
                            <div className="flex justify-between gap-2 pb-2 bg-white border-b border-gray-200">
                                <button
                                    type="button"
                                    onClick={handleBackClick}
                                    className="flex items-center gap-2 py-2 text-gray-700 rounded-lg transition-all"
                                    title="Back"
                                >
                                    <IoArrowBack
                                        size={22}
                                        className="text-primary"
                                    />
                                    {/* <span className="text-sm font-medium text-primary">Back</span> */}
                                </button>
                            </div>
                            <div className="relative bg-black rounded-xl overflow-hidden shadow-lg mb-4">
                                <div
                                    className="relative w-full"
                                    style={{ aspectRatio: "16/9" }}
                                >
                                    {currentVideo?.videoSrc &&
                                    isYouTubeVideo(currentVideo.videoSrc) ? (
                                        <div
                                            id="youtube-player"
                                            className="absolute top-0 left-0 w-full h-full"
                                        ></div>
                                    ) : (
                                        <video
                                            ref={videoRef}
                                            className="absolute top-0 left-0 w-full h-full object-cover"
                                            src={currentVideo?.videoSrc}
                                            onClick={handleVideoClick}
                                        />
                                    )}
                                </div>
                            </div>

                            <div className="video-info-section my-6">
                                <VideoPlayerInfo lectureData={lectureData} />
                            </div>
                        </div>
                    </div>

                    <div className="w-96">
                        <VideoPlayerSidebar />
                    </div>
                </div>
            ) : (
                <div className="flex flex-col">
                    <div className="p-4 bg-white border-b border-gray-200">
                        <button
                            type="button"
                            onClick={handleBackClick}
                            className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all"
                        >
                            <IoArrowBack size={18} />
                            <span className="text-sm font-medium">Back</span>
                        </button>
                    </div>

                    <div className="relative bg-black">
                        <div
                            className="relative w-full"
                            style={{ aspectRatio: "16/9" }}
                        >
                            {currentVideo?.videoSrc &&
                            isYouTubeVideo(currentVideo.videoSrc) ? (
                                <div
                                    id="youtube-player-mobile"
                                    className="absolute top-0 left-0 w-full h-full"
                                ></div>
                            ) : (
                                <video
                                    ref={videoRef}
                                    className="absolute top-0 left-0 w-full h-full object-cover"
                                    src={currentVideo?.videoSrc}
                                    onClick={handleVideoClick}
                                />
                            )}
                        </div>
                    </div>

                    <div className="bg-white">
                        <VideoPlayerInfo lectureData={lectureData} />
                        <VideoPlayerSidebar />
                    </div>
                </div>
            )}
        </div>
    );
};

export default VideoPlayer;
