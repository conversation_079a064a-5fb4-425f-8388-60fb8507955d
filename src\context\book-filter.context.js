"use client";
import React, { createContext, useContext, useState } from "react";

// Book titles list
const BOOKS = [
    "a-message-to-the-youth-of-india",
    "brahmacarya-in-krsna-consciousness",
    "glimpses-of-traditional-indian-life",
    "jaya-srila-prabhupada",
    "lekha-mala-a-garland-of-writings",
    "mothers-and-masters",
    "my-memories-of-srila-prabhupada",
    "on-pilgrimage-in-holy-india",
    "on-speaking-strongly-in-srila-prabhupadas-service",
    "patropadesa-volume-1",
    "patropadesa-volume-2",
    "ramayana",
    "the-story-of-rasikananda",
    "sri-bhaktisiddhanta-vaibhava-volume-1",
    "sri-bhaktisiddhanta-vaibhava-volume-2",
    "sri-bhaktisiddhanta-vaibhava-volume-3",
    "sri-caitanya-mahaprabhu",
    "sri-vamsidasa-babaji"
];

const BookFilterContext = createContext();

export const BookFilterContextProvider = ({ children }) => {
    const [selectedBooks, setSelectedBooks] = useState([]);
    const [isFiltering, setIsFiltering] = useState(false);

    // Function to clear all selected books
    const clearSelectedBooks = () => {
        setSelectedBooks([]);
        setIsFiltering(false);
    };

    return (
        <BookFilterContext.Provider
            value={{
                BOOKS,
                selectedBooks,
                setSelectedBooks,
                isFiltering,
                setIsFiltering,
                clearSelectedBooks
            }}
        >
            {children}
        </BookFilterContext.Provider>
    );
};

export const useBookFilterContext = () => {
    return useContext(BookFilterContext);
};
