import React, { useState } from "react";
import { removeFromFavourite } from "@/src/services/favouriteLecture.service";
import { broadcastFavoriteChange } from "@/src/hooks/useFavoriteSync";
import { message, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { RiDislikeLine } from "react-icons/ri";

interface AddToFavoritesProps {
  selectedFiles: any[];
  onFavoritesUpdated?: (updatedIds: (string | number)[], field: string, value: boolean) => void;
}

const RemoveFavorites: React.FC<AddToFavoritesProps> = ({
  selectedFiles,
  onFavoritesUpdated
}: any) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleRemoveFavorites = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setIsProcessing(true);

      // Call the addToFavourite function with batch size
      const successfulIds = await removeFromFavourite(selectedFiles);

      if (successfulIds.length > 0) {
        // message.success(`Added ${successfulIds.length} lecture(s) to favorites`);

        // Notify parent component about the updated favorites
        onFavoritesUpdated(successfulIds, "isFavourite", false);

        // Broadcast favorite changes to all listening components
        successfulIds.forEach(lectureId => {
          broadcastFavoriteChange(lectureId, false);
        });
      } else {
        message.warning('No lectures were removed from favorites');
      }
    } catch (error) {
      console.error('Error removing favorites:', error);
      message.error('Failed to removeo favorites');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div
      className={`h-[36px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left rounded-[12px] transition-all duration-300 ${
        selectedFiles.length > 0 && !isProcessing
          ? "opacity-100 cursor-pointer hover:bg-primary-hover"
          : "opacity-70 cursor-default"
      }`}
      onClick={selectedFiles.length > 0 && !isProcessing ? handleRemoveFavorites : undefined}
    >
      {isProcessing ?
        <Spin
          indicator={<LoadingOutlined spin className="text-text-primary" />}
          size="small"
        /> :
        <RiDislikeLine className="text-[18px] text-secondary" />
      }
      <h2 className={`text-[14px] leading-5 font-[500] text-text-primary min-[924px]:block hidden`}>
        {"Remove Favorites"}
      </h2>
    </div>
  );
};

export default RemoveFavorites;
