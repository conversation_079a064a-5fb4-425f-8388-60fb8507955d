import { initializeApp } from "firebase/app";
import { getAuth, onAuthStateChanged, User } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getMessaging, getToken, onMessage } from "firebase/messaging";

const firebaseConfig = {
    apiKey: process.env.FB_API_KEY,
    authDomain: process.env.FB_AUTH_DOMAIN,
    projectId: process.env.FB_PROJECT_ID,
    storageBucket: process.env.FB_STORAGE_BUCKET,
    messagingSenderId: process.env.FB_MESSAGING_SENDER_ID,
    appId: process.env.FB_APP_ID,
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const db = getFirestore(app)
const storage = getStorage(app);

let messaging: any = null;
if (typeof window !== "undefined") {
    // Check for iOS Safari specifically
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

    if ("Notification" in window && "serviceWorker" in navigator) {
        try {
            // For iOS Safari, we need additional checks
            if (isIOS && isSafari) {
                // iOS Safari has limited support for service workers and notifications
                console.warn("iOS Safari detected - Firebase Messaging may have limited functionality");
                // Still try to initialize but with error handling
                messaging = getMessaging(app);
            } else {
                messaging = getMessaging(app);
            }
        } catch (error) {
            console.error("Error initializing Firebase Messaging:", error);
            messaging = null;
        }
    } else {
        console.warn(
            "Notifications or Service Workers are not supported in this browser."
        );
        messaging = null;
    }
}

const getFCMToken = async (registration: any): Promise<string> => {
    if (typeof window !== "undefined" && "Notification" in window) {
        try {
            // Check if messaging is available
            if (!messaging) {
                throw new Error("Firebase messaging is not available in this browser");
            }

            // Check for iOS Safari
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

            if (isIOS && isSafari) {
                console.warn("iOS Safari detected - FCM tokens may not work properly");
                // You might want to return a placeholder or handle this differently
                // For now, we'll still try to get the token but with additional error handling
            }

            let permission = Notification.permission;
            console.log("Current notification permission:", permission);

            if (permission === "default") {
                permission = await Notification.requestPermission();
                console.log("Permission after request:", permission);
            }

            if (permission === "denied") {
                throw new Error("Notification permission denied by the user");
            }

            if (permission !== "granted") {
                throw new Error("Notification permission not granted");
            }

            const vapidKey = process.env.FB_NOTIFICATION_KEY;
            console.log("Using VAPID key:", vapidKey ? "Present" : "Missing");

            if (!vapidKey) {
                throw new Error("VAPID key is not configured");
            }

            const currentToken = await getToken(messaging, {
                vapidKey: vapidKey,
                serviceWorkerRegistration: registration,
            });

            if (currentToken) {
                console.log("FCM Token generated successfully");
                return currentToken;
            } else {
                throw new Error("No registration token available. Make sure the service worker is registered and the VAPID key is correct.");
            }
        } catch (error) {
            console.error("An error occurred while retrieving token:", error);

            // For iOS Safari, we might want to return a placeholder token or handle gracefully
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

            if (isIOS && isSafari) {
                console.warn("Returning placeholder token for iOS Safari");
                return "ios-safari-not-supported";
            }

            throw error;
        }
    } else {
        throw new Error("Notifications are not supported in this environment.");
    }
};

export { auth, app, getFCMToken, messaging, db, storage };
