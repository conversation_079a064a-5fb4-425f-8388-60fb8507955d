import { collection, getDocs } from "firebase/firestore";
import { db } from "../config/firebase.config";

export const fetchAllHistory = async () => {
    const userId = localStorage.getItem("firebaseUid");

    const listenInfoRef = collection(db, `users/${userId}/listenInfo`);
    const snapshot = await getDocs(listenInfoRef);

    // Convert snapshot to array with full doc (id and data)
    const docs = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
    }));

    // Sort by documentId (assuming format "13-5-2025")
    docs.sort((a, b) => {
        const parseDate = (id: string) => {
            const [day, month, year] = id.split("-").map(Number);
            return new Date(year, month - 1, day);
        };
        return parseDate(b.id).getTime() - parseDate(a.id).getTime();
    });

    return docs;
}