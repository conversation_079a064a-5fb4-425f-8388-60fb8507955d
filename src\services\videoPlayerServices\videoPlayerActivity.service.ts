import {
    collection,
    doc,
    getDocs,
    query,
    setDoc,
    where,
    getDoc,
} from "firebase/firestore";
import { db } from "../../config/firebase.config";
import { getLectureById, updateLecture, Lecture } from "../indexedDB.service";
import { trackUserStatistics } from "../userStatistics.service";
import { fetchUserStatistics } from "../../api/userStatistics.api";

// Interface for listen details by category
interface ListenDetails {
    BG: number;
    CC: number;
    SB: number;
    Seminars: number;
    VSN: number;
    others: number;
}

// Interface for date of record
interface DateOfRecord {
    day: number;
    month: number;
    year: number;
}

// Interface for user listening activity document
interface UserListenInfo {
    audioListen: number;
    creationTimestamp: number;
    date: string; // timestamp
    dateOfRecord: DateOfRecord;
    documentId: string;
    documentPath: string;
    lastModifiedTimestamp: number;
    listenDetails: ListenDetails;
    playedBy: string[];
    playedIds: number[];
    videoListen: number;
}

/**
 * Map lecture category to listen details category
 * @param lectureCategory - The category from lecture data
 * @returns The corresponding category key for listen details
 */
const mapLectureCategoryToListenCategory = (
    lectureCategory: string[] | string
): keyof ListenDetails => {
    const categoryStr = Array.isArray(lectureCategory)
        ? lectureCategory.join(" ")
        : lectureCategory || "";
    const lowerCategory = categoryStr.toLowerCase();

    if (lowerCategory.includes("bhagavad") || lowerCategory.includes("gita")) {
        return "BG";
    } else if (
        lowerCategory.includes("caitanya") ||
        lowerCategory.includes("caritamrta")
    ) {
        return "CC";
    } else if (
        lowerCategory.includes("bhagavatam") ||
        lowerCategory.includes("srimad")
    ) {
        return "SB";
    } else if (lowerCategory.includes("seminar")) {
        return "Seminars";
    } else if (
        lowerCategory.includes("visnu") ||
        lowerCategory.includes("sahasranama")
    ) {
        return "VSN";
    } else {
        return "others";
    }
};

/**
 * Format current date to dd-mm-yyyy string
 * @param date - Date object (defaults to current date)
 * @returns Formatted date string in dd-mm-yyyy format
 */
// const formatDateToDDMMYYYY = (date: Date = new Date()): string => {
//     const day = String(date.getDate()).padStart(2, "0");
//     const month = String(date.getMonth() + 1).padStart(2, "0");
//     const year = date.getFullYear();
//     return `${day}-${month}-${year}`;
// };

const formatDateToDMYYYY = (date: Date = new Date()): string => {
    const day = date.getDate(); // no padStart
    const month = date.getMonth() + 1; // no padStart
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
};

/**
 * Get date components from Date object
 * @param date - Date object (defaults to current date)
 * @returns Object with day, month, year
 */
const getDateComponents = (date: Date = new Date()): DateOfRecord => {
    return {
        day: date.getDate(),
        month: date.getMonth() + 1,
        year: date.getFullYear(),
    };
};

/**
 * Process a single lecture to update lastPlayedPoint by adding specified seconds
 *
 * @param lectureInput - Either a lecture ID or a lecture object
 * @param userId - The current user ID
 * @param secondsToAdd - Number of seconds to add to lastPlayedPoint (default: 60)
 * @returns The lecture ID if successful, null if failed
 */
const processSingleLectureUpdateLastPlayedPoint = async (
    lectureInput: number | string | Lecture,
    userId: string,
    secondsToAdd: number = 60
): Promise<number | string | null> => {
    try {
        // Step 1: Get the lecture object
        let lecture: Lecture | undefined;
        let lectureId: number | string;

        if (typeof lectureInput === "object") {
            // If a lecture object was provided, use it directly
            lecture = lectureInput;
            lectureId = lecture.id;
        } else {
            // If only an ID was provided, fetch the lecture from IndexedDB
            lectureId = lectureInput;
            lecture = await getLectureById(lectureId);
            if (!lecture) {
                // console.warn(`Lecture with ID ${lectureId} not found in IndexedDB`);
                return null;
            }
        }

        const currentTimestamp = Date.now();
        let documentPath: string;

        // Calculate the new lastPlayedPoint by adding specified seconds
        const currentLastPlayedPoint = lecture.lastPlayedPoint || 0;
        const totalLength = lecture.length || lecture.totallength || 0;

        let newLastPlayedPoint: number;
        // Check if current lastPlayedPoint exceeds total length
        if (currentLastPlayedPoint >= totalLength) {
            // If it exceeds, start from beginning and add the new seconds
            newLastPlayedPoint = secondsToAdd;
        } else {
            newLastPlayedPoint = currentLastPlayedPoint + secondsToAdd;
        }

        // Step 2: Check if the lecture has a documentId
        if (lecture.documentId) {
            // Case 1: documentId exists - Update both IndexedDB and Firestore
            documentPath =
                lecture.documentPath ||
                `users/${userId}/lectureInfo/${lecture.documentId}`;

            // Update in IndexedDB
            await updateLecture({
                ...lecture,
                lastPlayedPoint: newLastPlayedPoint,
                lastModifiedTimestamp: currentTimestamp,
            });

            // Update in Firestore
            const docRef = doc(db, documentPath);
            await setDoc(
                docRef,
                {
                    lastPlayedPoint: newLastPlayedPoint,
                    lastModifiedTimestamp: currentTimestamp,
                },
                { merge: true }
            );

            return lectureId;
        } else {
            // Case 2: No documentId - Check Firestore for existing document
            const subCollectionRef = collection(
                db,
                `users/${userId}/lectureInfo`
            );
            const q = query(subCollectionRef, where("id", "==", lectureId));
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                // Found document in Firestore - Update it and merge with IndexedDB
                const docRef = querySnapshot.docs[0];
                documentPath = docRef.ref.path;
                const documentId = docRef.id;

                // Update in Firestore
                await setDoc(
                    docRef.ref,
                    {
                        lastPlayedPoint: newLastPlayedPoint,
                        lastModifiedTimestamp: currentTimestamp,
                    },
                    { merge: true }
                );

                // Merge Firestore document with IndexedDB
                const firestoreData = docRef.data();
                await updateLecture({
                    ...lecture,
                    ...firestoreData,
                    documentId,
                    documentPath,
                    lastPlayedPoint: newLastPlayedPoint,
                    lastModifiedTimestamp: currentTimestamp,
                });

                return lectureId;
            } else {
                // No document in Firestore - Create a new one
                // First, create a new document reference (but don't add data yet)
                const newDocRef = doc(subCollectionRef);

                // Prepare the payload with documentId and documentPath
                const payload = {
                    id: lectureId,
                    completed: false,
                    creationTimestamp: currentTimestamp,
                    downloadPlace: 0,
                    downloaded: false,
                    // favourite: true,
                    isFavourite: false,
                    isCompleted: false,
                    favouritePlace: 0,
                    inPrivateList: false,
                    inPublicList: false,
                    lastModifiedTimestamp: currentTimestamp,
                    lastPlayedPoint: newLastPlayedPoint,
                    totalPlayedNo: 0,
                    totalPlayedTime: 0,
                    totallength: lecture.length || 0,
                    documentId: newDocRef.id,
                    documentPath: newDocRef.path,
                };

                // Add the payload to Firestore using setDoc
                await setDoc(newDocRef, payload);

                documentPath = newDocRef.path;
                const documentId = newDocRef.id;

                // Update in IndexedDB with the new document info
                await updateLecture({
                    ...lecture,
                    documentId,
                    documentPath,
                    completed: lecture.completed || false,
                    creationTimestamp:
                        lecture.creationTimestamp || currentTimestamp,
                    downloadPlace: lecture.downloadPlace || 0,
                    downloaded: lecture.downloaded || false,
                    isCompleted: lecture.isCompleted || false,
                    isFavourite: false,
                    favouritePlace: lecture.favouritePlace || 0,
                    inPrivateList: lecture.inPrivateList || false,
                    inPublicList: lecture.inPublicList || false,
                    lastModifiedTimestamp: currentTimestamp,
                    lastPlayedPoint: newLastPlayedPoint,
                    totalPlayedNo: lecture.totalPlayedNo || 0,
                    totalPlayedTime: lecture.totalPlayedTime || 0,
                    totallength: lecture.length || 0,
                });

                return lectureId;
            }
        }
    } catch (error) {
        console.error(`Error updating lastPlayedPoint:`, error);
        return null;
    }
};

/**
 * Process a single lecture to set lastPlayedPoint to a specific time (for seek operations)
 *
 * @param lectureInput - Either a lecture ID or a lecture object
 * @param userId - The current user ID
 * @param seekTime - The time in seconds to set as the new lastPlayedPoint
 * @returns The lecture ID if successful, null if failed
 */
const processSingleLectureSetLastPlayedPoint = async (
    lectureInput: number | string | Lecture,
    userId: string,
    seekTime: number
): Promise<number | string | null> => {
    try {
        // Step 1: Get the lecture object
        let lecture: Lecture | undefined;
        let lectureId: number | string;

        if (typeof lectureInput === "object") {
            // If a lecture object was provided, use it directly
            lecture = lectureInput;
            lectureId = lecture.id;
        } else {
            // If only an ID was provided, fetch the lecture from IndexedDB
            lectureId = lectureInput;
            lecture = await getLectureById(lectureId);
            if (!lecture) {
                return null;
            }
        }

        const currentTimestamp = Date.now();
        let documentPath: string;

        // Set the new lastPlayedPoint to the seek time
        const newLastPlayedPoint = Math.max(0, seekTime);

        // Step 2: Check if the lecture has a documentId
        if (lecture.documentId) {
            // Case 1: documentId exists - Update both IndexedDB and Firestore
            documentPath =
                lecture.documentPath ||
                `users/${userId}/lectureInfo/${lecture.documentId}`;

            // Update in IndexedDB
            await updateLecture({
                ...lecture,
                lastPlayedPoint: newLastPlayedPoint,
                lastModifiedTimestamp: currentTimestamp,
            });

            // Update in Firestore
            const docRef = doc(db, documentPath);
            await setDoc(
                docRef,
                {
                    lastPlayedPoint: newLastPlayedPoint,
                    lastModifiedTimestamp: currentTimestamp,
                },
                { merge: true }
            );

            return lectureId;
        } else {
            // Case 2: No documentId - Check Firestore for existing document
            const subCollectionRef = collection(
                db,
                `users/${userId}/lectureInfo`
            );
            const q = query(subCollectionRef, where("id", "==", lectureId));
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                // Found document in Firestore - Update it and merge with IndexedDB
                const docRef = querySnapshot.docs[0];
                documentPath = docRef.ref.path;
                const documentId = docRef.id;

                // Update in Firestore
                await setDoc(
                    docRef.ref,
                    {
                        lastPlayedPoint: newLastPlayedPoint,
                        lastModifiedTimestamp: currentTimestamp,
                    },
                    { merge: true }
                );

                // Merge Firestore document with IndexedDB
                const firestoreData = docRef.data();
                await updateLecture({
                    ...lecture,
                    ...firestoreData,
                    documentId,
                    documentPath,
                    lastPlayedPoint: newLastPlayedPoint,
                    lastModifiedTimestamp: currentTimestamp,
                });

                return lectureId;
            } else {
                // No document in Firestore - Create a new one
                const newDocRef = doc(subCollectionRef);

                // Prepare the payload with documentId and documentPath
                const payload = {
                    id: lectureId,
                    completed: false,
                    creationTimestamp: currentTimestamp,
                    downloadPlace: 0,
                    downloaded: false,
                    isFavourite: false,
                    isCompleted: false,
                    favouritePlace: 0,
                    inPrivateList: false,
                    inPublicList: false,
                    lastModifiedTimestamp: currentTimestamp,
                    lastPlayedPoint: newLastPlayedPoint,
                    totalPlayedNo: 0,
                    totalPlayedTime: 0,
                    totallength: lecture.length || 0,
                    documentId: newDocRef.id,
                    documentPath: newDocRef.path,
                };

                // Add the payload to Firestore using setDoc
                await setDoc(newDocRef, payload);

                documentPath = newDocRef.path;
                const documentId = newDocRef.id;

                // Update in IndexedDB with the new document info
                await updateLecture({
                    ...lecture,
                    documentId,
                    documentPath,
                    completed: lecture.completed || false,
                    creationTimestamp:
                        lecture.creationTimestamp || currentTimestamp,
                    downloadPlace: lecture.downloadPlace || 0,
                    downloaded: lecture.downloaded || false,
                    isCompleted: lecture.isCompleted || false,
                    isFavourite: false,
                    favouritePlace: lecture.favouritePlace || 0,
                    inPrivateList: lecture.inPrivateList || false,
                    inPublicList: lecture.inPublicList || false,
                    lastModifiedTimestamp: currentTimestamp,
                    lastPlayedPoint: newLastPlayedPoint,
                    totalPlayedNo: lecture.totalPlayedNo || 0,
                    totalPlayedTime: lecture.totalPlayedTime || 0,
                    totallength: lecture.length || 0,
                });

                return lectureId;
            }
        }
    } catch (error) {
        console.error(`Error setting lastPlayedPoint:`, error);
        return null;
    }
};

/**
 * Set lastPlayedPoint to a specific time (for seek operations), syncing between IndexedDB and Firestore
 * This function is used when user seeks to a specific position in the video/audio
 *
 * @param lectures - Array of lecture objects, lecture IDs, or a single lecture object/ID
 * @param seekTime - The time in seconds to set as the new lastPlayedPoint
 * @returns Array of successfully processed lecture IDs
 */
export const setLastPlayedPointOnSeek = async (
    lectures: (Lecture | number | string)[] | Lecture | number | string,
    seekTime: number
): Promise<(number | string)[]> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            throw new Error("User not logged in");
        }

        // Convert single lecture/ID to array if needed
        const lecturesArray = Array.isArray(lectures) ? lectures : [lectures];

        // Process all lectures in parallel
        const results = await Promise.all(
            lecturesArray.map((lecture) =>
                processSingleLectureSetLastPlayedPoint(
                    lecture,
                    userId,
                    seekTime
                )
            )
        );

        // Filter out null values (failed operations) and return successful IDs
        return results.filter((id): id is number | string => id !== null);
    } catch (error) {
        console.error("Error setting lastPlayedPoint on seek: ", error);
        throw new Error("Failed to set lastPlayedPoint on seek");
    }
};

/**
 * Update lastPlayedPoint by adding specified seconds, syncing between IndexedDB and Firestore
 *
 * Flow for each lecture:
 * 1. If lecture object is provided, use it directly; otherwise fetch from IndexedDB by ID
 * 2. If the lecture has a documentId, update both IndexedDB and Firestore
 * 3. If no documentId, check Firestore for an existing document
 * 4. If found in Firestore, update it and merge with IndexedDB
 * 5. If not found in Firestore, create a new document and update IndexedDB
 *
 * @param lectures - Array of lecture objects, lecture IDs, or a single lecture object/ID
 * @param secondsToAdd - Number of seconds to add to lastPlayedPoint (default: 60)
 * @returns Array of successfully processed lecture IDs
 */
export const updateLastPlayedPoint = async (
    lectures: (Lecture | number | string)[] | Lecture | number | string,
    secondsToAdd: number = 60
): Promise<(number | string)[]> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            throw new Error("User not logged in");
        }

        // Convert single lecture/ID to array if needed
        const lecturesArray = Array.isArray(lectures) ? lectures : [lectures];

        // Process all lectures in parallel
        const results = await Promise.all(
            lecturesArray.map((lecture) =>
                processSingleLectureUpdateLastPlayedPoint(
                    lecture,
                    userId,
                    secondsToAdd
                )
            )
        );

        // Update user video watching activity for each successful lecture
        for (const lectureInput of lecturesArray) {
            try {
                await trackUserVideoWatchingActivity(
                    lectureInput,
                    secondsToAdd
                );
            } catch (error) {
                console.error(
                    "Error updating user video watching activity:",
                    error
                );
                // Continue processing other lectures even if one fails
            }
        }

        // Filter out null values (failed operations) and return successful IDs
        return results.filter((id): id is number | string => id !== null);
    } catch (error) {
        console.error("Error updating lastPlayedPoint: ", error);
        throw new Error("Failed to update lastPlayedPoint");
    }
};

/**
 * Check if lecture has been watched for 10% or more of its total length
 * @param lecture - The lecture object
 * @returns boolean indicating if 10% threshold is reached
 */
const hasReached10PercentThreshold = (lecture: Lecture): boolean => {
    const totalLength = lecture.length || lecture.totallength || 0;
    const lastPlayedPoint = lecture.lastPlayedPoint || 0;

    if (totalLength === 0) return false;

    const percentageWatched = (lastPlayedPoint / totalLength) * 100;
    return percentageWatched >= 10;
};

/**
 * Update user video watching activity for the current date
 * Adds specified seconds to videoListen and appropriate category, tracks played IDs only when 10% threshold is reached
 *
 * @param lectureInput - Either a lecture ID or a lecture object
 * @param userId - The current user ID
 * @param seconds - Number of seconds to add (default: 60)
 * @returns Promise<void>
 */
const updateUserVideoWatchingActivity = async (
    lectureInput: number | string | Lecture,
    userId: string,
    seconds: number = 60
): Promise<void> => {
    try {
        // Step 1: Get the lecture object
        let lecture: Lecture | undefined;
        let lectureId: number | string;

        if (typeof lectureInput === "object") {
            lecture = lectureInput;
            lectureId = lecture.id;
        } else {
            lectureId = lectureInput;
            lecture = await getLectureById(lectureId);
            if (!lecture) {
                console.warn(
                    `Lecture with ID ${lectureId} not found in IndexedDB`
                );
                return;
            }
        }

        // Step 2: Get current date info
        const currentDate = new Date();
        const currentTimestamp = Date.now();
        // Use dd-mm-yyyy format for Firestore document path
        const firestoreDocumentId = formatDateToDMYYYY(currentDate);
        const documentPath = `users/${userId}/listenInfo/${firestoreDocumentId}`;
        // Use the Firestore document ID for documentId field
        const documentId = firestoreDocumentId;
        const dateOfRecord = getDateComponents(currentDate);

        // Step 3: Determine the category for this lecture
        const category = mapLectureCategoryToListenCategory(
            lecture.category || []
        );

        // Step 4: Check if 10% threshold is reached
        const shouldAddToPlayedIds = hasReached10PercentThreshold(lecture);

        // Step 5: Check if document exists for today
        const docRef = doc(db, documentPath);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            // Document exists - update it
            const existingData = docSnap.data() as UserListenInfo;

            // Check if this video ID is already played today
            const isAlreadyPlayed = existingData.playedIds?.includes(
                Number(lectureId)
            );

            // Update listen details for the category
            const updatedListenDetails = { ...existingData.listenDetails };
            updatedListenDetails[category] =
                (updatedListenDetails[category] || 0) + seconds;

            // Only add to playedIds if 10% threshold is reached and not already played
            let updatedPlayedIds = existingData.playedIds || [];
            if (shouldAddToPlayedIds && !isAlreadyPlayed) {
                updatedPlayedIds = [...updatedPlayedIds, Number(lectureId)];
            }

            // Prepare update data
            const updateData = {
                videoListen: existingData.videoListen + seconds,
                lastModifiedTimestamp: currentTimestamp,
                listenDetails: updatedListenDetails,
                playedIds: updatedPlayedIds,
                playedBy: existingData.playedBy?.includes(userId)
                    ? existingData.playedBy
                    : [...(existingData.playedBy || []), userId],
            };

            await setDoc(docRef, updateData, { merge: true });
        } else {
            // Document doesn't exist - create new one
            const newListenDetails: ListenDetails = {
                BG: 0,
                CC: 0,
                SB: 0,
                Seminars: 0,
                VSN: 0,
                others: 0,
            };

            // Set the appropriate category to the specified seconds
            newListenDetails[category] = seconds;

            // Only add to playedIds if 10% threshold is reached
            const initialPlayedIds = shouldAddToPlayedIds ? [Number(lectureId)] : [];

            const newDocumentData: UserListenInfo = {
                audioListen: 0,
                creationTimestamp: currentTimestamp,
                date: currentDate.toString(),
                dateOfRecord,
                documentId,
                documentPath,
                lastModifiedTimestamp: currentTimestamp,
                listenDetails: newListenDetails,
                playedBy: [userId],
                playedIds: initialPlayedIds,
                videoListen: seconds,
            };

            await setDoc(docRef, newDocumentData);
        }

        // Update user statistics (streak tracking) and sync statistics data every 60 seconds
        try {
            await trackUserStatistics();
            // Also fetch latest statistics to keep data in sync
            await fetchUserStatistics();
        } catch (error) {
            console.error("Error updating user statistics:", error);
            // Don't throw error here to avoid breaking the main video watching activity tracking
        }
    } catch (error) {
        console.error(`Error updating user video watching activity:`, error);
        throw error;
    }
};

/**
 * Update user video watching activity with custom seconds (for partial tracking)
 * @param lectureInput - Either a lecture ID or a lecture object
 * @param seconds - Number of seconds to add (default: 60)
 * @returns Promise<void>
 */
export const updateUserVideoWatchingActivityWithSeconds = async (
    lectureInput: number | string | Lecture,
    seconds: number = 60
): Promise<void> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            throw new Error("User not logged in");
        }

        // Step 1: Get the lecture object
        let lecture: Lecture | undefined;
        let lectureId: number | string;

        if (typeof lectureInput === "object") {
            lecture = lectureInput;
            lectureId = lecture.id;
        } else {
            lectureId = lectureInput;
            lecture = await getLectureById(lectureId);
            if (!lecture) {
                console.warn(
                    `Lecture with ID ${lectureId} not found in IndexedDB`
                );
                return;
            }
        }

        // Step 2: Get current date info
        const currentDate = new Date();
        const currentTimestamp = Date.now();
        // Use dd-mm-yyyy format for Firestore document path
        const firestoreDocumentId = formatDateToDMYYYY(currentDate);
        const documentPath = `users/${userId}/listenInfo/${firestoreDocumentId}`;
        // Use the Firestore document ID for documentId field
        const documentId = firestoreDocumentId;
        const dateOfRecord = getDateComponents(currentDate);

        // Step 3: Determine the category for this lecture
        const category = mapLectureCategoryToListenCategory(
            lecture.category || []
        );

        // Step 4: Check if 10% threshold is reached
        const shouldAddToPlayedIds = hasReached10PercentThreshold(lecture);

        // Step 5: Check if document exists for today
        const docRef = doc(db, documentPath);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            // Document exists - update it
            const existingData = docSnap.data() as UserListenInfo;

            // Check if this video ID is already played today
            const isAlreadyPlayed = existingData.playedIds?.includes(
                Number(lectureId)
            );

            // Update listen details for the category
            const updatedListenDetails = { ...existingData.listenDetails };
            updatedListenDetails[category] =
                (updatedListenDetails[category] || 0) + seconds;

            // Only add to playedIds if 10% threshold is reached and not already played
            let updatedPlayedIds = existingData.playedIds || [];
            if (shouldAddToPlayedIds && !isAlreadyPlayed) {
                updatedPlayedIds = [...updatedPlayedIds, Number(lectureId)];
            }

            // Prepare update data
            const updateData = {
                videoListen: existingData.videoListen + seconds,
                lastModifiedTimestamp: currentTimestamp,
                listenDetails: updatedListenDetails,
                playedIds: updatedPlayedIds,
                playedBy: existingData.playedBy?.includes(userId)
                    ? existingData.playedBy
                    : [...(existingData.playedBy || []), userId],
            };

            await setDoc(docRef, updateData, { merge: true });
        } else {
            // Document doesn't exist - create new one
            const newListenDetails: ListenDetails = {
                BG: 0,
                CC: 0,
                SB: 0,
                Seminars: 0,
                VSN: 0,
                others: 0,
            };

            // Set the appropriate category to the specified seconds
            newListenDetails[category] = seconds;

            // Only add to playedIds if 10% threshold is reached
            const initialPlayedIds = shouldAddToPlayedIds ? [Number(lectureId)] : [];

            const newDocumentData: UserListenInfo = {
                audioListen: 0,
                creationTimestamp: currentTimestamp,
                date: currentDate.toString(),
                dateOfRecord,
                documentId,
                documentPath,
                lastModifiedTimestamp: currentTimestamp,
                listenDetails: newListenDetails,
                playedBy: [userId],
                playedIds: initialPlayedIds,
                videoListen: seconds,
            };

            await setDoc(docRef, newDocumentData);
        }

        // Update user statistics (streak tracking) and sync statistics data every 60 seconds
        try {
            await trackUserStatistics();
            // Also fetch latest statistics to keep data in sync
            await fetchUserStatistics();
        } catch (error) {
            console.error("Error updating user statistics:", error);
            // Don't throw error here to avoid breaking the main video watching activity tracking
        }
    } catch (error) {
        console.error(
            `Error updating user video watching activity with seconds:`,
            error
        );
        throw error;
    }
};

/**
 * Public function to update user video watching activity
 * This can be called from other services to track video watching activity
 *
 * @param lectures - Array of lecture objects, lecture IDs, or a single lecture object/ID
 * @param seconds - Number of seconds to add (default: 60)
 * @returns Promise<void>
 */
const trackUserVideoWatchingActivity = async (
    lectures: (Lecture | number | string)[] | Lecture | number | string,
    seconds: number = 60
): Promise<void> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            throw new Error("User not logged in");
        }

        // Convert single lecture/ID to array if needed
        const lecturesArray = Array.isArray(lectures) ? lectures : [lectures];

        // Update user video watching activity for each lecture
        for (const lectureInput of lecturesArray) {
            try {
                await updateUserVideoWatchingActivity(
                    lectureInput,
                    userId,
                    seconds
                );
            } catch (error) {
                console.error(
                    "Error updating user video watching activity:",
                    error
                );
                // Continue processing other lectures even if one fails
            }
        }
    } catch (error) {
        console.error("Error tracking user video watching activity:", error);
        throw error;
    }
};
