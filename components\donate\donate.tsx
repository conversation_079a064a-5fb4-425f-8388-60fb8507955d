"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "antd";
import { CiShare1 } from "react-icons/ci";
import { MdShare } from "react-icons/md";

const open_Sans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const inter = Inter({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Donate = () => {
  const router = useRouter();

  return (
    <div className={`w-full h-full ${roboto.className}`}>
      <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
        {/* Heading */}
        <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
          Donate
        </h1>
      </div>
      <div className="flex flex-col px-4 py-6">
        <p>
          Donations are welcome for the upkeep of this site and for all other
          programs that His Holiness Bhakti Vikasa Swami is involved in.
        </p>
        <button
          className="rounded-lg my-4 w-[200px] h-[50px] flex justify-center items-center text-[22px] bg-primary text-white font-bold outline-none focus:outline-none"
          onClick={() =>
            window.open("https://donate.bvksmedia.net/") // https://donate.bvksmedia.com/b/fZe9Cld6L2n31HydQR
          }
        >
          <CiShare1
            className="mt-[2px] text-white mr-2"
            size={18}
            style={{ strokeWidth: 2 }}
          />{" "}
          Donate
        </button>
        <p>If you face any difficulty while donating, please contact us:</p>
        <p>
          Email:{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-primary cursor-pointer"
          >
            <EMAIL>
          </a>
        </p>

        <p>
          WhatsApp and Regular phone calls:{" "}
          <a href="tel:+917415128866" className="text-primary cursor-pointer">
            +91 7415128866
          </a>
        </p>
      </div>
    </div>
  );
};

export default Donate;
