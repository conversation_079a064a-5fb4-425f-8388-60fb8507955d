"use client";

import React, { useState } from "react";
import { useVideoContext } from "@/src/context/video.context";
import { <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON>eg<PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaShare } from "react-icons/fa";
import { CgTranscript } from "react-icons/cg";
import { FiBookmark } from "react-icons/fi";
import { GrLocation } from "react-icons/gr";
import { MdOutlineDateRange } from "react-icons/md";
import { VscCircleFilled } from "react-icons/vsc";
import { formatDateObjToDDMMYYYY } from "@/src/utils/timeFormat";
import AddToPlaylistModal from "../Modal/addToPlaylistModal";
import ShareModal from "../Modal/shareModal";
import { Poppins } from "next/font/google";
import { CgPlayList } from "react-icons/cg";
import { useRouter, usePathname } from "next/navigation";
import { TbHeadphonesFilled } from "react-icons/tb";
import { useAudioContext } from "@/src/context/audio.context";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface VideoPlayerProps {
    lectureData: any;
}

const VideoPlayerInfo: React.FC<VideoPlayerProps> = ({ lectureData }: any) => {
    const {
        currentVideo,
        isFavorite,
        isCompleted,
        isProcessing,
        toggleFavorite,
        toggleCompleted,
        setIsShareModalOpen,
        isShareModalOpen,
    } = useVideoContext();

    const router = useRouter();
    const pathname = usePathname();
    const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] =
        useState(false);
    const [showFullDescription, setShowFullDescription] = useState(false);
    const { playLecture, playAudio } = useAudioContext();

    const formatDate = (dateObj: any) => {
        if (!dateObj) return "";
        return formatDateObjToDDMMYYYY(dateObj);
    };

    const truncateDescription = (text: string, maxLength: number = 150) => {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + "...";
    };

    const navigateToTranscription = () => {
        if (currentVideo.id) {
            router.push(`/transcription/${currentVideo.id}`);
        }
    };

    const navigateToAudioPlayer = () => {
        console.log("lectureData", lectureData);
        if (lectureData) {
            router.back();
            playLecture(lectureData);
        }
    };

    return (
        <div className={`p-6 ${poppins.className}`}>
            {/* Title */}
            <h1 className="text-xl font-semibold text-gray-900 mb-3 leading-tight">
                {currentVideo.title}
            </h1>

            {/* Video Meta Info */}
            <div className="flex flex-wrap items-center gap-3 text-sm text-gray-600 mb-4">
                {currentVideo.dateOfRecording && (
                    <div className="flex items-center gap-1">
                        <MdOutlineDateRange size={14} />
                        <span>{formatDate(currentVideo.dateOfRecording)}</span>
                    </div>
                )}

                {currentVideo.place && (
                    <>
                        <VscCircleFilled size={3} className="text-gray-400" />
                        <div className="flex items-center gap-1">
                            <GrLocation size={12} />
                            <span>{currentVideo.place}</span>
                        </div>
                    </>
                )}

                {currentVideo.category && (
                    <>
                        <VscCircleFilled size={3} className="text-gray-400" />
                        <div className="flex items-center gap-1">
                            <FiBookmark size={12} />
                            <span>{currentVideo.category}</span>
                        </div>
                    </>
                )}

                {currentVideo.language && (
                    <>
                        <VscCircleFilled size={3} className="text-gray-400" />
                        <span>{currentVideo.language}</span>
                    </>
                )}
            </div>

            {/* Action Buttons Row */}
            <div className="flex flex-wrap items-center gap-3">
                {/* Like/Favorite Button */}
                <button
                    type="button"
                    onClick={toggleFavorite}
                    disabled={isProcessing}
                    className={`youtube-button flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
                        isFavorite
                            ? "bg-gray-900 text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                >
                    {isFavorite ? (
                        <FaHeart className="text-red-500" size={16} />
                    ) : (
                        <FaRegHeart size={16} />
                    )}
                    <span className="text-sm font-medium">
                        {isFavorite ? "Liked" : "Like"}
                    </span>
                </button>

                {/* Mark as Complete */}
                <button
                    type="button"
                    onClick={toggleCompleted}
                    disabled={isProcessing}
                    className={`youtube-button flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
                        isCompleted
                            ? "bg-green-100 text-green-700"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                >
                    <FaCheck size={14} />
                    <span className="text-sm font-medium">
                        {isCompleted ? "Completed" : "Complete"}
                    </span>
                </button>

                {/* Add to Playlist */}
                <button
                    type="button"
                    onClick={() => setIsAddToPlaylistModalOpen(true)}
                    className="youtube-button flex items-center gap-2 px-4 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all"
                >
                    <CgPlayList size={20} />
                    <span className="text-sm font-medium">Add to playlist</span>
                </button>

                {/* Share */}
                {/* <button
                    type="button"
                    onClick={() => setIsShareModalOpen(true)}
                    className="youtube-button flex items-center gap-2 px-4 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all"
                >
                    <FaShare size={14} />
                    <span className="text-sm font-medium">Share</span>
                </button> */}

                {/* Transcription */}
                {currentVideo?.audioSrc && (
                    <button
                        type="button"
                        onClick={navigateToTranscription}
                        className="youtube-button flex items-center gap-2 px-4 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all"
                    >
                        <CgTranscript size={18} />
                        <span className="text-sm font-medium">
                            Transcription
                        </span>
                    </button>
                )}

                {/* Switch to Audio Player */}
                <button
                    type="button"
                    onClick={navigateToAudioPlayer}
                    className="youtube-button flex items-center gap-2 px-4 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all"
                >
                    <TbHeadphonesFilled size={18} />
                    <span className="text-sm font-medium">Audio player</span>
                </button>
            </div>

            {/* Description */}
            {/* {currentVideo.description && (
                <div className="bg-gray-50 rounded-xl p-4">
                    <div className="text-gray-700 leading-relaxed">
                        {showFullDescription ? (
                            <div>
                                <p className="whitespace-pre-line text-sm">
                                    {currentVideo.description}
                                </p>
                                <button
                                    type="button"
                                    onClick={() =>
                                        setShowFullDescription(false)
                                    }
                                    className="text-gray-600 hover:text-gray-800 font-medium mt-3 text-sm"
                                >
                                    Show less
                                </button>
                            </div>
                        ) : (
                            <div>
                                <p className="whitespace-pre-line text-sm">
                                    {truncateDescription(
                                        currentVideo.description
                                    )}
                                </p>
                                {currentVideo.description.length > 150 && (
                                    <button
                                        type="button"
                                        onClick={() =>
                                            setShowFullDescription(true)
                                        }
                                        className="text-gray-600 hover:text-gray-800 font-medium mt-3 text-sm"
                                    >
                                        ...more
                                    </button>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            )} */}

            {/* Modals */}
            <AddToPlaylistModal
                isModalOpen={isAddToPlaylistModalOpen}
                setIsModalOpen={setIsAddToPlaylistModalOpen}
                selectedFiles={currentVideo}
            />

            <ShareModal
                isModalOpen={isShareModalOpen}
                setIsModalOpen={setIsShareModalOpen}
                title={currentVideo.title}
                url={typeof window !== "undefined" ? window.location.href : ""}
            />
        </div>
    );
};

export default VideoPlayerInfo;
