"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

const open_Sans = Open_Sans({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const inter = Inter({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});
const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const Bhakti = () => {
  const router = useRouter();

  return (
    <div
      id="scrollableDiv"
      className={`w-full h-full overflow-x-hidden overflow-y-auto scrollbar ${roboto.className} bg-white`}
    >
      <div className="flex flex-col p-6 ">
        {/* Back Button */}
        {/* <div
          className="flex gap-2 cursor-pointer"
          onClick={() => router.push("/about")}
        >
          <FaArrowLeft className="text-primary mt-[5px]" />
          <h1 className="text-[18px] text-primary font-bold">Go Back</h1>
        </div> */}
        <button
          className="flex items-center gap-2 text-[20px] text-primary font-bold outline-none focus:outline-none"
          onClick={() => router.push("/about")}
        >
           <FaArrowLeft className="mt-[2px]" size={16}/> Go Back
        </button>

        {/* Section */}
        <div className="flex flex-col lg:flex-row justify-between gap-6 mt-6">
          {/* Image Section */}
          <div className="w-full lg:w-[50%] flex justify-center lg:justify-start order-2 lg:order-none mt-4 h-[400px]">
            <Image
              src="/images/about/about2.png"
              alt="Śrīla Prabhupāda"
              width={350}
              height={400}
              className="h-full w-[90%]"
            />
          </div>

          {/* Text Content */}
          <div className="flex flex-col justify-start w-full lg:w-[50%] order-1 lg:order-none">
            <p className="text-[20px] text-secondary font-bold mb-2">
              A DISCIPLE OF HIS DIVINE GRACE A.C. BHAKTIVEDANTA SWAMI PRABHUPĀDA
            </p>
            <h1 className="text-[32px] lg:text-[44px] text-[#1b1f3b] font-bold">
              Bhakti Vikāsa Swami
            </h1>
            <p className="text-[15px] text-[#1b1f3b] font-normal mt-4 mb-4 text-justify">
              His Holiness Bhakti Vikāsa Swami appeared in this world in 1957 in
              England. He joined the International Society for Krishna
              Consciousness (ISKCON) in London in 1975 and was initiated in that
              year with the name Ilāpati dāsa by ISKCON’s founder-ācārya, His
              Divine Grace A.C. Bhaktivedānta Swami Prabhupāda.From 1977 to 1979
              His Holiness was based in India, mostly traveling in West Bengal
              preaching Kṛṣṇa consciousness and distributing Śrīla Prabhupāda’s
              books. He then spent the following ten years helping to pioneer
              ISKCON’s preaching in Bangladesh, Burma, Thailand, and Malaysia.
            </p>
            <p className="text-[15px] text-[#1b1f3b] font-normal mt-4 mb-4 text-justify">
              In 1989 he was granted the order of sannyāsa, receiving the name
              Bhakti Vikāsa Swami, and again made his base in India.He has since
              travelled widely throughout the subcontinent, lecturing in
              English, Hindi, and Bengali. His Holiness also travels to and
              preaches Kṛṣṇa consciousness in other parts of the world.
            </p>
          </div>
        </div>
        <div className="flex flex-col justify-start w-full mt-4">
          <p className="text-[15px] text-[#1b1f3b] font-normal mt-4 mb-4 text-justify">
            He continues to write books and magazine articles. His books have
            been translated into more than fifteen languages.
          </p>
          <div className="flex flex-wrap gap-6 justify-center mt-4 mb-4">
            {[...Array(5)].map((_, i) => (
              <Image
                key={i}
                src={`/images/about/about2-${i + 1}.png`}
                alt={`Śrīla Prabhupāda ${i + 1}`}
                width={240}
                height={250}
                className="object-contain"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Bhakti;
