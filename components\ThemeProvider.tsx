"use client";

import { useEffect } from 'react';
import { loadThemeVariables } from '@/src/utils/themeLoader';

/**
 * ThemeProvider component
 * Loads theme variables on the client side
 */
export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Load theme variables when the component mounts
    loadThemeVariables();
  }, []);

  return <>{children}</>;
}
