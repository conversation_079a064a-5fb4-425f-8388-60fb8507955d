importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js");

// Initialize Firebase
firebase.initializeApp({
    apiKey: "AIzaSyCXFbWCZyoeHL8hxbojbexI-kRKPDcirj8",
    authDomain: "bvks-ee662.firebaseapp.com",
    projectId: "bvks-ee662",
    storageBucket: "bvks-ee662.appspot.com",
    messagingSenderId: "184985407900",
    appId: "1:184985407900:web:c3632eef8c2104e727b859",
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
    console.log("[firebase-messaging-sw.js] Received background message:", payload);

    try {
        // Handle both notification and data-only messages
        let notificationTitle = '';
        let notificationOptions = {};

        if (payload.notification) {
            // Standard notification payload
            notificationTitle = payload.notification.title || 'New Notification';
            notificationOptions = {
                body: payload.notification.body || '',
                icon: payload.notification.icon || '',
                badge: '',
                tag: 'notification-tag',
                requireInteraction: false,
                data: {
                    url: payload.fcmOptions?.link || payload.data?.link || '/',
                    ...payload.data
                },
            };
        } else if (payload.data) {
            // Data-only message - create notification from data
            notificationTitle = payload.data.title || 'New Notification';
            notificationOptions = {
                body: payload.data.body || '',
                icon: payload.data.icon || '',
                badge: '',
                tag: 'notification-tag',
                requireInteraction: false,
                data: {
                    url: payload.data.link || '/',
                    ...payload.data
                },
            };
        } else {
            console.warn("[firebase-messaging-sw.js] No notification or data payload found");
            return;
        }

        // Show notification with error handling
        console.log("[firebase-messaging-sw.js] Showing notification:", notificationTitle);

        self.registration.showNotification(notificationTitle, notificationOptions)
            .then(() => {
                console.log("[firebase-messaging-sw.js] Notification displayed successfully");
            })
            .catch((error) => {
                console.error("[firebase-messaging-sw.js] Error showing notification:", error);
            });

    } catch (error) {
        console.error("[firebase-messaging-sw.js] Error processing background message:", error);
    }
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
    console.log("[firebase-messaging-sw.js] Notification click received:", event);

    event.notification.close();

    // Get the URL from notification data
    const urlToOpen = event.notification.data?.url || '/';

    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then((clientList) => {
                // Check if there's already a window/tab open with the target URL
                for (const client of clientList) {
                    if (client.url === urlToOpen && 'focus' in client) {
                        return client.focus();
                    }
                }
                // If no existing window/tab, open a new one
                if (clients.openWindow) {
                    return clients.openWindow(urlToOpen);
                }
            })
            .catch((error) => {
                console.error("[firebase-messaging-sw.js] Error handling notification click:", error);
            })
    );
});

// Service Worker activation
self.addEventListener('activate', (event) => {
    console.log("[firebase-messaging-sw.js] Service Worker activated");
    event.waitUntil(self.clients.claim());
});

// Service Worker installation
self.addEventListener('install', (event) => {
    console.log("[firebase-messaging-sw.js] Service Worker installed");
    self.skipWaiting();
});