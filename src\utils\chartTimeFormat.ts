/**
 * Utility functions for formatting time in charts
 */

/**
 * Converts hours to minutes if less than 1 hour, otherwise keeps as hours
 * @param hours Number of hours
 * @returns Formatted time string
 */
export const formatChartTime = (hours: number): string => {
    if (hours < 1) {
        const minutes = Math.round(hours * 60);
        return `${minutes}m`;
    }
    
    // If hours is a whole number, show without decimal
    if (hours % 1 === 0) {
        return `${hours}h`;
    }
    
    // Show one decimal place for hours
    return `${hours.toFixed(1)}h`;
};

/**
 * Determines the appropriate unit and maximum value for chart axis
 * @param maxValue Maximum value in hours
 * @returns Object with unit, max value, and formatter function
 */
export const getChartAxisConfig = (maxValue: number) => {
    const shouldUseMinutes = maxValue < 1;
    
    if (shouldUseMinutes) {
        const maxMinutes = Math.ceil(maxValue * 60);
        const roundedMax = Math.ceil(maxMinutes / 10) * 10; // Round to nearest 10 minutes
        
        return {
            unit: 'minutes',
            maxValue: roundedMax / 60, // Convert back to hours for internal use
            displayMax: roundedMax,
            formatter: (value: number) => `${Math.round(value * 60)}m`,
            yAxisMax: roundedMax
        };
    } else {
        const roundedMax = Math.ceil(maxValue);
        
        return {
            unit: 'hours',
            maxValue: roundedMax,
            displayMax: roundedMax,
            formatter: (value: number) => value % 1 === 0 ? `${value}h` : `${value.toFixed(1)}h`,
            yAxisMax: roundedMax
        };
    }
};

/**
 * Formats time for progress chart display
 * @param totalSeconds Total seconds
 * @returns Formatted time string (e.g., "2h 30m" or "45m")
 */
export const formatProgressTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    
    if (hours === 0) {
        return `${minutes}m`;
    }
    
    if (minutes === 0) {
        return `${hours}h`;
    }
    
    return `${hours}h ${minutes}m`;
};

/**
 * Calculates the maximum value for circular progress to make it look complete
 * @param categories Array of category data with hours
 * @returns Adjusted maximum value
 */
export const getProgressMaxValue = (categories: { hours: number }[]): number => {
    const maxHours = Math.max(...categories.map(cat => cat.hours));
    
    // Add 20% padding to make circles look more complete
    const paddedMax = maxHours * 1.2;
    
    // Round up to a nice number
    if (paddedMax < 10) {
        return Math.ceil(paddedMax);
    } else if (paddedMax < 100) {
        return Math.ceil(paddedMax / 5) * 5; // Round to nearest 5
    } else {
        return Math.ceil(paddedMax / 10) * 10; // Round to nearest 10
    }
};

/**
 * Formats category hours for display in progress chart
 * @param hours Number of hours
 * @returns Formatted string
 */
export const formatCategoryTime = (hours: number): string => {
    if (hours < 1) {
        const minutes = Math.round(hours * 60);
        return `${minutes} m`;
    }

    if (hours % 1 === 0) {
        return `${hours} h`;
    }

    return `${hours.toFixed(1)} h`;
};

/**
 * Formats time in h m s format without decimals for category display
 * @param totalSeconds Total seconds
 * @returns Formatted time string (e.g., "2h 30m 45s" or "45m 30s" or "30s")
 */
// export const formatCategoryTimeHMS = (totalSeconds: number): string => {
//     const hours = Math.floor(totalSeconds / 3600);
//     const minutes = Math.floor((totalSeconds % 3600) / 60);
//     const seconds = Math.floor(totalSeconds % 60);

//     const parts: string[] = [];

//     if (hours > 0) {
//         parts.push(`${hours}h`);
//     }

//     if (minutes > 0) {
//         parts.push(`${minutes}m`);
//     }

//     if (seconds > 0 || parts.length === 0) {
//         parts.push(`${seconds}s`);
//     }

//     return parts.join(' ');
// };

export const formatCategoryTimeHMS = (totalSeconds: number): string => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    // Do not include seconds when hours exist
  } else if (minutes > 0) {
    parts.push(`${minutes}m`);
    if (seconds > 0) parts.push(`${seconds}s`);
  } else {
    parts.push(`${seconds}s`);
  }

  return parts.join(' ');
};
