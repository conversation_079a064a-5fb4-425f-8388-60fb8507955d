"use client";

import React from "react";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { Roboto } from "next/font/google";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

interface FullScreenLoaderProps {
  message?: string;
  visible: boolean;
}

const FullScreenLoader: React.FC<FullScreenLoaderProps> = ({
  message = "Loading content...",
  visible,
}) => {
  if (!visible) return null;

  return (
    <div
      className={`fixed inset-0 flex flex-col items-center justify-center bg-white z-50 ${roboto.className}`}
    >
      {/* <Spin
        indicator={
          <LoadingOutlined
            style={{ fontSize: 48, color: "var(--primary-color)" }}
            spin
          />
        }
      /> */}
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      <p className="mt-6 text-lg font-medium text-gray-700">{message}</p>
      <p className="mt-2 text-sm text-gray-500">
        Please wait while we prepare your content
      </p>
    </div>
  );
};

export default FullScreenLoader;
