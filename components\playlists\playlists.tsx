"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import SortBy from "../helperComponents/sortBy";
import PlaylistType from "../helperComponents/playlistType";
import { Button, message } from "antd";
import { CgPlayListAdd } from "react-icons/cg";
import CreatePlaylistModal from "../Modal/createPlaylistModal";
import PlaylistCardSkeleton from "../playlistCard/playlistCardSkeleton";
import {
    applyPlaylistSort,
    fetchPlaylists,
} from "@/src/services/playlist.service";
import { useFilterContext } from "@/src/context/filter.context";
import { useSearchContext } from "@/src/context/search.context";
import InfiniteScroll from "react-infinite-scroll-component";
import PlaylistCard from "../playlistCard/playlistCard";

const open_Sans = Open_Sans({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const inter = Inter({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const roboto = Roboto({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

const Playlists = () => {
    const { searchQuery, search, deepSearch, isSearching, setIsSearching } =
        useSearchContext();
    const { sortBy, playlistShow, setPlaylistShow } = useFilterContext();

    const [allPlaylists, setAllPlaylists] = useState<any>([]);
    const [displayedPlaylists, setDisplayedPlaylists] = useState<any>([]);
    const [isCreatePlaylistModalOpen, setIsCreatePlaylistModalOpen] =
        useState(false);
    const [isDeepSearchLoading, setIsDeepSearchLoading] = useState(false);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [newAddedPlaylist, setNewAddedPlaylist] = useState<any>({});
    const resultsPerPage = 20;

    const isFirstRender = useRef(true);
    const isSearchInitialMount = useRef(true);
    const isDeepSearchInitialMount = useRef(true);

    // Load initial playlists data
    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            loadPlaylistsData();
        }
    }, []);

    useEffect(() => {
        if (newAddedPlaylist) {
            loadPlaylistsData();
        }
    }, [newAddedPlaylist]);

    // Trigger reload when playlistShow changes
    useEffect(() => {
        if (!isFirstRender.current && !deepSearch) {
            loadPlaylistsData();
        }
    }, [playlistShow]);

    // Handle search
    useEffect(() => {
        if (isSearchInitialMount.current) {
            isSearchInitialMount.current = false;
            return;
        }
        handleSearch();
    }, [search]);

    // Handle filter/sort changes
    useEffect(() => {
        if (!isFirstRender.current) {
            if (sortBy !== 1 || allPlaylists.length === 1) {
                setPage(1);
                const sortedPlaylists = applyPlaylistSort(allPlaylists, sortBy);
                setAllPlaylists(sortedPlaylists);
                const firstPagePlaylists = sortedPlaylists.slice(
                    0,
                    resultsPerPage
                );
                setDisplayedPlaylists(firstPagePlaylists);
                setHasMore(sortedPlaylists.length > resultsPerPage);
            } else {
                loadPlaylistsData();
            }
        }
    }, [sortBy]);

    const loadPlaylistsData = async () => {
        try {
            setLoading(true);

            const playlists = await fetchPlaylists(
                playlistShow,
                sortBy,
                searchQuery
            );

            setAllPlaylists(playlists);

            // Reset pagination and load first page
            setPage(1);
            const firstPagePlaylists = playlists.slice(0, resultsPerPage);
            setDisplayedPlaylists(firstPagePlaylists);
            setHasMore(playlists.length > resultsPerPage);
        } catch (error) {
            console.error("Error loading playlists:", error);
            message.error(
                "We couldn't fetch playlists at this moment, please try again later."
            );
            setAllPlaylists([]);
            setDisplayedPlaylists([]);
            setHasMore(false);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async () => {
        if (!searchQuery.trim()) {
            setIsSearching(false);
            await loadPlaylistsData();
            return;
        }

        try {
            setIsDeepSearchLoading(true);
            setIsSearching(true);
            setIsLoadingMore(true);

            const playlists = await fetchPlaylists(
                playlistShow,
                sortBy,
                searchQuery
            );

            setAllPlaylists(playlists);
            setPage(1);
            setDisplayedPlaylists(playlists.slice(0, resultsPerPage));
            setHasMore(playlists.length > resultsPerPage);
        } catch (error) {
            console.error("Error searching playlists:", error);
            setAllPlaylists([]);
            setDisplayedPlaylists([]);
            setHasMore(false);
        } finally {
            setIsDeepSearchLoading(false);
            setTimeout(() => {
                setIsLoadingMore(false);
            }, 800);
        }
    };

    // Function to load more results for infinite scroll
    const loadMoreResults = useCallback(() => {
        if (!hasMore || isLoadingMore) return;

        setIsLoadingMore(true);

        setTimeout(() => {
            const nextPage = page + 1;
            const startIndex = (nextPage - 1) * resultsPerPage;
            const endIndex = startIndex + resultsPerPage;

            const nextResults = allPlaylists.slice(startIndex, endIndex);

            if (nextResults.length > 0) {
                setDisplayedPlaylists((prev: any) => [...prev, ...nextResults]);
                setPage(nextPage);
            }

            // Check if we've loaded all results
            if (endIndex >= allPlaylists.length) {
                setHasMore(false);
            }

            setIsLoadingMore(false);
        }, 500);
    }, [hasMore, isLoadingMore, page, allPlaylists, resultsPerPage]);

    return (
        <div className={`w-full h-full ${roboto.className}`}>
            <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
                {/* Heading */}
                <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
                    Playlists
                </h1>
                <div className="flex gap-2 items-end">
                    <PlaylistType />
                    <SortBy />
                    <Button
                        className={`h-[30px] sm:h-[32px] flex gap-1 sm:gap-2 items-center pt-[2px] px-1 md:px-3 text-[12px] sm:text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[10px] sm:rounded-[12px] hover:!border-primary cursor-pointer transition-all relative`}
                        onClick={() => setIsCreatePlaylistModalOpen(true)}
                    >
                        <CgPlayListAdd className="text-[24px] md:text-[20px] text-text-primary mt-1" />
                        <h2 className="text-[12px] sm:text-[13px] leading-5 font-[400] text-text-primary md:block hidden">
                            New playlist
                        </h2>
                    </Button>
                </div>
            </div>

            {/* Playlists List */}
            <div
                id="scrollableDiv"
                className="w-full h-[calc(100%-60px)] md:h-[calc(100%-84px)] p-5 pb-40 !overflow-x-hidden overflow-y-auto scrollbar"
            >
                {!isSearching ? (
                    <div className="w-full flex flex-col gap-4">
                        {loading ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <PlaylistCardSkeleton
                                            key={`history-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : allPlaylists.length === 0 ? (
                            <div className="text-center py-20">
                                <p className="text-lg text-gray-500">
                                    No Playlist found
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    {playlistShow === 1
                                        ? "There are no playlists created yet."
                                        : playlistShow === 2
                                        ? "You haven't created any private playlists yet."
                                        : playlistShow === 3
                                        ? "You haven't created any public playlists yet."
                                        : `There are no public playlists available.`}
                                </p>
                            </div>
                        ) : (
                            <InfiniteScroll
                                dataLength={displayedPlaylists.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <PlaylistCardSkeleton
                                                    key={`history-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedPlaylists.map((playlist: any) => (
                                        <PlaylistCard
                                            key={playlist.id}
                                            playlistData={playlist}
                                            setDisplayedPlaylists={
                                                setDisplayedPlaylists
                                            }
                                            setAllPlaylists={setAllPlaylists}
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        )}
                    </div>
                ) : (
                    <div className="w-full flex flex-col gap-4">
                        <div className="flex flex-col gap-1">
                            <h1 className="text-[22px] min-[460px]:text-[24px] font-[600] leading-8">
                                {isSearching &&
                                    `Search Results for "${searchQuery}"`}
                            </h1>
                        </div>

                        {isDeepSearchLoading ? (
                            // Show skeleton loading during deep search
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                {Array(15)
                                    .fill(0)
                                    .map((_, index) => (
                                        <PlaylistCardSkeleton
                                            key={`deep-search-skeleton-${index}`}
                                        />
                                    ))}
                            </div>
                        ) : displayedPlaylists.length > 0 ? (
                            <InfiniteScroll
                                dataLength={displayedPlaylists.length}
                                next={loadMoreResults}
                                hasMore={hasMore}
                                loader={
                                    <div className="w-full grid grid-cols-5 gap-5 mt-4">
                                        {Array(5)
                                            .fill(0)
                                            .map((_, index) => (
                                                <PlaylistCardSkeleton
                                                    key={`search-skeleton-${index}`}
                                                />
                                            ))}
                                    </div>
                                }
                                endMessage={null}
                                scrollableTarget="scrollableDiv"
                                scrollThreshold={0.9}
                                className="!overflow-x-hidden"
                            >
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-5">
                                    {displayedPlaylists.map((playlist: any) => (
                                        <PlaylistCard
                                            key={playlist.id}
                                            playlistData={playlist}
                                            setDisplayedPlaylists={
                                                setDisplayedPlaylists
                                            }
                                            setAllPlaylists={setAllPlaylists}
                                        />
                                    ))}
                                </div>
                            </InfiniteScroll>
                        ) : (
                            <div className="text-center py-10">
                                <p className="text-lg text-gray-500">
                                    {isSearching
                                        ? `No results found for "${searchQuery}"`
                                        : "No results match the applied filters"}
                                </p>
                                <p className="text-sm text-gray-400 mt-2">
                                    {isSearching &&
                                        "Try different keywords or check your spelling"}
                                </p>
                            </div>
                        )}
                    </div>
                )}
            </div>

            <CreatePlaylistModal
                isModalOpen={isCreatePlaylistModalOpen}
                setIsModalOpen={setIsCreatePlaylistModalOpen}
                setNewAddedPlaylist={setNewAddedPlaylist}
            />
        </div>
    );
};

export default Playlists;
