"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { getAuth } from "firebase/auth";
import {
    getTranscriptionDetail,
    TranscriptionDetailItem,
} from "@/src/api/knowledge-base.api";
import TranscriptionView from "@/components/transcription/transcriptionView";
import { Poppins } from "next/font/google";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const TranscriptionPage = () => {
    const params = useParams();
    const searchParams = useSearchParams();
    const [transcription, setTranscription] =
        useState<TranscriptionDetailItem | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const id: any = params.id;
    const searchQuery = searchParams.get("search");

    useEffect(() => {
        const fetchTranscriptionDetail = async () => {
            if (!id) return;

            try {
                setIsLoading(true);
                setError(null);

                // Get current user's ID token if available
                const auth = getAuth();
                const user = auth.currentUser;
                let idToken = null;

                if (user) {
                    idToken = await user.getIdToken();
                }

                // Fetch transcription details
                const response = await getTranscriptionDetail(
                    {
                        id,
                        query: searchQuery || undefined,
                    },
                    idToken
                );

                if (response && response.data) {
                    setTranscription(response.data);
                } else {
                    setError("Failed to load transcription data");
                }
            } catch (err) {
                console.error("Error fetching transcription:", err);
                setError("An error occurred while loading the transcription");
            } finally {
                setIsLoading(false);
            }
        };

        fetchTranscriptionDetail();
    }, [id, searchQuery]);

    if (isLoading) {
        return (
            <div
                className={`w-full h-[calc(100vh-60px)] flex flex-col items-center justify-center ${poppins.className}`}
            >
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                <p className="mt-6 text-lg font-medium text-gray-700">
                    Loading transcription...
                </p>
                <p className="mt-2 text-sm text-gray-500">
                    Please wait while we prepare your content
                </p>
            </div>
        );
    }

    if (error || !transcription) {
        return (
            <div
                className={`w-full h-full flex items-center justify-center ${poppins.className}`}
            >
                <div className="text-center p-8">
                    <div className="text-red-500 text-5xl mb-4">⚠️</div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-2">
                        {error || "Transcription not found"}
                    </h2>
                    <p className="text-gray-600">
                        The requested transcription could not be loaded.
                    </p>
                    <button
                        onClick={() => window.history.back()}
                        className="mt-4 px-4 py-2 bg-[#3fa1d1] text-white rounded hover:bg-[#2980b9] transition-colors"
                    >
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    return (
        <TranscriptionView
            transcription={transcription}
            searchQuery={searchQuery || undefined}
        />
    );
};

export default TranscriptionPage;
