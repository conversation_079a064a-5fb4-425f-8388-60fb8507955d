import { useEffect } from "react";
import { onMessage } from "firebase/messaging";
import { messaging } from "@/src/config/firebase.config";
import { notification } from "antd";

const NotificationListener = () => {
  const [api, contextHolder] = notification.useNotification();

  // Define openNotification outside of the onMessage callback
  const openNotification = (title: string, body: string) => {
    api.open({
      message: title,
      description: body,
      duration: 5,
      placement: "bottomRight",
      style: {
        borderRadius: "12px",
      },
      pauseOnHover: true,
    });
  };

  useEffect(() => {
    // Check if messaging is available before setting up the listener
    if (!messaging) {
      console.warn("Firebase messaging is not available in this browser");
      return;
    }

    try {
      const unsubscribe = onMessage(messaging, (payload) => {
        console.log("Foreground message received:", payload);
        const { title, body } = payload.notification || {};

        // Directly call openNotification
        if (title && body) {
          openNotification(title, body);
        }
      });

      return () => {
        try {
          unsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from messages:", error);
        }
      };
    } catch (error) {
      console.error("Error setting up message listener:", error);
      // Return empty cleanup function
      return () => {};
    }
  }, [api]); // Add api to dependencies

  return contextHolder; // Important: Return contextHolder to make notifications work
};

export default NotificationListener;
