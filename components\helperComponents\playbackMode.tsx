import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { playbackModes } from "@/src/libs/constant";
import { Poppins } from "next/font/google";
import Image from "next/image";
import { useFilterContext } from "@/src/context/filter.context";
import { MdVideoLibrary } from "react-icons/md";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const PlaybackMode = () => {
  const [open, setOpen] = useState(false);
  const { playbackMode, setPlaybackMode } = useFilterContext();

  const handleChange = (value: number) => {
    setPlaybackMode(value);
    setOpen(false);
  };

  // Get the label of the currently selected mode
  const getSelectedModeLabel = () => {
    const selectedMode = playbackModes.find((item) => item.value === playbackMode);
    return selectedMode ? selectedMode.label : "Audio only (all lectures)";
  };

  const content = (
    <div className={`w-[188px] p-1 ${poppins.className}`}>
      <div className="w-full flex flex-col gap-[2px]">
        {playbackModes.map((item: any) => (
          <div
            key={item.value}
            className="w-full flex items-center cursor-pointer"
            onClick={() => handleChange(item.value)}
          >
            <h2
              className={`w-full text-[13px] px-3 py-2 leading-5 font-[400] rounded-md text-text-primary ${
                playbackMode === item.value
                  ? "bg-primary-light !text-text"
                  : "hover:bg-gray-100"
              }`}
            >
              {item.label}
            </h2>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="flex flex-col gap-1">
      <h2 className="text-[11px] leading-4 pl-[2px] font-[500] text-text-primary md:block hidden">
        Playback Mode
      </h2>
      <Popover
        placement="bottomRight"
        title={
          <h2 className="hidden text-[16px] leading-4 font-[500] text-text-primary max-[768px]:block px-3 pt-3">
            Playback Mode
          </h2>
        }
        content={content}
        arrow={false}
        trigger={["click"]}
        open={open}
        onOpenChange={setOpen}
      >
        <Button
          className={`h-[32px] md:w-[188px] flex gap-2 items-center justify-between pt-[2px] px-1 md:px-3 text-[13px] text-left shadow-none max-[768px]:border-none md:!border border-[#E0E0E0] rounded-[12px] hover:!border-primary cursor-pointer transition-all ${
            open && "!border-primary"
          }`}
        >
          <h2 className="text-[13px] leading-5 font-[400] text-text-primary hidden md:block">
            {getSelectedModeLabel()}
          </h2>
          <Image
            src="/images/helperComponents/arrow.svg"
            width={16}
            height={16}
            alt=""
            className={`cursor-pointer transform transition-transform duration-300 ${
              open ? "rotate-180" : "rotate-0"
            } hidden md:block`}
          />
          <div className={`max-[768px]:block hidden !text-text-primary`}>
            <MdVideoLibrary className="text-[18px]"  />
          </div>
        </Button>
      </Popover>
    </div>
  );
};

export default PlaybackMode;
