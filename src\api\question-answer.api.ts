// import { collection, doc, getDoc, getDocs, orderBy, query, setDoc } from "firebase/firestore";
// import { db } from "../config/firebase.config";

// /**
//  * Fetch all questions from Firestore
//  */
// export const fetchQuestions = async (): Promise<any[]> => {
//   try {
//     // const querySnapshot = await getDocs(collection(db, "question"));
//     // const questions = querySnapshot.docs.map((doc) => ({
//     //   id: doc.id,
//     //   ...doc.data(),
//     // }));
//     const questionRef = collection(db, "question");

//     const q = query(questionRef, orderBy("id"));

//     const querySnapshot = await getDocs(q);
//     const questions = querySnapshot.docs.map((doc) => ({
//       id: doc.id,
//       ...doc.data(),
//     }));
//     console.log("Questions data:", questions);
//     return questions;
//   } catch (error) {
//     console.error("Error fetching questions:", error);
//     throw error;
//   }
// };

// /**
//  * Fetch user's answers
//  */
// export const fetchUserAnswers = async (): Promise<any> => {
//   const userId = localStorage.getItem("firebaseUid");
//   if (!userId) throw new Error("User ID not found in local storage");
//   try {
//     const docRef = doc(db, `users/${userId}/questionAndAnswers/answers`);
//     const snapshot = await getDoc(docRef);
//     const data = snapshot.data();
//     console.log("User answers data:", data);
//     return data || {};
//   } catch (error) {
//     console.error("Error fetching user answers:", error);
//     throw error;
//   }
// };

// /**
//  * Update user's answer for a question
//  */
// export const updateUserAnswer = async (questionId: string, answer: any): Promise<void> => {
//   const userId = localStorage.getItem("firebaseUid");
//   if (!userId) throw new Error("User ID not found in local storage");
//   try {
//     const docRef = doc(db, `users/${userId}/questionAndAnswers/answers`);
//     await setDoc(docRef, { [questionId]: answer }, { merge: true });
//     console.log(`Updated answer for question ${questionId}:`, answer);
//   } catch (error) {
//     console.error("Error updating user answer:", error);
//     throw error;
//   }
// }

import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  setDoc,
  updateDoc,
  increment,
} from "firebase/firestore";
import { db } from "../config/firebase.config";

export const fetchNextQuestion = async (): Promise<any | null> => {
  const userId = localStorage.getItem("firebaseUid");
  if (!userId) throw new Error("User ID not found in local storage");

  try {
    //Get user's answered question IDs
    const docRef = doc(db, `users/${userId}/questionAndAnswers/answers`);
    const snapshot = await getDoc(docRef);
    const answeredData = snapshot.data() || {};
    const answeredIds = Object.keys(answeredData).map(Number);

    const lastAnsweredId = answeredIds.length ? Math.max(...answeredIds) : 0;

    //Fetch next question
    const questionRef = collection(db, "questions");
    const q = query(
      questionRef,
      where("id", ">", lastAnsweredId),
      orderBy("id"),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    const docSnap = querySnapshot.docs[0];
    if (!docSnap) {
      console.log("No more questions to show.");
      return null;
    }

    const nextQuestion = { docId: docSnap.id, ...docSnap.data() };
    console.log("Next question fetched:", nextQuestion);

    return nextQuestion;
  } catch (error) {
    console.error("Error fetching next question:", error);
    throw error;
  }
};

// export const updateUserAnswer = async (
//   questionId: string,
//   answer: any
// ): Promise<void> => {
//   const userId = localStorage.getItem("firebaseUid");
//   if (!userId) throw new Error("User ID not found in local storage");
//   try {
//     const docRef = doc(db, `users/${userId}/questionAndAnswers/answers`);
//     await setDoc(docRef, { [questionId]: answer }, { merge: true });
//     console.log(`Updated answer for question ${questionId}:`, answer);
//   } catch (error) {
//     console.error("Error updating user answer:", error);
//     throw error;
//   }
// };

export const updateUserAnswer = async (
  questionDocId: string, // Firestore docId (e.g. Ihoxb7iDSnmPOVrw...),
  questionId: number, // question.id (e.g. 3)
  answerId: any // single or multiple answers
): Promise<void> => {
  const userId = localStorage.getItem("firebaseUid");
  if (!userId) throw new Error("User ID not found in local storage");

  try {
    const now = Date.now();

    const shouldIncrementAttempt =
      answerId !== null && (!Array.isArray(answerId) || answerId.length > 0);

    if (shouldIncrementAttempt) {
      const questionRef = doc(db, "questions", questionDocId);
      await updateDoc(questionRef, {
        attempt: increment(1),
      });
    }

    // //Increment attempt count on the question doc
    // const questionRef = doc(db, "testQuestion", questionDocId);
    // await updateDoc(questionRef, {
    //   attempt: increment(1),
    // });

    // Save answer under user's answer subcollection
    const userAnswerRef = doc(db, `users/${userId}/questionAndAnswers/answers`);
    const userAnswerPayload = {
      [questionId]: {
        answers: Array.isArray(answerId) ? answerId : [answerId],
        createdAt: now,
      },
    };

    await setDoc(userAnswerRef, userAnswerPayload, { merge: true });

    console.log(`Updated answer for question ${questionId}`, userAnswerPayload);
  } catch (error) {
    console.error("Error updating user answer:", error);
    throw error;
  }
};
