"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Alert } from 'antd';
import { getFCMToken } from '@/src/config/firebase.config';

const { Title, Text, Paragraph } = Typography;

const NotificationDebug: React.FC = () => {
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [fcmToken, setFcmToken] = useState<string>('');
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [logs, setLogs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[NotificationDebug] ${message}`);
  };

  useEffect(() => {
    // Check for iOS Safari
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    
    if (isIOS && isSafari) {
      addLog('⚠️ iOS Safari detected - Push notifications have limited support');
    }
    
    // Check initial permission status
    if ('Notification' in window) {
      setPermission(Notification.permission);
      addLog(`Initial notification permission: ${Notification.permission}`);
    } else {
      addLog('❌ Notifications not supported in this browser');
    }

    // Check if service worker is supported
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration('/firebase-messaging-sw.js')
        .then(registration => {
          if (registration) {
            setSwRegistration(registration);
            addLog('✅ Service Worker found: ' + registration.scope);
          } else {
            addLog('No Service Worker registration found');
          }
        })
        .catch(error => {
          addLog('❌ Error checking service worker: ' + error.message);
        });
    } else {
      addLog('❌ Service Workers not supported in this browser');
    }

    // Check for existing FCM token
    const existingToken = localStorage.getItem('fcmToken');
    if (existingToken) {
      setFcmToken(existingToken);
      if (existingToken === 'ios-safari-not-supported') {
        addLog('📱 iOS Safari placeholder token found in localStorage');
      } else {
        addLog('✅ Existing FCM token found in localStorage');
      }
    }
  }, []);

  const requestPermission = async () => {
    try {
      setLoading(true);
      addLog('Requesting notification permission...');
      
      const result = await Notification.requestPermission();
      setPermission(result);
      addLog(`Permission result: ${result}`);
      
      if (result === 'granted') {
        addLog('✅ Notification permission granted');
      } else {
        addLog('❌ Notification permission denied or dismissed');
      }
    } catch (error) {
      addLog(`Error requesting permission: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const registerServiceWorker = async () => {
    try {
      setLoading(true);
      addLog('Registering service worker...');
      
      const registration = await navigator.serviceWorker.register(
        '/firebase-messaging-sw.js',
        { scope: '/' }
      );
      
      setSwRegistration(registration);
      addLog(`✅ Service Worker registered: ${registration.scope}`);
      
      await navigator.serviceWorker.ready;
      addLog('✅ Service Worker is ready');
      
    } catch (error) {
      addLog(`❌ Error registering service worker: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getFCMTokenDebug = async () => {
    try {
      setLoading(true);
      addLog('Getting FCM token...');
      
      if (!swRegistration) {
        addLog('❌ No service worker registration available');
        return;
      }
      
      const token = await getFCMToken(swRegistration);
      setFcmToken(token);
      localStorage.setItem('fcmToken', token);
      
      if (token === 'ios-safari-not-supported') {
        addLog('📱 iOS Safari placeholder token generated');
      } else {
        addLog('✅ FCM Token retrieved successfully');
        addLog(`Token: ${token.substring(0, 50)}...`);
      }
      
    } catch (error) {
      addLog(`❌ Error getting FCM token: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testLocalNotification = () => {
    if (permission === 'granted') {
      addLog('Testing local notification...');
      new Notification('Test Notification', {
        body: 'This is a test notification from the debug panel',
        icon: '/favicon/bvks/favicon-32x32.png',
        tag: 'test-notification'
      });
      addLog('✅ Local notification sent');
    } else {
      addLog('❌ Cannot send local notification - permission not granted');
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const copyToken = () => {
    if (fcmToken) {
      navigator.clipboard.writeText(fcmToken);
      addLog('FCM Token copied to clipboard');
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>🔔 Notification Debug Panel</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* Status Cards */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
          <Card size="small">
            <Text strong>Notification Permission</Text>
            <br />
            <Text type={permission === 'granted' ? 'success' : permission === 'denied' ? 'danger' : 'warning'}>
              {permission.toUpperCase()}
            </Text>
          </Card>
          
          <Card size="small">
            <Text strong>Service Worker</Text>
            <br />
            <Text type={swRegistration ? 'success' : 'danger'}>
              {swRegistration ? 'REGISTERED' : 'NOT REGISTERED'}
            </Text>
          </Card>
          
          <Card size="small">
            <Text strong>FCM Token</Text>
            <br />
            <Text type={fcmToken ? 'success' : 'danger'}>
              {fcmToken ? 'AVAILABLE' : 'NOT AVAILABLE'}
            </Text>
          </Card>
        </div>

        {/* Action Buttons */}
        <Card title="Actions">
          <Space wrap>
            <Button 
              onClick={requestPermission} 
              loading={loading}
              disabled={permission === 'granted'}
            >
              Request Permission
            </Button>
            
            <Button 
              onClick={registerServiceWorker} 
              loading={loading}
              disabled={!!swRegistration}
            >
              Register Service Worker
            </Button>
            
            <Button 
              onClick={getFCMTokenDebug} 
              loading={loading}
              disabled={!swRegistration || permission !== 'granted'}
            >
              Get FCM Token
            </Button>
            
            <Button 
              onClick={testLocalNotification}
              disabled={permission !== 'granted'}
            >
              Test Local Notification
            </Button>
            
            <Button onClick={copyToken} disabled={!fcmToken}>
              Copy FCM Token
            </Button>
          </Space>
        </Card>

        {/* FCM Token Display */}
        {fcmToken && (
          <Card title="FCM Token" size="small">
            <Paragraph copyable={{ text: fcmToken }}>
              <Text code style={{ fontSize: '12px', wordBreak: 'break-all' }}>
                {fcmToken}
              </Text>
            </Paragraph>
          </Card>
        )}

        {/* Debug Logs */}
        <Card 
          title="Debug Logs" 
          extra={<Button size="small" onClick={clearLogs}>Clear</Button>}
        >
          <div style={{ 
            maxHeight: '300px', 
            overflowY: 'auto', 
            backgroundColor: '#f5f5f5', 
            padding: '12px',
            borderRadius: '4px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {logs.length === 0 ? (
              <Text type="secondary">No logs yet...</Text>
            ) : (
              logs.map((log, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  {log}
                </div>
              ))
            )}
          </div>
        </Card>

        {/* Instructions */}
        <Alert
          message="iOS Safari Limitations"
          description={
            <div>
              <p><strong>Note:</strong> iOS Safari has limited support for push notifications:</p>
              <ul>
                <li>Background notifications may not work reliably</li>
                <li>Service workers have restricted functionality</li>
                <li>Users must add the site to their home screen for better notification support</li>
                <li>This debug panel will show a placeholder token for iOS Safari</li>
              </ul>
            </div>
          }
          type="warning"
          showIcon
        />
      </Space>
    </div>
  );
};

export default NotificationDebug;
