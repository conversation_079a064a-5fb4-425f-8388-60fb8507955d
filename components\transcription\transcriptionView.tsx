"use client";
import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { Poppins } from "next/font/google";
import { TranscriptionDetailItem } from "@/src/api/knowledge-base.api";
import TranscriptionContent from "./transcriptionContent";
import { useAudioContext } from "@/src/context/audio.context";
import { formatSecondsToHHMMSS } from "@/src/utils/timeFormat";
import { IoArrowBack } from "react-icons/io5";
import { FiEdit } from "react-icons/fi";
import { BsArrowRepeat } from "react-icons/bs";
import { CgScrollV } from "react-icons/cg";
import { BiTime } from "react-icons/bi";
import {
    MdPlaylistAdd,
    MdFavoriteBorder,
    MdFavorite,
    MdOutlineRestartAlt,
} from "react-icons/md";
import { FaRegCircleCheck } from "react-icons/fa6";
import { FiShare2 } from "react-icons/fi";
import AddToPlaylistModal from "../Modal/addToPlaylistModal";
import ShareModal from "../Modal/shareModal";
import { FiBookmark } from "react-icons/fi";
import { GrLocation } from "react-icons/gr";
import { VscCircleFilled } from "react-icons/vsc";
import { MdOutlineDateRange } from "react-icons/md";
import { MdStarPurple500 } from "react-icons/md";
import { FiFlag } from "react-icons/fi";
import {
    addToFavourite,
    removeFromFavourite,
} from "@/src/services/favouriteLecture.service";
import {
    markAsCompleted,
    removeFromCompleted,
} from "@/src/services/lectureCompletion.service";
import { getLectureById } from "@/src/services/indexedDB.service";
import { message } from "antd";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

interface TranscriptionViewProps {
    transcription: TranscriptionDetailItem;
    searchQuery?: string;
}

const TranscriptionView = ({
    transcription,
    searchQuery,
}: TranscriptionViewProps) => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const {
        playAudio,
        isFavorite: audioIsFavorite,
        isCompleted: audioIsCompleted,
        isProcessing: audioIsProcessing,
        toggleFavorite: audioToggleFavorite,
        toggleCompleted: audioToggleCompleted,
        currentAudio,
    } = useAudioContext();
    const [showTimestamps, setShowTimestamps] = useState(false);
    const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] =
        useState(false);
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(false);
    const [isFavorite, setIsFavorite] = useState(false);
    const [isCompleted, setIsCompleted] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);

    // Get search query from URL if not provided as prop
    const query = searchQuery || searchParams.get("search") || "";

    // Check if transcription is already in favorites and if it's completed
    useEffect(() => {
        const checkStatus = async () => {
            try {
                if (transcription?.id) {
                    const lecture = await getLectureById(transcription.id);

                    console.log("lecture", lecture);
                    if (lecture) {
                        // Check favorite status
                        if (lecture.isFavourite || lecture.favourite) {
                            setIsFavorite(true);
                        }

                        // Check completion status
                        if (lecture.isCompleted) {
                            setIsCompleted(true);
                        }
                    }
                }
            } catch (error) {
                console.error("Error checking lecture status:", error);
            }
        };

        checkStatus();
    }, [transcription?.id]);

    // Sync with audio context state when it changes
    useEffect(() => {
        // Only sync if the current audio is for this transcription
        if (currentAudio.id === transcription.id) {
            setIsFavorite(audioIsFavorite);
            setIsCompleted(audioIsCompleted);
            setIsProcessing(audioIsProcessing);
        }
    }, [
        audioIsFavorite,
        audioIsCompleted,
        audioIsProcessing,
        currentAudio.id,
        transcription.id,
    ]);

    // Handle back button click
    const handleBackClick = () => {
        query
            ? router.push(
                  `/knowledge-base${
                      query
                          ? `?search=${encodeURIComponent(
                                query
                            )}&activeTab=transcriptions`
                          : "?activeTab=transcriptions"
                  }`
              )
            : router.back();
    };

    // Handle play audio
    const handlePlayAudio = () => {
        if (
            transcription.resources_audios &&
            transcription.resources_audios.length > 0
        ) {
            const audioUrl = transcription.resources_audios[0].url;
            playAudio({
                title: transcription.title,
                subtitle: transcription.category,
                audioSrc: audioUrl,
                thumbnailUrl: transcription.thumbnail,
                id: transcription.id,
            });
        }
    };

    // Auto play audio when component mounts
    useEffect(() => {
        handlePlayAudio();
    }, []);

    // Action button handlers
    const handleSuggestEdit = () => {
        setIsEditMode((prevMode) => !prevMode);
    };

    const handleAutoScroll = () => {
        setIsAutoScrollEnabled((prevState) => !prevState);
    };

    const handleShowTimestamp = () => {
        setShowTimestamps((prev) => !prev);
    };

    const handleAddToPlaylist = () => {
        setIsAddToPlaylistModalOpen(true);
    };

    const handleAddToFavorites = async () => {
        // If the current audio is for this transcription, use the audio context method
        if (currentAudio.id === transcription.id) {
            await audioToggleFavorite();
            return;
        }

        // Otherwise use the local method
        try {
            setIsProcessing(true);

            if (isFavorite) {
                // Remove from favorites
                const successfulIds = await removeFromFavourite(
                    transcription.id
                );
                if (successfulIds.includes(transcription.id)) {
                    setIsFavorite(false);
                    // message.success({
                    //     content: "Lecture successfully removed from favorites!",
                    //     className: `${poppins.className}`,
                    // });
                }
            } else {
                // Add to favorites
                const successfulIds = await addToFavourite(transcription.id);
                if (successfulIds.includes(transcription.id)) {
                    setIsFavorite(true);
                    // message.success({
                    //     content: "Lecture successfully Added to favorites!",
                    //     className: `${poppins.className}`,
                    // });
                }
            }
        } catch (error) {
            console.error("Error updating favorite status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const handleResetCompletion = async () => {
        // If the current audio is for this transcription, use the audio context method
        if (currentAudio.id === transcription.id) {
            await audioToggleCompleted();
            return;
        }

        // Otherwise use the local method
        try {
            setIsProcessing(true);

            if (isCompleted) {
                // Remove from completed
                const successfulIds = await removeFromCompleted(
                    transcription.id
                );
                if (successfulIds.includes(transcription.id)) {
                    setIsCompleted(false);
                    // message.success({
                    //     content: "Completion Reset Successfully!",
                    //     className: `${poppins.className}`,
                    // });
                }
            } else {
                // Mark as completed
                const successfulIds = await markAsCompleted(transcription.id);
                if (successfulIds.includes(transcription.id)) {
                    setIsCompleted(true);
                    // message.success({
                    //     content: "Marked as completed!",
                    //     className: `${poppins.className}`,
                    // });
                }
            }
        } catch (error) {
            console.error("Error updating completion status:", error);
        } finally {
            setIsProcessing(false);
        }
    };

    const handleShare = () => {
        setIsShareModalOpen(true);
    };

    return (
        <div className={`w-full h-full ${poppins.className}`}>
            {/* Transcription content */}
            <div className="pt-4">
                {/* Title with back arrow */}
                <div className="flex items-center mb-4 px-4 sm:px-8">
                    <button
                        type="button"
                        onClick={handleBackClick}
                        className="mr-3 text-primary text-opacity-90 hover:text-opacity-100 transition-colors"
                        aria-label="Go back"
                    >
                        <IoArrowBack size={24} />
                    </button>
                    <h1 className="text-2xl sm:text-3xl font-bold text-primary">
                        {transcription.title}
                    </h1>
                </div>

                {/* Metadata pills */}
                <div className="flex gap-4 mb-4 mx-4 sm:mx-8 overflow-x-scroll scrollbar-hide">
                    {transcription.dateOfRecording && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <MdOutlineDateRange className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>{transcription.dateOfRecording}</span>
                        </div>
                    )}
                    {transcription.category && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <FiBookmark className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>{transcription.category}</span>
                        </div>
                    )}
                    {transcription.language_main && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <VscCircleFilled className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>
                                {transcription.language_main} (
                                {transcription.language_main})
                            </span>
                        </div>
                    )}
                    {transcription.length > 0 && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <BiTime className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>
                                {formatSecondsToHHMMSS(transcription.length)}
                            </span>
                        </div>
                    )}
                    {transcription.place && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <GrLocation className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>{transcription.place}</span>
                        </div>
                    )}
                    {transcription.category && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <MdStarPurple500 className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>
                                Bhagavad-gītā{" "}
                                {transcription.category.includes("4.34")
                                    ? "4.34"
                                    : ""}
                            </span>
                        </div>
                    )}
                    {transcription.tags && (
                        <div className="px-5 py-2 rounded-[10px] text-sm border border-primary flex items-center flex-shrink-0">
                            <span className="inline-block mr-1">
                                <FiFlag className="text-[12px] sm:text-[14px]" />
                            </span>
                            <span>{transcription.tags}</span>
                        </div>
                    )}
                </div>

                {/* Action buttons */}
                <div
                    className={`flex gap-5 text-sm text-gray-700 mx-4 sm:mx-8 overflow-x-scroll scrollbar-hide ${
                        isEditMode || isAutoScrollEnabled ? "mb-4" : "mb-8"
                    }`}
                >
                    <button
                        type="button"
                        onClick={handleSuggestEdit}
                        className={`flex items-center transition-colors py-1 flex-shrink-0 ${
                            isEditMode
                                ? "bg-green-500 text-white rounded-[10px] px-3"
                                : "hover:text-primary rounded-[10px]"
                        }`}
                    >
                        <FiEdit className="mr-1" />
                        <span>Suggest Edit</span>
                    </button>

                    <button
                        type="button"
                        onClick={handleAutoScroll}
                        className={`flex items-center transition-colors py-1 flex-shrink-0 ${
                            isAutoScrollEnabled
                                ? "bg-green-500 text-white rounded-[10px] px-3"
                                : "hover:text-primary rounded-[10px]"
                        }`}
                    >
                        <CgScrollV className="mr-1" />
                        <span>Auto Scroll</span>
                    </button>

                    <button
                        type="button"
                        onClick={handleShowTimestamp}
                        className="flex items-center hover:text-primary transition-colors flex-shrink-0"
                    >
                        <BiTime className="mr-1" />
                        <span>
                            {showTimestamps
                                ? "Hide Timestamp"
                                : "Show Timestamp"}
                        </span>
                    </button>

                    <button
                        type="button"
                        onClick={handleAddToPlaylist}
                        className="flex items-center hover:text-primary transition-colors flex-shrink-0"
                    >
                        <MdPlaylistAdd className="mr-1" />
                        <span>Add to Playlist</span>
                    </button>

                    <button
                        type="button"
                        onClick={handleAddToFavorites}
                        className={`flex items-center transition-colors flex-shrink-0 hover:text-primary`}
                        disabled={isProcessing}
                    >
                        {isFavorite ? (
                            <MdFavorite className="mr-1" />
                        ) : (
                            <MdFavoriteBorder className="mr-1" />
                        )}
                        <span>
                            {isFavorite
                                ? "Remove from Favorites"
                                : "Add to Favorites"}
                        </span>
                    </button>

                    <button
                        type="button"
                        onClick={handleResetCompletion}
                        className={`flex items-center transition-colors flex-shrink-0 hover:text-primary`}
                        disabled={isProcessing}
                    >
                        {isCompleted ? (
                            <MdOutlineRestartAlt className="mr-1" />
                        ) : (
                            <FaRegCircleCheck className="mr-1" />
                        )}
                        <span>
                            {isCompleted
                                ? "Reset completion"
                                : "Mark as completed"}
                        </span>
                    </button>

                    {/* <button
                        type="button"
                        onClick={handleShare}
                        className="flex items-center hover:text-primary transition-colors flex-shrink-0"
                    >
                        <FiShare2 className="mr-1" />
                        <span>Share</span>
                    </button> */}
                </div>

                <div
                    className={`
                        ${
                            isEditMode || isAutoScrollEnabled
                                ? "h-[calc(100vh-226px)]"
                                : "h-[calc(100vh-240px)]"
                        } overflow-y-auto scrollbar px-4 sm:px-8`}
                >
                    <TranscriptionContent
                        transcription={transcription}
                        onPlayAudio={handlePlayAudio}
                        showTimestamps={showTimestamps}
                        isEditMode={isEditMode}
                        isAutoScrollEnabled={isAutoScrollEnabled}
                        autoStartFromHighlight={!!searchQuery}
                    />
                </div>
            </div>

            {/* Add To Playlist Modal */}
            <AddToPlaylistModal
                isModalOpen={isAddToPlaylistModalOpen}
                setIsModalOpen={setIsAddToPlaylistModalOpen}
                selectedFiles={transcription}
            />

            {/* Share Modal */}
            <ShareModal
                isModalOpen={isShareModalOpen}
                setIsModalOpen={setIsShareModalOpen}
                title={transcription.title}
                url={window.location.href}
            />
        </div>
    );
};

export default TranscriptionView;
