import { Switch, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { MdHeadset } from "react-icons/md";
import { MdHeadsetOff } from "react-icons/md";

const AudioToggleSwitch = () => {
    const [isAudioOn, setIsAudioOn] = useState(false);

    useEffect(() => {
        const audioOn = localStorage.getItem("audioOn");
        if (audioOn) {
            setIsAudioOn(JSON.parse(audioOn));
        }
    }, []);

    const handleToggleAudio = () => {
        localStorage.setItem("audioOn", JSON.stringify(!isAudioOn));
        setIsAudioOn(!isAudioOn);
    };

    return (
        <div className="h-[30px] flex gap-1 items-center">
            {/* <h2 className="text-[11px] leading-4 pl-[2px] font-[500] text-text-primary md:block hidden">
                Audio Only
            </h2> */}
            <Tooltip
                title={isAudioOn ? "Audio Only (On)" : "Audio Only (Off)"}
                placement="bottom"
            >
                <Switch
                    value={isAudioOn}
                    onChange={handleToggleAudio}
                    style={{
                        backgroundColor: isAudioOn ? "#f97316" : undefined,
                    }}
                    checkedChildren={
                        <MdHeadset className="text-[16px] relative top-[3px]" />
                    }
                    unCheckedChildren={
                        <MdHeadsetOff className="text-[16px] relative top-[3px]" />
                    }
                />
            </Tooltip>
        </div>
    );
};

export default AudioToggleSwitch;
