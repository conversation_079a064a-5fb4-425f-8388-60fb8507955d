import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "../../config/firebase.config";
import { getLectureById, Lecture } from "../indexedDB.service";

// Interface for created day map
interface CreatedDay {
    day: number;
    month: number;
    year: number;
}

// Interface for TopLectures document
interface TopLecturesDocument {
    audioPlayedTime: number;
    createdDay: CreatedDay;
    creationTimestamp: number;
    documentId: string;
    documentPath: string;
    lastModifiedTimestamp: number;
    playedBy: string[];
    playedIds: number[];
    videoPlayedTime: number;
}

/**
 * Format current date to DD-MM-YYYY string
 * @param date - Date object (defaults to current date)
 * @returns Formatted date string in DD-MM-YYYY format
 */
// const formatDateToDMYYYY = (date: Date = new Date()): string => {
//     const day = String(date.getDate()).padStart(2, "0");
//     const month = String(date.getMonth() + 1).padStart(2, "0");
//     const year = date.getFullYear();
//     return `${day}-${month}-${year}`;
// };

const formatDateToDMYYYY = (date: Date = new Date()): string => {
    const day = date.getDate();        // no padStart
    const month = date.getMonth() + 1; // no padStart
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
};

/**
 * Get date components from Date object
 * @param date - Date object (defaults to current date)
 * @returns Object with day, month, year
 */
const getDateComponents = (date: Date = new Date()): CreatedDay => {
    return {
        day: date.getDate(),
        month: date.getMonth() + 1,
        year: date.getFullYear(),
    };
};

/**
 * Update TopLectures collection when video starts playing
 * This function should be called only once when video starts playing
 *
 * @param lectureInput - Either a lecture ID or a lecture object
 * @param userId - The current user ID
 * @returns Promise<void>
 */
const updateTopLecturesOnStart = async (
    lectureInput: number | string | Lecture,
    userId: string
): Promise<void> => {
    try {
        // Step 1: Get the lecture object and ID
        let lecture: Lecture | undefined;
        let lectureId: number | string;

        if (typeof lectureInput === "object") {
            lecture = lectureInput;
            lectureId = lecture.id;
        } else {
            lectureId = lectureInput;
            lecture = await getLectureById(lectureId);
            if (!lecture) {
                console.warn(
                    `Lecture with ID ${lectureId} not found in IndexedDB`
                );
                return;
            }
        }

        // Step 2: Get current date info
        const currentDate = new Date();
        const currentTimestamp = Date.now();
        const documentId = formatDateToDMYYYY(currentDate);
        const documentPath = `TopLectures/${documentId}`;
        const createdDay = getDateComponents(currentDate);

        // Step 3: Check if document exists for today
        const docRef = doc(db, documentPath);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            // Document exists - update it
            const existingData = docSnap.data() as TopLecturesDocument;

            // Check if user is already in playedBy array
            const isUserAlreadyAdded = existingData.playedBy?.includes(userId);

            // Prepare update data
            const updateData = {
                lastModifiedTimestamp: currentTimestamp,
                // Add user to playedBy only if not already present
                playedBy: isUserAlreadyAdded
                    ? existingData.playedBy
                    : [...(existingData.playedBy || []), userId],
                // Always add lecture ID to playedIds (no restriction for duplicates)
                playedIds: [
                    ...(existingData.playedIds || []),
                    Number(lectureId),
                ],
            };

            await setDoc(docRef, updateData, { merge: true });
        } else {
            // Document doesn't exist - create new one
            const newDocumentData: TopLecturesDocument = {
                audioPlayedTime: 0,
                createdDay,
                creationTimestamp: currentTimestamp,
                documentId,
                documentPath,
                lastModifiedTimestamp: currentTimestamp,
                playedBy: [userId],
                playedIds: [Number(lectureId)],
                videoPlayedTime: 0,
            };

            await setDoc(docRef, newDocumentData);
        }
    } catch (error) {
        console.error(`Error updating TopLectures on start:`, error);
        throw error;
    }
};

/**
 * Update only the videoPlayedTime in TopLectures collection
 * This function should be called every 60 seconds during video playback
 *
 * @param userId - The current user ID
 * @returns Promise<void>
 */
const updateTopLecturesPlayTime = async (userId: string): Promise<void> => {
    try {
        // Step 1: Get current date info
        const currentDate = new Date();
        const currentTimestamp = Date.now();
        const documentId = formatDateToDMYYYY(currentDate);
        const documentPath = `TopLectures/${documentId}`;

        // Step 2: Check if document exists for today
        const docRef = doc(db, documentPath);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
            // Document exists - update only videoPlayedTime
            const existingData = docSnap.data() as TopLecturesDocument;

            const updateData = {
                lastModifiedTimestamp: currentTimestamp,
                videoPlayedTime: existingData.videoPlayedTime + 60,
            };

            await setDoc(docRef, updateData, { merge: true });
        } else {
            // Document doesn't exist - this shouldn't happen if updateTopLecturesOnStart was called first
            console.warn("TopLectures document doesn't exist for today. Cannot update video play time.");
        }
    } catch (error) {
        console.error(`Error updating TopLectures video play time:`, error);
        throw error;
    }
};

/**
 * Public function to update TopLectures collection when video starts playing
 * This should be called only once when video starts playing
 *
 * @param lectureInput - Either a lecture ID or a lecture object
 * @returns Promise<void>
 */
export const updateTopLecturesOnPlay = async (
    lectureInput: number | string | Lecture
): Promise<void> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            console.warn("User not logged in - skipping TopLectures update");
            return; // Don't throw error, just skip the update
        }

        await updateTopLecturesOnStart(lectureInput, userId);
    } catch (error) {
        console.error("Error updating TopLectures on play:", error);
        // Don't re-throw the error to avoid breaking video playback
    }
};

/**
 * Public function to update TopLectures videoPlayedTime every 60 seconds
 * This should be called every 60 seconds during video playback
 *
 * @returns Promise<void>
 */
export const updateTopLecturesTime = async (): Promise<void> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            console.warn("User not logged in - skipping TopLectures time update");
            return; // Don't throw error, just skip the update
        }

        await updateTopLecturesPlayTime(userId);
    } catch (error) {
        console.error("Error updating TopLectures video time:", error);
        // Don't re-throw the error to avoid breaking video playback
    }
};
