# IndexedDB Service Implementation

## Overview
This implementation provides a comprehensive local database management system using IndexedDB for storing and managing lecture data offline. It serves as the primary local storage solution for the BVKS web application, enabling offline functionality and improved performance.

## Features Implemented

### 1. Database Management
- **Database Initialization**: Automatic database creation and schema management
- **Version Control**: Database versioning for schema updates and migrations
- **Object Stores**: Structured storage for lectures and synchronization information

### 2. Lecture Data Management
- **Bulk Storage**: Efficient storage of large lecture datasets
- **Individual Operations**: CRUD operations for single lectures
- **Data Retrieval**: Fast lookup and filtering capabilities
- **Update Management**: Seamless lecture data updates and modifications

### 3. Synchronization Support
- **Sync Information**: Tracking of synchronization status and timestamps
- **Data Integrity**: Ensuring consistency between local and cloud data
- **Performance Optimization**: In-memory filtering for fast query responses

## Database Schema

### Database Configuration
- **Database Name**: `bvksMediaLibrary`
- **Current Version**: `2`
- **Object Stores**: `lectures`, `syncInfo`

### Lecture Interface
```typescript
interface Lecture {
  id: string;                      // Unique lecture identifier
  title: string[];                 // Lecture titles in multiple languages
  thumbnailUrl?: string;           // Thumbnail image URL
  thumbnail?: string;              // Thumbnail image data
  audioUrl: string;                // Audio file URL
  category: string[];              // Lecture categories
  language: {                      // Language information
    main: string;                  // Primary language
    [key: string]: string;         // Additional language mappings
  };
  dateOfRecording: any;            // Recording date information
  length: number;                  // Lecture duration in seconds
  creationTimestamp: number;       // Creation timestamp
  
  // User-specific fields from lectureInfo
  completed?: boolean;             // Legacy completion flag
  isCompleted?: boolean;           // Current completion status
  documentId?: string;             // Firestore document ID
  documentPath?: string;           // Firestore document path
  downloadPlace?: number;          // Download queue position
  downloaded?: boolean;            // Download status
  favourite?: boolean;             // Legacy favorite flag
  isFavourite?: boolean;          // Current favorite status
  favouritePlace?: number;         // Favorite list position
  inPrivateList?: boolean;         // Private playlist membership
  inPublicList?: boolean;          // Public playlist membership
  lastModifiedTimestamp?: number;  // Last modification time
  lastPlayedPoint?: number;        // Audio playback position
  totalPlayedNo?: number;          // Total play count
  totalPlayedTime?: number;        // Total listening time
  totallength?: number;            // Total lecture length
  [key: string]: any;              // Additional dynamic fields
}
```

### Sync Information Interface
```typescript
interface SyncInfo {
  id: string;                      // Sync record identifier
  lastSyncTimestamp: number;       // Last synchronization time
  totalLectures: number;           // Total lectures count
}
```

## Implementation Details

### Core Functions

#### 1. `initDB()`
- **Purpose**: Initialize IndexedDB database with proper schema
- **Features**: 
  - Automatic object store creation
  - Version management for schema updates
  - Error handling for database initialization

#### 2. `storeLectures(lectures)`
- **Purpose**: Store multiple lectures efficiently
- **Features**: 
  - Bulk insert operations using Promise.all
  - Transaction management for data integrity
  - Performance optimization for large datasets

#### 3. `getAllLectures()`
- **Purpose**: Retrieve all lectures from IndexedDB
- **Returns**: Array of all stored lectures
- **Usage**: Primary data retrieval method

#### 4. `getLectureById(id)`
- **Purpose**: Retrieve specific lecture by ID
- **Parameters**: Lecture ID (string or number)
- **Returns**: Lecture object or undefined

#### 5. `updateLecture(lecture)`
- **Purpose**: Update single lecture data
- **Features**: 
  - Upsert operation (insert or update)
  - Transaction safety
  - Logging for debugging

#### 6. `getLectureCount()`
- **Purpose**: Get total count of stored lectures
- **Returns**: Number of lectures in database
- **Usage**: Sync validation and statistics

### Database Operations

#### 7. `clearDatabase()`
- **Purpose**: Clear all lecture data
- **Usage**: Data reset and cleanup operations

#### 8. `deleteDatabase()`
- **Purpose**: Completely remove IndexedDB database
- **Features**: 
  - Native IndexedDB API usage
  - Promise-based operation
  - Complete cleanup

### Sync Management

#### 9. `updateSyncInfo(syncInfo)`
- **Purpose**: Update synchronization metadata
- **Usage**: Track sync status and timestamps

#### 10. `getSyncInfo()`
- **Purpose**: Retrieve synchronization information
- **Returns**: Sync metadata or undefined

## Integration Points

The IndexedDB service integrates with:

1. **Sync Service**
   - Data synchronization between Firebase and IndexedDB
   - Sync status tracking and management

2. **Media Library API**
   - Local data retrieval for lecture listings
   - Offline functionality support

3. **Favorites and Completion Services**
   - Local data updates for user interactions
   - Immediate response for UI updates

4. **Audio Player**
   - Lecture data retrieval for playback
   - Progress tracking and updates

## Usage Examples

### Database Initialization
```javascript
import { initDB } from './indexedDB.service';

const db = await initDB();
```

### Storing Lectures
```javascript
import { storeLectures } from './indexedDB.service';

const lectures = [lecture1, lecture2, lecture3];
await storeLectures(lectures);
```

### Retrieving Data
```javascript
import { getAllLectures, getLectureById } from './indexedDB.service';

// Get all lectures
const allLectures = await getAllLectures();

// Get specific lecture
const lecture = await getLectureById('12345');
```

### Updating Lecture
```javascript
import { updateLecture } from './indexedDB.service';

const updatedLecture = {
  ...existingLecture,
  isFavourite: true,
  lastModifiedTimestamp: Date.now()
};

await updateLecture(updatedLecture);
```

## Performance Optimizations

### 1. Bulk Operations
- **Promise.all**: Parallel processing for multiple operations
- **Transaction Batching**: Efficient database transactions
- **Memory Management**: Optimized for large datasets

### 2. Query Optimization
- **In-memory Filtering**: Client-side filtering instead of IndexedDB queries
- **Simple Schema**: Minimal indexes for better write performance
- **Key-based Lookup**: Fast retrieval using primary keys

## Error Handling
- **Database Initialization**: Graceful handling of database creation failures
- **Transaction Errors**: Proper error propagation and logging
- **Data Validation**: Type checking and data integrity validation
- **Network Independence**: Fully offline-capable operations

## Benefits
1. **Offline Functionality**: Complete offline access to lecture data
2. **Performance**: Fast local data access and retrieval
3. **Data Persistence**: Reliable local storage across sessions
4. **Sync Support**: Foundation for cloud synchronization
5. **Scalability**: Handles large datasets efficiently
6. **Browser Compatibility**: Works across modern browsers
7. **Transaction Safety**: ACID compliance for data integrity
