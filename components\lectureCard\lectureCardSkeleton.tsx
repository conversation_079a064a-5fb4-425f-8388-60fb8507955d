import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Poppins } from "next/font/google";

const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const LectureCardSkeleton = () => {
  return (
    <div className="cursor-default">
      <div className={`w-full transition-all duration-500 relative`}>
        {/* Thumbnail skeleton */}
        <Skeleton 
          height={150} 
          width="100%" 
          borderRadius={6}
        />
      </div>
      <div className="flex gap-2">
        <div className="w-[calc(100%-26px)] flex flex-col gap-1 py-2">
          {/* Title skeleton */}
          <Skeleton 
            width="90%" 
            height={20} 
            className={`${poppins.className}`}
          />
          {/* Date and category skeleton */}
          <Skeleton 
            width="70%" 
            height={16} 
            className={`${poppins.className}`}
          />
        </div>
      </div>
    </div>
  );
};

export default LectureCardSkeleton;
