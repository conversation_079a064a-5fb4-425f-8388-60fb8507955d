"use client";
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, message } from "antd";
import { <PERSON><PERSON>, <PERSON> } from "next/font/google";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { signOutUser } from "@/src/api/auth.api";
import { deleteDatabase } from "@/src/services/indexedDB.service";
import { useAudioContext } from "@/src/context/audio.context";
import { useFilterContext } from "@/src/context/filter.context";
import {
    fetchUserSettings,
    unsubscribeTopic,
    updateUserSettings,
} from "@/src/api/settings.api";
import { deleteToken } from "firebase/messaging";
import { messaging } from "@/src/config/firebase.config";
import { useSidebarContext } from "@/src/context/sidebar.context";

const roboto = Roboto({ weight: ["300", "400", "500"], subsets: ["latin"] });
const inter = Inter({ weight: ["300", "400", "500"], subsets: ["latin"] });

const LogoutModal = ({ isModalOpen, setIsModalOpen }: any) => {
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const { isPlaying, closePlayer } = useAudioContext();
    const { resetFilters } = useFilterContext();
    const { setIsTabChangeLoading } = useSidebarContext();

    const handleLogout = async () => {
        try {
            setLoading(true);
            setIsTabChangeLoading(true);

            if (isPlaying) {
                closePlayer();
            }

            const settings = await fetchUserSettings();

            if (settings?.notification) {
                const appName = process.env.APP;
                const fcmToken = localStorage.getItem("fcmToken");

                if (fcmToken) {
                    ["english", "hindi", "bengali"].forEach((lang) => {
                        if (settings.notification[lang]) {
                            unsubscribeTopic(fcmToken, `${appName}_${lang.toUpperCase()}`);
                        }
                    });

                    if (settings?.fcm) {
                        settings.fcm = settings.fcm.filter(
                            (item: any) => item.token !== fcmToken
                        );

                        settings.lastModificationTime = Date.now();

                        await updateUserSettings(settings);
                    }

                    await deleteToken(messaging);
                }
            }

            // Delete IndexedDB database
            try {
                await deleteDatabase();
                console.log("IndexedDB database deleted successfully during logout");
            } catch (dbError) {
                console.error("Error deleting IndexedDB database:", dbError);
                // Continue with logout even if database deletion fails
            }

            // Clear local storage
            localStorage.clear();
            sessionStorage.clear();
            resetFilters();

            // Sign out from Firebase
            await signOutUser();

            // Redirect to login page
            router.push("/login");
        } catch (error) {
            message.error(
                "Something went wrong while logging out. Please try again later."
            );
            console.error("Error logging out:", error);
            setLoading(false);
        } finally {
            console.log("");
            setLoading(false);
        }
    };

    return (
        <Modal
            centered
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            footer={null} // Remove default buttons
            width={380}
        >
            <h1
                className={`w-full text-[24px] mt-4 leading-[22.4px] font-bold ${inter.className}`}
            >
                Sign Out?
            </h1>
            <p
                className={`w-full text-[16px] mt-4 leading-[22.4px] font-[400] ${inter.className}`}
            >
                Are you sure you want to sign out?
            </p>
            {/* Custom Buttons */}
            <div className="flex gap-4 mt-4">
                <Button
                    onClick={handleLogout}
                    className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
                    style={{
                        background: `var(--primary-color)`,
                        color: "white",
                        borderColor: `var(--primary-color)`,
                    }}
                    loading={loading}
                >
                    Logout
                </Button>
                <Button
                    onClick={() => setIsModalOpen(false)}
                    className="h-[44px] w-[100px] border-none rounded-[12px] text-[16px] font-[600] hover:opacity-80"
                    style={{
                        background: `var(--primary-color)`,
                        color: "white",
                        borderColor: `var(--primary-color)`,
                    }}
                >
                    Cancel
                </Button>
            </div>
        </Modal>
    );
};

export default LogoutModal;
