/* Video Player Styles */

/* Custom slider styles for video progress */
.video-progress-slider .ant-slider-rail {
  background-color: rgba(255, 255, 255, 0.3);
  height: 5px;
  border-radius: 3px;
}

.video-progress-slider .ant-slider-track {
  background-color: #ff0000;
  height: 5px;
  border-radius: 3px;
}

.video-progress-slider .ant-slider-handle {
  border: 3px solid #ff0000;
  background-color: #ff0000;
  width: 18px;
  height: 18px;
  margin-top: -7px;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.video-progress-slider:hover .ant-slider-handle {
  opacity: 1;
}

.video-progress-slider .ant-slider-handle:focus {
  box-shadow: 0 0 0 8px rgba(255, 0, 0, 0.2);
}

/* Volume slider styles */
.ant-slider-vertical .ant-slider-rail {
  background-color: rgba(255, 255, 255, 0.4);
  width: 4px;
  border-radius: 2px;
}

.ant-slider-vertical .ant-slider-track {
  background-color: #ffffff;
  width: 4px;
  border-radius: 2px;
}

.ant-slider-vertical .ant-slider-handle {
  border: 2px solid #ffffff;
  background-color: #ffffff;
  width: 14px;
  height: 14px;
  margin-left: -5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Video container styles */
.video-container {
  position: relative;
  background: #000;
}

.video-container video {
  object-fit: contain;
}

/* Controls overlay */
.video-controls-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px;
  transition: opacity 0.3s ease;
}

.video-controls-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

/* Control buttons */
.video-control-btn {
  background: none;
  border: none;
  color: white;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
  cursor: pointer;
}

.video-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.video-control-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Speed options dropdown */
.speed-options {
  position: absolute;
  bottom: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
  min-width: 80px;
}

.speed-option {
  display: block;
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  color: white;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
}

.speed-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.speed-option.active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Volume control */
.volume-control {
  position: relative;
}

.volume-slider-container {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .video-controls-overlay {
    padding: 16px;
  }

  .video-control-btn {
    padding: 6px;
  }

  .speed-options {
    min-width: 70px;
  }

  .speed-option {
    padding: 6px 12px;
    font-size: 14px;
  }
}

/* Loading spinner for video */
.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
}

/* Video info section */
.video-info {
  background: white;
  padding: 24px;
}

.video-info h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  line-height: 1.3;
}

.video-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.video-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.video-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.video-action-btn:hover {
  background: #f0f0f0;
}

.video-action-btn.active {
  background: #fff2e8;
  border-color: #ffb366;
  color: #d46b08;
}

.video-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.video-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.video-thumbnail {
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Button hover effects */
.youtube-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.youtube-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.youtube-button:active {
  transform: translateY(0);
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}

.skeleton {
  animation: shimmer 1.2s ease-in-out infinite;
  background: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
  background-size: 800px 104px;
}

/* Video info section styling */
.video-info-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Queue section styling */
.queue-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.queue-header {
  background: linear-gradient(135deg, #fd7e14 0%, #ea6a01 100%);
  color: white;
  padding: 16px;
}

/* Related videos styling */
.related-videos-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
