import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Modal, Button, Input, Select, message } from "antd";
import { Poppins } from "next/font/google";
import { PlaylistType } from "@/src/libs/constant";
import { createPlaylist, editPlaylist } from "@/src/services/playlist.service";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const { TextArea } = Input;

const CreatePlaylistModal = ({
    isModalOpen,
    setIsModalOpen,
    editData = null,
    onEditSuccess,
    setNewAddedPlaylist,
}: any) => {
    const [playlistName, setPlaylistName] = useState("");
    const [category, setCategory] = useState("");
    const [description, setDescription] = useState("");
    const [listType, setListType] = useState("PRIVATE");
    const [selectOpen, setSelectOpen] = useState(false);
    const [btnLoading, setBtnLoading] = useState(false);

    const isEditMode = !!editData;

    const handleCancel = () => {
        setIsModalOpen(false);
        setPlaylistName("");
        setCategory("");
        setDescription("");
        setListType("PRIVATE");
    };

    const handleChange = (value: string) => {
        setListType(value);
    };

    const listTypeOptions = PlaylistType.map((item) => ({
        value: item.value,
        label: item.label,
    }));

    useEffect(() => {
        if (isModalOpen && isEditMode) {
            // Pre-fill fields if editing
            setPlaylistName(editData?.title || "");
            setCategory(editData?.lecturesCategory || "");
            setDescription(editData?.description || "");
            setListType(
                editData?.listType?.toUpperCase() === "PUBLIC"
                    ? "PUBLIC"
                    : "PRIVATE"
            );
        }
    }, [isModalOpen]);

    const handleSave = async () => {
        const missingFields = [];
        if (!playlistName.trim()) missingFields.push("playlist name");
        if (!category.trim()) missingFields.push("category");
        if (!description.trim()) missingFields.push("description");

        if (missingFields.length > 0) {
            message.warning(
                `Please enter ${missingFields.join(", ")} to continue.`
            );
            return;
        }

        setBtnLoading(true);

        try {
            if (isEditMode) {
                // ✏️ EDIT MODE
                await editPlaylist(listType, editData.listID, {
                    title: playlistName,
                    lecturesCategory: category,
                    discription: description,
                });
                message.success("Playlist updated successfully.");
                onEditSuccess({
                    title: playlistName,
                    lecturesCategory: category,
                    discription: description,
                });
            } else {
                // ➕ CREATE MODE
                const payload = {
                    title: playlistName,
                    lecturesCategory: category,
                    discription: description,
                    lectureCount: 0,
                    lectureIds: [],
                    listType: listType === "PUBLIC" ? "Public" : "Private",
                    thumbnail: "",
                };
                await createPlaylist(listType, payload);
                message.success("Successfully created new playlist.");
                setNewAddedPlaylist(payload);
            }
            handleCancel();
        } catch (err) {
            message.error("Something went wrong. Please try again.");
        } finally {
            setBtnLoading(false);
        }
    };

    return (
        <Modal
            title={
                <h1
                    className={`text-[24px] leading-8 font-[600] ${poppins.className}`}
                >
                    {isEditMode ? "Edit Playlist" : "Create new playlist"}
                </h1>
            }
            open={isModalOpen}
            onCancel={handleCancel}
            footer={null}
            centered
            width={346}
        >
            <div className="flex flex-col gap-4 mt-6">
                <Input
                    className="h-[40px] w-full !rounded-medium !text-sm !shadow-none hover:!border-primary focus:!border-primary"
                    placeholder="Playlist name"
                    onChange={(e) => setPlaylistName(e.target.value)}
                    value={playlistName}
                />

                <div className="flex gap-4 items-center">
                    <Input
                        className="h-[40px] w-[50%] !rounded-medium !text-sm !shadow-none hover:!border-primary focus:!border-primary"
                        placeholder="Category"
                        onChange={(e) => setCategory(e.target.value)}
                        value={category}
                    />
                    <Select
                        className="h-[40px] w-[50%]"
                        value={listType}
                        onChange={handleChange}
                        options={listTypeOptions}
                        onDropdownVisibleChange={(open) => setSelectOpen(open)}
                        suffixIcon={
                            <div className="flex items-center">
                                <Image
                                    src="/images/helperComponents/arrow.svg"
                                    width={16}
                                    height={16}
                                    alt=""
                                    className={`cursor-pointer transform transition-transform duration-300 ${
                                        selectOpen ? "rotate-180" : "rotate-0"
                                    }`}
                                />
                            </div>
                        }
                        popupClassName={`${poppins.className}`}
                        disabled={isEditMode} // optional: disable changing type on edit
                    />
                </div>

                <TextArea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Description"
                    style={{
                        height: 120,
                        resize: "none",
                        borderRadius: "12px",
                    }}
                    className="py-2 px-3 !shadow-none hover:!border-primary focus:!border-primary"
                />

                <div className="flex justify-end gap-1">
                    <Button
                        className="text-[15px] !text-text-primary !border-none"
                        onClick={handleCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="text-[15px] !text-primary !border-none"
                        onClick={handleSave}
                        loading={btnLoading}
                    >
                        {isEditMode ? "Save" : "Create"}
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default CreatePlaylistModal;
