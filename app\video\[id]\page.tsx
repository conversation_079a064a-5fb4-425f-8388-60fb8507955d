"use client";

import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import VideoPlayer from "@/components/videoPlayer/videoPlayer";
import { getLectureById } from "@/src/services/indexedDB.service";
import TabLoader from "@/components/ui/tabLoader";

const VideoPlayerPage = () => {
    const params = useParams();
    const router = useRouter();
    const [lectureData, setLectureData] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchLectureData = async () => {
            try {
                if (params.id) {
                    const lectureId = Array.isArray(params.id)
                        ? params.id[0]
                        : parseInt(params.id);

                    // Try to get from database
                    const lecture = await getLectureById(lectureId);
                    console.log("lecture", lecture);
                    if (lecture) {
                        // Check if lecture has video
                        const hasVideo = lecture.resources?.videos?.length > 0;
                        if (!hasVideo) {
                            setError(
                                "This lecture does not have video content."
                            );
                            return;
                        }
                        setLectureData(lecture);
                    } else {
                        setError("Lecture not found.");
                    }
                }
            } catch (error) {
                console.error("Error fetching lecture data:", error);
                setError("Failed to load lecture data.");
            } finally {
                setLoading(false);
            }
        };

        fetchLectureData();
    }, [params.id]);

    if (loading) {
        return (
            <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                <TabLoader visible={loading} />
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col justify-center items-center h-[calc(100vh-150px)]">
                <h2 className="text-xl font-semibold mb-4">{error}</h2>
                <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                    Go Back
                </button>
            </div>
        );
    }

    return <VideoPlayer lectureData={lectureData} />;
};

export default VideoPlayerPage;
