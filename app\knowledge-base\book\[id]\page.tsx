"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { getAuth } from "firebase/auth";
import {
    getBookParagraphs,
    BookParagraph,
} from "@/src/api/knowledge-base.api";
import { Poppins } from "next/font/google";
import BookView from "@/components/book/bookView";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const BookPage = () => {
    const params = useParams();
    const searchParams = useSearchParams();
    const [bookParagraphs, setBookParagraphs] = useState<BookParagraph[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [bookTitle, setBookTitle] = useState<string>("");

    const id: string = params.id as string;
    const searchQuery = searchParams.get("search");

    useEffect(() => {
        const fetchBookParagraphs = async () => {
            if (!id) return;

            try {
                setIsLoading(true);
                setError(null);

                // Get current user's ID token if available
                const auth = getAuth();
                const user = auth.currentUser;
                let idToken = null;

                if (user) {
                    idToken = await user.getIdToken();
                }

                // Fetch book paragraphs
                const response = await getBookParagraphs(
                    {
                        id,
                        query: searchQuery || undefined,
                    },
                    idToken
                );

                if (response && response.data) {
                    setBookParagraphs(response.data);
                    // Set book title from the first paragraph if available
                    if (response.data.length > 0) {
                        setBookTitle(response.data[0].bookTitle);
                    }
                } else {
                    setError("Failed to load book data");
                }
            } catch (err) {
                console.error("Error fetching book paragraphs:", err);
                setError("An error occurred while loading the book");
            } finally {
                setIsLoading(false);
            }
        };

        fetchBookParagraphs();
    }, [id, searchQuery]);

    if (isLoading) {
        return (
            <div
                className={`w-full h-[calc(100vh-60px)] flex flex-col items-center justify-center ${poppins.className}`}
            >
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                <p className="mt-6 text-lg font-medium text-gray-700">
                    Loading book content...
                </p>
                <p className="mt-2 text-sm text-gray-500">
                    Please wait while we prepare your content
                </p>
            </div>
        );
    }

    if (error || bookParagraphs.length === 0) {
        return (
            <div
                className={`w-full h-full flex items-center justify-center ${poppins.className}`}
            >
                <div className="text-center p-8">
                    <div className="text-red-500 text-5xl mb-4">⚠️</div>
                    <h2 className="text-xl font-semibold text-gray-800 mb-2">
                        {error || "Book content not found"}
                    </h2>
                    <p className="text-gray-600">
                        The requested book content could not be loaded.
                    </p>
                    <button
                        onClick={() => window.history.back()}
                        className="mt-4 px-4 py-2 bg-[#f39c12] text-white rounded hover:bg-[#e67e22] transition-colors"
                    >
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    return (
        <BookView
            bookParagraphs={bookParagraphs}
            bookTitle={bookTitle}
            searchQuery={searchQuery || undefined}
        />
    );
};

export default BookPage;
