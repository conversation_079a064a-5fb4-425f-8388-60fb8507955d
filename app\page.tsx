"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { waitForAuthState } from "@/src/libs/helper";

const Page = () => {
    const router = useRouter();

    useEffect(() => {
        const checkAuth = async () => {
            const user = await waitForAuthState();
            if (user) {
                router.replace("/media-library");
            } else {
                router.replace("/login");
            }
        };
        checkAuth();
    }, []);

    return null;
};

export default Page;
