import React from "react";

const IconItinerary = ({ width = 24, height = 24, color = "#919191" }: any) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3869_191)">
        <path
          d="M0 24.2617L8.77419 28.3711L16 24.2421L24.2581 28.3711L32 24.2617V7.22945L24.2581 11.3389L16 7.20984L8.77419 11.3389L0 7.22945V24.2617Z"
          fill="#6A6A6E"
        />
        <path
          d="M16.0012 24.2419V7.20959L8.77539 11.3386V28.3709L16.0012 24.2419ZM24.2593 28.3709L32.0012 24.2615V7.22921L24.2593 11.3386V28.3709Z"
          fill="#46464B"
        />
        <path
          d="M18.5809 20.6291H7.74219V19.5969H18.5809C19.1501 19.5969 19.6132 19.1338 19.6132 18.5646C19.6132 17.9954 19.1501 17.5324 18.5809 17.5324H15.4841C14.3457 17.5324 13.4196 16.6062 13.4196 15.4678C13.4196 14.3294 14.3457 13.4033 15.4841 13.4033H22.1938V14.4356H15.4841C14.915 14.4356 14.4519 14.8987 14.4519 15.4678C14.4519 16.037 14.915 16.5001 15.4841 16.5001H18.5809C19.7193 16.5001 20.6454 17.4262 20.6454 18.5646C20.6454 19.703 19.7193 20.6291 18.5809 20.6291Z"
          fill="#FAFAFA"
        />
        <path
          d="M18.5811 8.75818C18.5811 6.4778 20.4297 4.62915 22.7101 4.62915C24.9905 4.62915 26.8391 6.4778 26.8391 8.75818C26.8391 11.855 22.7101 15.4679 22.7101 15.4679C22.7101 15.4679 18.5811 11.855 18.5811 8.75818Z"
          fill="#2E2E32"
        />
        <path
          d="M22.71 10.8228C23.8502 10.8228 24.7745 9.89844 24.7745 8.75824C24.7745 7.61804 23.8502 6.69373 22.71 6.69373C21.5698 6.69373 20.6455 7.61804 20.6455 8.75824C20.6455 9.89844 21.5698 10.8228 22.71 10.8228Z"
          fill="#FAFAFA"
        />
        <path
          d="M3.09668 14.9518C3.09668 12.6714 4.94532 10.8228 7.22571 10.8228C9.5061 10.8228 11.3547 12.6714 11.3547 14.9518C11.3547 18.0486 7.22571 21.6615 7.22571 21.6615C7.22571 21.6615 3.09668 18.0486 3.09668 14.9518Z"
          fill="#2E2E32"
        />
        <path
          d="M7.22565 17.0162C8.36585 17.0162 9.29016 16.0919 9.29016 14.9517C9.29016 13.8115 8.36585 12.8872 7.22565 12.8872C6.08545 12.8872 5.16113 13.8115 5.16113 14.9517C5.16113 16.0919 6.08545 17.0162 7.22565 17.0162Z"
          fill="#FAFAFA"
        />
        <rect
          width="16"
          height="16"
          rx="8"
          transform="matrix(-1 0 0 1 32 16)"
          fill="url(#pattern0_3869_191)"
        />
      </g>
      <defs>
        <clipPath id="clip0_3869_191">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconItinerary;
