# Knowledge Base API Implementation

## Overview
This implementation provides a comprehensive API service for searching and retrieving content from the BVKS knowledge base, including transcriptions and books. It supports advanced search functionality, filtering, pagination, and detailed content retrieval with highlighting.

## Features Implemented

### 1. Search Functionality
- **Transcription Search**: Full-text search across lecture transcriptions
- **Book Search**: Search through book content and paragraphs
- **Advanced Filtering**: Filter by language, year, country, place, category, and month
- **Pagination Support**: Efficient handling of large result sets
- **Sorting Options**: Customizable result ordering

### 2. Content Retrieval
- **Transcription Details**: Detailed transcription data with audio resources
- **Book Paragraphs**: Structured book content with chapter information
- **Search Highlighting**: Highlighted search terms in results
- **Metadata Access**: Comprehensive content metadata and properties

### 3. Content Management
- **Suggested Edits**: Submit transcription edit suggestions
- **Content Validation**: Structured data validation and error handling
- **Authorization Support**: Token-based authentication for API access

## Data Structures

### Search Query Parameters
```typescript
interface SearchQueryParams {
  query: string;          // Search query text
  from?: number;          // Pagination offset
  size?: number;          // Results per page
  language?: string;      // Content language filter
  year?: string;          // Recording year filter
  country?: string;       // Recording country filter
  place?: string;         // Recording place filter
  category?: string;      // Content category filter
  month?: string;         // Recording month filter
  sort?: string;          // Sort order specification
}
```

### Transcription Item Structure
```typescript
interface TranscriptionItem {
  id: number;             // Unique transcription ID
  count: number;          // Search result relevance count
  title: string;          // Transcription title
  transcription: string[]; // Transcription text segments
  thumbnail: string;      // Thumbnail image URL
  length: number;         // Audio duration in seconds
  description: string;    // Content description
  language_main: string;  // Primary language
  tags: string;           // Content tags
  lengthType: string;     // Duration category
  dateOfRecording: string; // Recording date
  place: string;          // Recording location
  category: string;       // Content category
}
```

### Book Item Structure
```typescript
interface BookItem {
  id: string;             // Unique book ID
  title: string;          // Book title
  highlights: string[];   // Highlighted search matches
}
```

### Book Paragraph Structure
```typescript
interface BookParagraph {
  type: string;           // Paragraph type
  text: string;           // Paragraph content
  paragraphNumber: number; // Paragraph sequence number
  chapterTitle: string;   // Chapter title
  bookTitle: string;      // Book title
  topicTitle: string | null; // Topic title (if applicable)
  subtopicTitle: string | null; // Subtopic title (if applicable)
}
```

## Implementation Details

### Core API Functions

#### 1. `getTranscriptionSearchResults(queryParams, authorization)`
- **Purpose**: Search transcriptions with advanced filtering
- **Parameters**: Search parameters and authorization token
- **Returns**: Transcription search results with total count
- **Features**: Full-text search with metadata filtering

#### 2. `getBooksSearchResults(queryParams, authorization)`
- **Purpose**: Search book content with highlighting
- **Parameters**: Search parameters and authorization token
- **Returns**: Book search results with highlighted matches
- **Features**: Content search with relevance scoring

#### 3. `getTranscriptionDetail(params, authorization)`
- **Purpose**: Retrieve detailed transcription data
- **Parameters**: Transcription ID and optional search query
- **Returns**: Complete transcription with audio resources
- **Features**: Detailed metadata and resource information

#### 4. `getBookParagraphs(params, authorization)`
- **Purpose**: Retrieve book paragraphs with search highlighting
- **Parameters**: Book ID and optional search query
- **Returns**: Structured book content with chapter information
- **Features**: Hierarchical content organization

#### 5. `submitSuggestedEdit(payload, authorization)`
- **Purpose**: Submit transcription edit suggestions
- **Parameters**: Edit details and authorization token
- **Returns**: Submission confirmation with ID
- **Features**: Community-driven content improvement

## API Endpoints

### Search Endpoints
- **GET /search**: Transcription search with filtering
- **GET /books/search**: Book content search

### Detail Endpoints
- **GET /transcription/{id}**: Detailed transcription data
- **GET /books/{book-id}/paragraphs**: Book paragraph content

### Content Management
- **POST /transcriptions/{transcription-id}/suggested-edits**: Submit edit suggestions

## Integration Points

The knowledge base API integrates with:

1. **Authentication System**
   - Token-based authorization for API access
   - User-specific content access control

2. **Search Interface Components**
   - Search forms and result displays
   - Filter components and pagination
   - Content detail views

3. **Audio Player Integration**
   - Transcription-audio synchronization
   - Playback position tracking

4. **Content Management System**
   - Edit suggestion workflows
   - Content moderation processes

## Usage Examples

### Transcription Search
```javascript
import { getTranscriptionSearchResults } from './knowledge-base.api';

const searchParams = {
  query: "bhagavad gita",
  from: 0,
  size: 10,
  language: "english",
  category: "BG"
};

const results = await getTranscriptionSearchResults(searchParams, authToken);
console.log(`Found ${results.data.total} transcriptions`);
```

### Book Search
```javascript
import { getBooksSearchResults } from './knowledge-base.api';

const bookSearch = {
  query: "devotional service",
  size: 20
};

const bookResults = await getBooksSearchResults(bookSearch, authToken);
```

### Detailed Content Retrieval
```javascript
import { getTranscriptionDetail, getBookParagraphs } from './knowledge-base.api';

// Get transcription details
const transcription = await getTranscriptionDetail(
  { id: 12345, query: "search term" }, 
  authToken
);

// Get book paragraphs
const paragraphs = await getBookParagraphs(
  { id: "bhagavad-gita", query: "karma" }, 
  authToken
);
```

### Submit Edit Suggestion
```javascript
import { submitSuggestedEdit } from './knowledge-base.api';

const editSuggestion = {
  transcriptionId: 12345,
  timestamp: "00:15:30",
  originalText: "original text",
  suggestedText: "corrected text",
  reason: "Spelling correction"
};

const result = await submitSuggestedEdit(editSuggestion, authToken);
```

## Error Handling
- **Network Errors**: Handles API connectivity issues
- **Authentication Errors**: Manages token validation failures
- **Data Validation**: Validates API responses and parameters
- **Rate Limiting**: Respects API rate limits and quotas

## Benefits
1. **Comprehensive Search**: Full-text search across all content types
2. **Advanced Filtering**: Precise content discovery with multiple filters
3. **Performance**: Efficient pagination and result handling
4. **Content Quality**: Community-driven content improvement system
5. **Integration Ready**: Seamless integration with UI components
6. **Scalability**: Handles large content repositories efficiently
7. **User Experience**: Fast search with highlighted results and detailed content access
