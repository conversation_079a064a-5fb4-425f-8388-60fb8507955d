# User Statistics Streak Implementation

## Overview
This implementation provides a comprehensive user streak tracking system that monitors daily listening/watching activity and maintains streak counters for user engagement. The system automatically updates streaks based on consecutive days of activity.

## Features Implemented

### 1. Streak Tracking Logic
- **Current Streak**: Tracks consecutive days of listening/watching activity
- **Best Streak**: Records the highest streak ever achieved by the user
- **Last Updated Time**: Timestamp of the last activity for accurate date comparison
- **Automatic Updates**: Updates every 60 seconds during audio/video playback

### 2. Firebase Collection Structure
```
users/{userId}/statistics/userStatistics
{
    bestStreak: number,           // Highest streak achieved
    currentStreak: number,        // Current consecutive days
    lastUpdatedTime: number,      // Timestamp in milliseconds
    creationTimestamp: number,    // When record was created
    lastModifiedTimestamp: number, // Last modification time
    documentId: string,           // Document reference ID
    documentPath: string          // Full document path
}
```

### 3. Streak Logic Rules

#### Same Day Activity (daysDifference === 0)
- **Action**: Update timestamp only
- **Streak**: No change to current or best streak
- **Use Case**: Multiple listening sessions on the same day

#### Next Day Activity (daysDifference === 1)
- **Action**: Increment current streak by 1
- **Best Streak**: Update if current streak exceeds previous best
- **Use Case**: Consecutive daily listening activity

#### Gap in Activity (daysDifference > 1)
- **Action**: Reset current streak to 1
- **Best Streak**: Remains unchanged
- **Use Case**: User missed one or more days

#### Edge Cases (daysDifference < 0)
- **Action**: Update timestamp only
- **Streak**: No changes
- **Use Case**: System clock issues or data inconsistencies

## Implementation Details

### Core Functions

#### 1. `getUserStatistics(userId: string)`
- **Purpose**: Retrieves user statistics from Firebase
- **Default Creation**: Creates default stats if none exist
- **Returns**: UserStatistics object

#### 2. `updateUserStatistics(userId: string)`
- **Purpose**: Core streak logic implementation
- **Date Comparison**: Uses start-of-day comparison for accuracy
- **Updates**: Both current and best streaks as needed
- **Returns**: Updated UserStatistics object

#### 3. `trackUserStatistics()`
- **Purpose**: Public function called from audio/video services
- **Authentication**: Checks for logged-in user
- **Error Handling**: Graceful failure without breaking main functionality

### Integration Points

#### Audio Player Integration
- **File**: `src/services/audioPlayerServices/audioPlayerActivity.service.ts`
- **Integration**: Added to `updateUserListeningActivity` functions
- **Frequency**: Called every 60 seconds during audio playback

#### Video Player Integration
- **File**: `src/services/videoPlayerServices/videoPlayerActivity.service.ts`
- **Integration**: Added to `updateUserVideoWatchingActivity` functions
- **Frequency**: Called every 60 seconds during video playback

#### Statistics UI Integration
- **File**: `components/statistics/statistics.tsx`
- **Display**: Shows current streak with best streak indicator
- **Loading**: Handles loading states and fallback to sample data
- **API**: Uses `fetchUserStatistics` from `src/api/userStatistics.api.ts`

## Usage Examples

### Automatic Usage
The streak tracking happens automatically when users:
- Play audio content (every 60 seconds)
- Play video content (every 60 seconds)
- Continue listening/watching across multiple days

### Manual Usage
```javascript
import { trackUserStatistics, getUserStatistics } from './userStatistics.service';

// Update statistics manually
const updatedStats = await trackUserStatistics();

// Get current statistics
const userId = localStorage.getItem("firebaseUid");
const currentStats = await getUserStatistics(userId);

console.log(`Current streak: ${currentStats.currentStreak} days`);
console.log(`Best streak: ${currentStats.bestStreak} days`);
```

### API Usage
```javascript
import { fetchUserStatistics } from '../api/userStatistics.api';

// Fetch for UI display
const stats = await fetchUserStatistics();
if (stats) {
    console.log(`User's current streak: ${stats.currentStreak}`);
}
```

## Error Handling

### Service Level
- **Firebase Errors**: Logged and re-thrown for proper error propagation
- **Authentication**: Graceful handling of non-logged-in users
- **Data Validation**: Ensures proper data structure and types

### Integration Level
- **Non-blocking**: Statistics errors don't break audio/video functionality
- **Logging**: All errors logged for debugging purposes
- **Fallback**: UI falls back to sample data if statistics unavailable

## Performance Considerations

### Efficient Updates
- **Timestamp Comparison**: Uses efficient date comparison logic
- **Minimal Writes**: Only writes to Firebase when streak changes occur
- **Error Isolation**: Statistics failures don't impact core functionality

### Scalability
- **User-specific**: Each user has their own statistics document
- **Lightweight**: Minimal data structure for fast reads/writes
- **Indexed**: Uses Firebase document structure for efficient queries

## Testing Scenarios

### Streak Continuation
1. User listens on Day 1 → Streak: 1
2. User listens on Day 2 → Streak: 2
3. User listens on Day 3 → Streak: 3

### Streak Break
1. User has 5-day streak
2. User misses Day 6
3. User listens on Day 7 → Streak resets to 1

### Best Streak Update
1. User achieves 10-day streak (new best)
2. User breaks streak, starts new one
3. Current streak grows but best remains 10 until exceeded

## Future Enhancements

### Potential Features
- **Weekly/Monthly Streaks**: Track different time periods
- **Streak Rewards**: Gamification elements
- **Streak Notifications**: Remind users to maintain streaks
- **Streak Analytics**: Detailed streak history and patterns
- **Social Features**: Share streaks with friends

### Performance Optimizations
- **Batch Updates**: Combine multiple statistics updates
- **Caching**: Local caching of statistics data
- **Background Sync**: Offline streak tracking with sync when online
