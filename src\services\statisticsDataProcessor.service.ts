import { collection, getDocs } from "firebase/firestore";
import { db } from "../config/firebase.config";
import {
    FirebaseListenInfo,
    StatisticsTimeData,
    ProgressPeriodData,
    CategoryData,
    ChartDataPoint,
} from "../../components/statistics/statisticsData";
import { getTotalLecturesCount } from "./metadata.service";
import { formatCategoryTimeHMS } from "../utils/chartTimeFormat";

// Category colors mapping
const CATEGORY_COLORS = {
    BG: "#F94680",
    CC: "#FEBD17",
    SB: "#A6C43F",
    Seminars: "#8095E4",
    VSN: "#1BC0BA",
    others: "#47A2D1",
};

// Category full names mapping
const CATEGORY_NAMES = {
    BG: "Bhagavad-gita",
    CC: "Caitanya-caritamrta",
    SB: "Srimad Bhagavatam",
    Seminars: "Seminars",
    VSN: "Visnu-sahasranama",
    others: "Others",
};

/**
 * Fetch all listenInfo data for the current user
 */
export const fetchUserListenInfo = async (): Promise<FirebaseListenInfo[]> => {
    try {
        const userId = localStorage.getItem("firebaseUid");
        if (!userId) {
            throw new Error("User not logged in");
        }

        const listenInfoRef = collection(db, `users/${userId}/listenInfo`);
        const snapshot = await getDocs(listenInfoRef);

        const docs = snapshot.docs.map(
            (doc) =>
                ({
                    id: doc.id,
                    ...doc.data(),
                } as unknown as FirebaseListenInfo)
        );

        // Sort by date (newest first)
        docs.sort((a, b) => {
            const parseDate = (id: string) => {
                const [day, month, year] = id.split("-").map(Number);
                return new Date(year, month - 1, day);
            };
            return (
                parseDate(b.documentId).getTime() -
                parseDate(a.documentId).getTime()
            );
        });

        return docs;
    } catch (error) {
        console.error("Error fetching user listen info:", error);
        throw error;
    }
};

/**
 * Convert seconds to hours, minutes and seconds format
 */
const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    const parts: string[] = [];

    if (hours > 0) {
        parts.push(`${hours}h`);
    }

    if (minutes > 0) {
        parts.push(`${minutes}m`);
    }

    if (remainingSeconds > 0 || parts.length === 0) {
        parts.push(`${remainingSeconds}s`);
    }

    return parts.join(" ");
};

/**
 * Convert seconds to hours (decimal)
 */
const secondsToHours = (seconds: number): number => {
    return Math.round((seconds / 3600) * 100) / 100; // Round to 2 decimal places
};

/**
 * Get current date info
 */
const getCurrentDateInfo = () => {
    const now = new Date();
    return {
        currentYear: now.getFullYear(),
        currentMonth: now.getMonth() + 1, // 1-based
        currentDay: now.getDate(),
        currentWeekStart: getWeekStart(now),
        currentWeekEnd: getWeekEnd(now),
    };
};

/**
 * Get start of current week (Monday)
 */
const getWeekStart = (date: Date): Date => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(d.setDate(diff));
};

/**
 * Get end of current week (Sunday)
 */
const getWeekEnd = (date: Date): Date => {
    const weekStart = getWeekStart(date);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    return weekEnd;
};

/**
 * Filter data by date range
 */
const filterDataByDateRange = (
    data: FirebaseListenInfo[],
    startDate: Date,
    endDate: Date
): FirebaseListenInfo[] => {
    // Normalize start and end dates to start of day for proper comparison
    const normalizedStartDate = new Date(startDate);
    normalizedStartDate.setHours(0, 0, 0, 0);

    const normalizedEndDate = new Date(endDate);
    normalizedEndDate.setHours(23, 59, 59, 999);

    return data.filter((item) => {
        const itemDate = new Date(
            item.dateOfRecord.year,
            item.dateOfRecord.month - 1,
            item.dateOfRecord.day
        );
        // itemDate is already at 00:00:00 by default
        return itemDate >= normalizedStartDate && itemDate <= normalizedEndDate;
    });
};

/**
 * Process categories data from listenInfo with data validation
 */
const processCategories = (data: FirebaseListenInfo[]): CategoryData[] => {
    const totals = {
        BG: 0,
        CC: 0,
        SB: 0,
        Seminars: 0,
        VSN: 0,
        others: 0,
    };

    data.forEach((item) => {
        // Handle both uppercase and lowercase category keys
        const listenDetails = item.listenDetails;

        // Map lowercase to uppercase and get individual category values
        const categoryValues = {
            BG: (listenDetails.BG || 0) + (listenDetails.bg || 0),
            CC: (listenDetails.CC || 0) + (listenDetails.cc || 0),
            SB: (listenDetails.SB || 0) + (listenDetails.sb || 0),
            Seminars:
                (listenDetails.Seminars || 0) + (listenDetails.seminars || 0),
            VSN: (listenDetails.VSN || 0) + (listenDetails.vsn || 0),
            others: listenDetails.others || 0,
        };

        // Calculate sum of all categories for this item
        const categoriesSum = Object.values(categoryValues).reduce(
            (sum, val) => sum + val,
            0
        );

        // Get total audio/video listen time for this item
        const totalAudioVideo = item.audioListen || 0; // + (item.videoListen || 0)

        // If there's a difference, add it to 'others' category
        // const difference = totalAudioVideo - categoriesSum;
        // if (difference > 0) {
        //     categoryValues.others += difference;
        // }

        if (categoriesSum !== totalAudioVideo) {
            if (categoriesSum < totalAudioVideo) {
                // If there's a difference, add it to 'others' category
                categoryValues.others += totalAudioVideo - categoriesSum;
            } else if (categoriesSum > 0) {
                // Scale down all categories proportionally
                const scale = totalAudioVideo / categoriesSum;
                ["BG", "CC", "SB", "Seminars", "VSN", "others"].forEach(
                    (key) => {
                        categoryValues[key as keyof typeof categoryValues] =
                            Math.round(
                                categoryValues[
                                    key as keyof typeof categoryValues
                                ] * scale
                            );
                    }
                );
                // Recalculate sum after scaling (may be off by a few seconds due to rounding)
                let scaledSum = Object.values(categoryValues).reduce(
                    (sum, val) => sum + val,
                    0
                );
                // If still not matching, adjust 'others' to fix rounding error
                categoryValues.others += totalAudioVideo - scaledSum;
            }
        }

        // Add to totals
        totals.BG += categoryValues.BG;
        totals.CC += categoryValues.CC;
        totals.SB += categoryValues.SB;
        totals.Seminars += categoryValues.Seminars;
        totals.VSN += categoryValues.VSN;
        totals.others += categoryValues.others;
    });

    // Define the desired order: SB, BG, CC, VSN, SEMINARS, OTHERS
    const orderedKeys = ["SB", "BG", "CC", "VSN", "Seminars", "others"];

    return orderedKeys.map((key) => ({
        name: key,
        fullName: CATEGORY_NAMES[key as keyof typeof CATEGORY_NAMES],
        hours: secondsToHours(totals[key as keyof typeof totals]),
        seconds: totals[key as keyof typeof totals], // Add seconds field for h m s formatting
        color: CATEGORY_COLORS[key as keyof typeof CATEGORY_COLORS],
    }));
};

/**
 * Calculate total listening time
 */
const calculateTotalTime = (data: FirebaseListenInfo[]): string => {
    const totalSeconds = data.reduce(
        (sum, item) => sum + (item.audioListen || 0), // + (item.videoListen || 0)
        0
    );
    // return formatTime(totalSeconds);
    return formatCategoryTimeHMS(totalSeconds)
};

/**
 * Calculate progress percentage and lectures listened
 */
const calculateProgress = async (
    data: FirebaseListenInfo[]
): Promise<{ percentage: number; lecturesListened: number }> => {
    const totalPlayedIds = new Set<number>();
    data.forEach((item) => {
        item.playedIds?.forEach((id) => totalPlayedIds.add(id));
    });

    const lecturesListened = totalPlayedIds.size;

    // Get total lectures count from metadata collection
    const totalLectures = await getTotalLecturesCount();

    // Calculate percentage based on actual total lectures
    const percentage =
        totalLectures > 0
            ? Math.min(
                  Math.round((lecturesListened / totalLectures) * 100),
                  100
              )
            : 0;

    return { percentage, lecturesListened };
};

/**
 * Process Year data - show current year months
 */
export const processYearData = async (): Promise<StatisticsTimeData> => {
    try {
        const allData = await fetchUserListenInfo();
        const { currentYear } = getCurrentDateInfo();

        // Filter data for current year
        const yearStart = new Date(currentYear, 0, 1);
        const yearEnd = new Date(currentYear, 11, 31);
        const yearData = filterDataByDateRange(allData, yearStart, yearEnd);

        // Create chart data for all 12 months
        const monthNames = [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ];
        const chartData: ChartDataPoint[] = monthNames.map((month, index) => {
            const monthData = yearData.filter(
                (item) => item.dateOfRecord.month === index + 1
            );
            const totalSeconds = monthData.reduce(
                (sum, item) => sum + (item.audioListen || 0), // + (item.videoListen || 0)
                0
            );

            return {
                day: month,
                date: currentYear.toString(),
                hours: secondsToHours(totalSeconds),
            };
        });

        const progress = await calculateProgress(yearData);
        const categories = processCategories(yearData);

        return {
            progress: {
                percentage: progress.percentage,
                lecturesListened: progress.lecturesListened,
                streak: 0, // Will be handled by userStatistics
                totalListened: calculateTotalTime(yearData),
            },
            chartData,
            categories,
        };
    } catch (error) {
        console.error("Error processing year data:", error);
        throw error;
    }
};

/**
 * Process Month data - show all dates of current month
 */
export const processMonthData = async (): Promise<StatisticsTimeData> => {
    try {
        const allData = await fetchUserListenInfo();
        const { currentYear, currentMonth } = getCurrentDateInfo();

        // Filter data for current month
        const monthStart = new Date(currentYear, currentMonth - 1, 1);
        const monthEnd = new Date(currentYear, currentMonth, 0); // Last day of current month
        const monthData = filterDataByDateRange(allData, monthStart, monthEnd);

        // Create chart data for all days in current month
        const daysInMonth = monthEnd.getDate();
        const chartData: ChartDataPoint[] = [];

        for (let day = 1; day <= daysInMonth; day++) {
            const dayData = monthData.filter(
                (item) => item.dateOfRecord.day === day
            );
            const totalSeconds = dayData.reduce(
                (sum, item) => sum + (item.audioListen || 0), // + (item.videoListen || 0)
                0
            );

            chartData.push({
                day: day.toString(),
                date: day,
                hours: secondsToHours(totalSeconds),
            });
        }

        const progress = await calculateProgress(monthData);
        const categories = processCategories(monthData);

        return {
            progress: {
                percentage: progress.percentage,
                lecturesListened: progress.lecturesListened,
                streak: 0, // Will be handled by userStatistics
                totalListened: calculateTotalTime(monthData),
            },
            chartData,
            categories,
        };
    } catch (error) {
        console.error("Error processing month data:", error);
        throw error;
    }
};

/**
 * Process Week data - show current week days
 */
export const processWeekData = async (): Promise<StatisticsTimeData> => {
    try {
        const allData = await fetchUserListenInfo();
        const { currentWeekStart, currentWeekEnd } = getCurrentDateInfo();

        // Filter data for current week
        const weekData = filterDataByDateRange(
            allData,
            currentWeekStart,
            currentWeekEnd
        );

        // Create chart data for all 7 days of the week
        const dayNames = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];
        const chartData: ChartDataPoint[] = [];

        for (let i = 0; i < 7; i++) {
            const currentDay = new Date(currentWeekStart);
            currentDay.setDate(currentWeekStart.getDate() + i);

            const dayData = weekData.filter((item) => {
                const itemDate = new Date(
                    item.dateOfRecord.year,
                    item.dateOfRecord.month - 1,
                    item.dateOfRecord.day
                );
                return itemDate.toDateString() === currentDay.toDateString();
            });

            const totalSeconds = dayData.reduce(
                (sum, item) => sum + (item.audioListen || 0), // + (item.videoListen || 0)
                0
            );

            chartData.push({
                day: dayNames[i],
                date: currentDay.getDate(),
                hours: secondsToHours(totalSeconds),
            });
        }

        const progress = await calculateProgress(weekData);
        const categories = processCategories(weekData);

        return {
            progress: {
                percentage: progress.percentage,
                lecturesListened: progress.lecturesListened,
                streak: 0, // Will be handled by userStatistics
                totalListened: calculateTotalTime(weekData),
            },
            chartData,
            categories,
        };
    } catch (error) {
        console.error("Error processing week data:", error);
        throw error;
    }
};

/**
 * Process All Time progress data
 */
export const processAllTimeProgressData =
    async (): Promise<ProgressPeriodData> => {
        try {
            const allData = await fetchUserListenInfo();
            const categories = processCategories(allData);
            const totalListened = calculateTotalTime(allData);

            return {
                totalListened,
                categories,
            };
        } catch (error) {
            console.error("Error processing all time progress data:", error);
            throw error;
        }
    };

/**
 * Process Last Week progress data
 */
export const processLastWeekProgressData =
    async (): Promise<ProgressPeriodData> => {
        try {
            const allData = await fetchUserListenInfo();
            const now = new Date();

            // Get last week's date range
            const lastWeekEnd = new Date(now);
            lastWeekEnd.setDate(now.getDate() - now.getDay()); // Last Sunday
            const lastWeekStart = new Date(lastWeekEnd);
            lastWeekStart.setDate(lastWeekEnd.getDate() - 6); // Previous Monday

            const weekData = filterDataByDateRange(
                allData,
                lastWeekStart,
                lastWeekEnd
            );
            const categories = processCategories(weekData);
            const totalListened = calculateTotalTime(weekData);

            return {
                totalListened,
                categories,
            };
        } catch (error) {
            console.error("Error processing last week progress data:", error);
            throw error;
        }
    };

/**
 * Process Last Month progress data
 */
export const processLastMonthProgressData =
    async (): Promise<ProgressPeriodData> => {
        try {
            const allData = await fetchUserListenInfo();
            const now = new Date();

            // Get last month's date range
            const lastMonthStart = new Date(
                now.getFullYear(),
                now.getMonth() - 1,
                1
            );
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

            const monthData = filterDataByDateRange(
                allData,
                lastMonthStart,
                lastMonthEnd
            );
            const categories = processCategories(monthData);
            const totalListened = calculateTotalTime(monthData);

            return {
                totalListened,
                categories,
            };
        } catch (error) {
            console.error("Error processing last month progress data:", error);
            throw error;
        }
    };

/**
 * Process Last Year progress data
 */
export const processLastYearProgressData =
    async (): Promise<ProgressPeriodData> => {
        try {
            const allData = await fetchUserListenInfo();
            const now = new Date();

            // Get last year's date range
            const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
            const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31);

            const yearData = filterDataByDateRange(
                allData,
                lastYearStart,
                lastYearEnd
            );
            const categories = processCategories(yearData);
            const totalListened = calculateTotalTime(yearData);

            return {
                totalListened,
                categories,
            };
        } catch (error) {
            console.error("Error processing last year progress data:", error);
            throw error;
        }
    };

/**
 * Main function to get statistics data based on time period
 */
export const getStatisticsData = async (
    period: string
): Promise<StatisticsTimeData> => {
    switch (period) {
        case "Year":
            return await processYearData();
        case "Month":
            return await processMonthData();
        case "Week":
            return await processWeekData();
        default:
            return await processWeekData();
    }
};

/**
 * Get total progress data (all-time percentage and lectures listened)
 */
export const getTotalProgressData = async (): Promise<{
    percentage: number;
    lecturesListened: number;
}> => {
    try {
        const allData = await fetchUserListenInfo();
        return await calculateProgress(allData);
    } catch (error) {
        console.error("Error getting total progress data:", error);
        throw error;
    }
};

/**
 * Main function to get progress data based on period
 */
export const getProgressData = async (
    period: string
): Promise<ProgressPeriodData> => {
    switch (period) {
        case "All time":
            return await processAllTimeProgressData();
        case "Last week":
            return await processLastWeekProgressData();
        case "Last month":
            return await processLastMonthProgressData();
        case "Last year":
            return await processLastYearProgressData();
        default:
            return await processAllTimeProgressData();
    }
};
