"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { MenuOutlined } from "@ant-design/icons";
import { Avatar, Button, Dropdown, Input } from "antd";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import LogoutIcon from "@mui/icons-material/Logout";
import { SearchOutlined } from "@ant-design/icons";
import { useSidebarContext } from "@/src/context/sidebar.context";
import appConfig from "@/src/config/apps";
import LogoutModal from "../Modal/logoutModal";
import { useSearchContext } from "@/src/context/search.context";
import { useAudioContext } from "@/src/context/audio.context";
import { useVideoContext } from "@/src/context/video.context";
import { usePathname, useRouter } from "next/navigation";
import StatisticsDropdown from "./statisticsDropdown";
import { useIndexedDBContext } from "@/src/context/indexedDB.context";
import { fetchUserStatistics } from "@/src/api/userStatistics.api";
import { checkAndResetStreakOnLaunch } from "@/src/services/userStatistics.service";
import { fetchAllAdminSettings } from "@/src/api/settings.api";

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const searchablePages = [
  "/media-library",
  "/popular",
  "/favorites",
  "/playlists",
  "/history",
  "/gallery",
];

const Navbar = () => {
  const pathName = usePathname();
  const router = useRouter();
  const { setIsCollapsed, setIsTabChangeLoading } = useSidebarContext();
  const { isAudioStatisticsUpdating } = useAudioContext();
  const { isVideoStatisticsUpdating } = useVideoContext();
  const {
    searchQuery,
    setSearchQuery,
    setSearch,
    setDeepSearch,
    setIsSearching,
  } = useSearchContext();

  const { syncData } = useIndexedDBContext();

  const [searchValue, setSearchValue] = useState("");
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [isStatisticsDropdownOpen, setIsStatisticsDropdownOpen] =
    useState(false);
  // Add a state to track if component has been mounted
  const [isMounted, setIsMounted] = useState(false);
  const [currentStreak, setCurrentStreak] = useState<number | null>(null);
  const [isItineraryVisible, setIsItineraryVisible] = useState(false);

  useEffect(() => {
    const initializeStreakAndStats = async () => {
      try {
        // First, check and reset streak if needed (when web app launches)
        const checkedStats = await checkAndResetStreakOnLaunch();

        // Then fetch the latest statistics to ensure we get fresh data
        const stats = await fetchUserStatistics();
        if (stats) {
          setCurrentStreak(stats.currentStreak);
        } else if (checkedStats) {
          // Fallback to checked stats if fetch fails
          setCurrentStreak(checkedStats.currentStreak);
        }
      } catch (err) {
        console.error("Failed to initialize streak and stats:", err);
      }
    };
    initializeStreakAndStats();
  }, [isVideoStatisticsUpdating, isAudioStatisticsUpdating]);

  useEffect(() => {
    // Access localStorage safely in useEffect
    const email = localStorage.getItem("email");
    if (email) {
      setEmail(email);
    }

    const collapsed = localStorage.getItem("collapsed");
    if (collapsed)
      collapsed === "true" ? setIsCollapsed(true) : setIsCollapsed(false);

    // Set mounted state to true
    setIsMounted(true);
  }, []);

    useEffect(() => {
      const loadSettings = async () => {
        try {
          const settings = await fetchAllAdminSettings();
          setIsItineraryVisible(settings?.showItinerary?.isVisible ?? false);
        } catch (error) {
          console.error("Failed to load admin settings:", error);
        }
      };
  
      loadSettings();
    }, []);

  const items: any = [
    {
      key: "1",
      label: (
        <h1 className="text-[14px] py-1 cursor-default tracking-wide font-[500]">
          {email}
        </h1>
      ),
    },
    {
      key: "2",
      label: (
        <div
          onClick={() => isMounted && setIsLogoutModalOpen(true)}
          className="text-[15px] py-1 flex gap-2 items-center font-[500]"
        >
          <LogoutIcon /> <p className="pb-1">Sign Out</p>
        </div>
      ),
    },
  ];

  useEffect(() => {
    // Access localStorage safely in useEffect
    const email = localStorage.getItem("email");
    if (email) {
      setEmail(email);
    }
  }, []);

  return (
    <div
      className={`w-full h-[58px] border-b border-border px-7 flex gap-2 items-center justify-between ${roboto.className}`}
    >
      <div className="flex gap-6 items-center">
        <MenuOutlined
          className="text-xl cursor-pointer"
          onClick={() => {
            setIsCollapsed((prev: boolean) => {
              if (prev) {
                localStorage.setItem("collapsed", "false");
              } else {
                localStorage.setItem("collapsed", "true");
              }
              return !prev;
            });
          }}
        />
        <div
          className="max-[912px]:hidden flex gap-4 items-center"
          onClick={() => {
            setIsTabChangeLoading(true);
            syncData()
              .then(() => {
                if (pathName === "/media-library") {
                  setIsTabChangeLoading(false);
                }
                router.push("/media-library");
                console.log("Data synced successfully");
              })
              .catch((error) => {
                setIsTabChangeLoading(false);
                console.error("Error syncing data:", error);
              });
          }}
        >
          <Image
            src="/images/auth/ava.jpg"
            width={88}
            height={88}
            alt=""
            className="w-[40px] h-[40px] cursor-pointer"
          />
          <h3 className="max-[1110px]:hidden text-[20px] font-bold leading-[100%] text-center cursor-pointer">
            {appConfig.appTitle}
          </h3>
        </div>
      </div>

      {searchablePages.some((path) => pathName.startsWith(path)) && (
        <div className="w-[calc(100%-40px)] md:w-auto flex gap-3 items-center">
          <Input
            className="h-[34px] w-[calc(100%-100px)] min-[850px]:w-[410px] !rounded-medium !text-sm !shadow-none !outline-none hover:!border-primary focus:!border-primary"
            placeholder="Search lectures"
            onChange={(e) => {
              setSearchValue(e.target.value);
              if (e.target.value.trim() === "") {
                setSearchQuery("");
                setSearchValue("");
                setSearch(false);
                setDeepSearch(false);
                setIsSearching(false);
              }
            }}
            value={searchValue}
            onClear={() => {
              setSearchQuery("");
              setSearchValue("");
              setSearch(false);
              setDeepSearch(false);
              setIsSearching(false);
            }}
            allowClear
            onPressEnter={() => {
              if (searchValue.trim() !== "") {
                setSearchQuery(searchValue.trim());
                setIsSearching(true);
                setSearch((prev: boolean) => !prev);
                console.log("searchValue", searchValue);
              } else {
                setSearchQuery("");
                setSearchValue("");
                setIsSearching(false);
                setSearch((prev: boolean) => !prev);
                console.log("searchValue", searchValue);
              }
            }}
          />
          <div className="flex gap-2">
            <Button
              className="h-[34px] w-[44px] lg:w-[100px] border-none rounded-medium text-[14px] font-[500] hover:opacity-80"
              style={{
                background: `var(--primary-color)`,
                color: "white",
                borderColor: `var(--primary-color)`,
              }}
              onClick={() => {
                if (searchValue.trim() !== "") {
                  setSearchQuery(searchValue.trim());
                  setIsSearching(true);
                  setSearch((prev: boolean) => !prev);
                } else {
                  setSearchQuery("");
                  setSearchValue("");
                  setIsSearching(false);
                  setSearch((prev: boolean) => !prev);
                }
                setDeepSearch(false);
              }}
              disabled={searchValue.trim() === ""}
            >
              <Image
                src="/images/navbar/search.png"
                width={14}
                height={14}
                alt="Search Icon"
                className="cursor-pointer"
              />
              <span className="hidden lg:block">Search</span>
            </Button>

            <div className="w-auto sm:w-[140px]">
              {appConfig.features.enableDeepSearch &&
                pathName === "/media-library" && (
                  <Button
                    className="h-[34px] w-auto sm:w-[140px] border hover:!border-primary rounded-medium text-[14px] font-bold hover:opacity-80"
                    style={{
                      background: `var(--background-color)`,
                      color: `var(--primary-color)`,
                      borderColor: `var(--primary-color)`,
                    }}
                    onClick={() => {
                      setIsSearching(true);
                      setSearchQuery(searchValue.trim());
                      setDeepSearch((prev: boolean) => !prev);
                    }}
                    disabled={searchValue.trim() === ""}
                  >
                    <Image
                      src="/images/navbar/deep-search.svg"
                      width={22}
                      height={22}
                      alt="Search Icon"
                      className="cursor-pointer"
                    />
                    <span className="hidden sm:block"> Deep Search</span>
                  </Button>
                )}
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-row gap-5">
        {pathName !== "/itinerary" && isItineraryVisible && (
          <div
            className="min-[600px]:flex hidden relative w-[40px] h-[40px] cursor-pointer"
            onClick={() => router.push("/itinerary")}
          >
            <Image
              src="/images/sidebar/itinerarySelected.svg"
              alt="Itinerary"
              fill
              className="object-contain"
            />
          </div>
        )}

        {pathName !== "/statistics" && (
          <Dropdown
            open={isStatisticsDropdownOpen}
            onOpenChange={setIsStatisticsDropdownOpen}
            trigger={["click"]}
            placement="bottomRight"
            dropdownRender={() => (
              <StatisticsDropdown
                onClose={() => setIsStatisticsDropdownOpen(false)}
              />
            )}
          >
            {/* <Image
                    src="/images/navbar/statistics/statistics.svg"
                    width={40}
                    height={40}
                    alt="Statistics"
                    className="w-[40px] h-[40px] cursor-pointer"
                /> */}
            <div className="relative w-[40px] h-[40px] cursor-pointer">
              <Image
                src="/images/navbar/statistics/statistics.svg"
                alt="Statistics"
                fill
                className="object-contain"
              />
              <Image
                src={
                  currentStreak !== null && currentStreak > 0
                    ? "/images/ui/flame_light.gif"
                    : "/images/ui/flame_gray.gif"
                }
                alt="Flame"
                width={32}
                height={32}
                className="absolute top-[-14px] right-[-10px]"
              />
            </div>
          </Dropdown>
        )}

        <Dropdown
          className="hidden md:block"
          menu={{ items }}
          trigger={["click"]}
          placement="bottomRight"
        >
          <div className="flex gap-1 items-center cursor-pointer">
            <Avatar
              style={{
                backgroundColor: `var(--accent-color)`,
                color: `var(--secondary-color)`,
                verticalAlign: "middle",
              }}
              className="font-bold"
              size="large"
            >
              {email.toUpperCase().slice(0, 2)}
            </Avatar>
            <KeyboardArrowDownIcon className="text-[12px] !font-[600] !h-[60%] cursor-pointer" />
          </div>
        </Dropdown>
      </div>

      <LogoutModal
        isModalOpen={isLogoutModalOpen}
        setIsModalOpen={setIsLogoutModalOpen}
      />
    </div>
  );
};

export default Navbar;
