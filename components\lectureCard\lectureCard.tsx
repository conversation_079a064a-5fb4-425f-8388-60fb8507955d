import Image from "next/image";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Dropdown, message, Spin } from "antd";
import { FaCheck } from "react-icons/fa6";
import LectureInfoModal from "./lectureInfoModal";
import AddToPlaylistModal from "../Modal/addToPlaylistModal";
import {
    formatDateObjToDDMMYYYY,
    formatSecondsToHHMMSS,
} from "@/src/utils/timeFormat";
import appConfig from "@/src/config/apps";
import Skeleton from "react-loading-skeleton";
import IconSelected from "../ui/iconSelected";
import {
    addToFavourite,
    removeFromFavourite,
} from "@/src/services/favouriteLecture.service";
import {
    useFavoriteSync,
    broadcastFavoriteChange,
} from "@/src/hooks/useFavoriteSync";
import {
    markAsCompleted,
    removeFromCompleted,
} from "@/src/services/lectureCompletion.service";
import ShareModal from "../Modal/shareModal";
import { LoadingOutlined } from "@ant-design/icons";
import { RiDislikeLine } from "react-icons/ri";
import { RiResetLeftFill } from "react-icons/ri";
import { MdOutlinePlaylistRemove } from "react-icons/md";
import { FaHeart } from "react-icons/fa6";
import { useAudioContext } from "@/src/context/audio.context";
import { useSearchContext } from "@/src/context/search.context";
import { TbHeadphonesFilled } from "react-icons/tb";
import { formatToK } from "@/src/utils/helperFunctions";
import {
    removeFromPublicPlaylist,
    removeFromPrivatePlaylist,
} from "@/src/services/playlist.service";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const LectureCard = ({
    lectureData,
    isSelectFileOpen,
    selectedFiles,
    setSelectedFiles,
    onLectureUpdated,
    setPlaylistData,
    playlistData,
    setAllLectures,
    setDisplayedLectures,
}: any) => {
    const pathName = usePathname();
    const searchParams = useSearchParams();
    const { playLecture, isPlaying, closePlayer } = useAudioContext();

    const router = useRouter();
    const [isLectureInfoModalOpen, setIsLectureInfoModalOpen] = useState(false);
    const [isAddToPlaylistModalOpen, setIsAddToPlaylistModalOpen] =
        useState(false);
    const [isShareModalOpen, setIsShareModalOpen] = useState(false);

    const [imageLoaded, setImageLoaded] = useState(false);
    const [isFavorite, setIsFavorite] = useState(
        lectureData?.isFavourite || false
    );
    const [isCompleted, setIsCompleted] = useState(
        lectureData?.isCompleted || false
    );

    const [favouriteLoader, setFavouriteLoader] = useState(false);
    const [completeLoader, setCompleteLoader] = useState(false);
    const [removeFromPlaylistLoader, setRemoveFromPlaylistLoader] =
        useState(false);

    const { deepSearch } = useSearchContext();

    // Add this useEffect to keep local state in sync with lectureData
    useEffect(() => {
        setIsFavorite(lectureData?.isFavourite || false);
        setIsCompleted(lectureData?.isCompleted || false);
    }, [lectureData]);

    // Listen for favorite changes from other components (like audio player)
    useFavoriteSync(
        lectureData?.id,
        useCallback(
            (lectureId: any, isFavoriteStatus: any) => {
                if (lectureId === lectureData?.id) {
                    setIsFavorite(isFavoriteStatus);
                    // Also update the lectureData object to keep it in sync
                    if (lectureData) {
                        lectureData.isFavourite = isFavoriteStatus;
                    }
                }
            },
            [lectureData?.id, lectureData]
        )
    );

    // Handle both data structures
    const getTitle = () => {
        if (Array.isArray(lectureData?.title)) {
            return lectureData.title.join(" ");
        } else if (typeof lectureData?.title === "string") {
            return lectureData.title;
        }
        return "";
    };

    const getCategory = () => {
        if (Array.isArray(lectureData?.category)) {
            return lectureData.category.join(", ");
        } else if (typeof lectureData?.category === "string") {
            return lectureData.category;
        }
        return "";
    };

    const getThumbnail = () => {
        return lectureData?.thumbnail || lectureData?.thumbnailUrl || "";
    };

    const getLength = () => {
        return lectureData?.length || 0;
    };

    const getDate = () => {
        return lectureData?.dateOfRecording || null;
    };

    const handleAddToFavorite = async (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the parent click handler

        try {
            setFavouriteLoader(true);

            // Process the database operations in the background
            const successfulIds = await addToFavourite(lectureData);

            if (successfulIds[0] === lectureData.id) {
                setIsFavorite(true);
                lectureData.isFavourite = true;
                onLectureUpdated([lectureData.id], "isFavourite", true);
                // Broadcast the favorite change to other components
                broadcastFavoriteChange(lectureData.id, true);
            } else {
                // If the operation fails, revert the UI changes
                setIsFavorite(false);
                // lectureData.favourite = false;
                lectureData.isFavourite = false;
                onLectureUpdated([lectureData.id], "isFavourite", false);
            }
            message.success("Lecture has been added to Favorite.");
        } catch (error) {
            // If an error occurs, revert the UI changes
            setIsFavorite(false);
            // lectureData.favourite = false;
            lectureData.isFavourite = false;
            onLectureUpdated([lectureData.id], "isFavourite", false);
        } finally {
            setFavouriteLoader(false);
        }
    };

    const handleRemoveFromFavorite = async (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the parent click handler

        try {
            setFavouriteLoader(true);

            // Process the database operations in the background
            const successfulIds = await removeFromFavourite(lectureData);

            if (successfulIds[0] === lectureData.id) {
                // Notify parent component if callback exists
                setIsFavorite(false);
                lectureData.isFavourite = false;
                onLectureUpdated(successfulIds, "isFavourite", false);
                // Broadcast the favorite change to other components
                broadcastFavoriteChange(lectureData.id, false);
            } else {
                // If the operation fails, revert the UI changes
                setIsFavorite(true);
                // lectureData.favourite = true;
                lectureData.isFavourite = true;
                onLectureUpdated([lectureData.id], "isFavourite", true);
            }
            message.success("Lecture has been removed from Favorite.");
        } catch (error) {
            // If an error occurs, revert the UI changes
            setIsFavorite(true);
            // lectureData.favourite = true;
            lectureData.isFavourite = true;
            onLectureUpdated([lectureData.id], "isFavourite", true);
        } finally {
            setFavouriteLoader(false);
        }
    };

    const handleMarkAsCompleted = async (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the parent click handler

        try {
            setCompleteLoader(true);

            // Process the database operations in the background
            const successfulIds = await markAsCompleted(lectureData);

            if (successfulIds[0] === lectureData.id) {
                console.log("done");
                setIsCompleted(true);
                lectureData.isCompleted = true;
                onLectureUpdated([lectureData.id], "isCompleted", true);
            } else {
                // If the operation fails, revert the UI changes
                setIsCompleted(false);
                lectureData.isCompleted = false;
                onLectureUpdated([lectureData.id], "isCompleted", false);
            }
            message.success("Lecture has been marked as completed.");
        } catch (error) {
            // If an error occurs, revert the UI changes
            setIsCompleted(false);
            lectureData.isCompleted = false;
            onLectureUpdated([lectureData.id], "isCompleted", false);
        } finally {
            setCompleteLoader(false);
        }
    };

    const handleRemoveFromCompleted = async (e: React.MouseEvent) => {
        e.stopPropagation(); // Prevent triggering the parent click handler

        try {
            setCompleteLoader(true);

            // Process the database operations in the background
            const successfulIds = await removeFromCompleted(lectureData);

            if (successfulIds[0] === lectureData.id) {
                setIsCompleted(false);
                lectureData.isCompleted = false;
                onLectureUpdated([lectureData.id], "isCompleted", false);
            } else {
                // If the operation fails, revert the UI changes
                setIsCompleted(true);
                lectureData.isCompleted = true;
                onLectureUpdated([lectureData.id], "isCompleted", true);
            }
            message.success("Lecture has been removed from Completed.");
        } catch (error) {
            // If an error occurs, revert the UI changes
            setIsCompleted(true);
            lectureData.isCompleted = true;
            onLectureUpdated([lectureData.id], "isCompleted", true);
        } finally {
            setCompleteLoader(false);
        }
    };

    const isPlaylistPage = useMemo(() => {
        return /^\/playlists\/[^/]+/.test(pathName);
    }, [pathName]);

    const handleRemoveFromPlaylist = async (e: React.MouseEvent) => {
        e.stopPropagation();
        try {
            setRemoveFromPlaylistLoader(true);
            if (playlistData.listType === "Public") {
                await removeFromPublicPlaylist(
                    playlistData.listID,
                    lectureData.id
                );
            } else {
                await removeFromPrivatePlaylist(
                    playlistData.listID,
                    lectureData.id
                );
            }
            // Update playlistData state and URL
            const updatedLectureIds = (playlistData.lectureIds || []).filter(
                (id: any) => id !== lectureData.id
            );
            const updatedLectureCount = (playlistData.lectureCount || 1) - 1;
            const newPlaylistData = {
                ...playlistData,
                lectureIds: updatedLectureIds,
                lectureCount: updatedLectureCount,
            };
            setPlaylistData(newPlaylistData);
            // Remove lecture from all lectures and displayed lectures
            setAllLectures &&
                setAllLectures((prev: any[]) =>
                    prev.filter((l) => l.id !== lectureData.id)
                );
            setDisplayedLectures &&
                setDisplayedLectures((prev: any[]) =>
                    prev.filter((l) => l.id !== lectureData.id)
                );
            const encoded = encodeURIComponent(JSON.stringify(newPlaylistData));
            router.replace(`?data=${encoded}`, { scroll: false });
            if (onLectureUpdated) {
                onLectureUpdated([lectureData.id], "removedFromPlaylist", true);
            }
            message.success("Lecture removed from playlist successfully");
            setRemoveFromPlaylistLoader(false);
        } catch (error) {
            console.error("Failed to remove lecture from playlist", error);
            setRemoveFromPlaylistLoader(false);
        }
    };

    const items = [
        {
            key: "1",
            label: (
                <div
                    className="h-[28px] flex gap-2 items-center py-1"
                    onClick={
                        isFavorite
                            ? handleRemoveFromFavorite
                            : handleAddToFavorite
                    }
                >
                    <div className="w-[20px]">
                        {favouriteLoader ? (
                            <Spin
                                indicator={
                                    <LoadingOutlined
                                        spin
                                        className="text-text-primary relative -top-[3px]"
                                        style={{ fontSize: 18 }}
                                    />
                                }
                            />
                        ) : (
                            <>
                                {isFavorite ? (
                                    <RiDislikeLine className="text-[18px] text-secondary" />
                                ) : (
                                    <Image
                                        src="/images/helperComponents/IconFavorite.svg"
                                        width={20}
                                        height={20}
                                        alt=""
                                        className={`w-[20px] h-[20px]`}
                                    />
                                )}
                            </>
                        )}
                    </div>
                    <h2
                        className={`text-[14px] leading-5 font-[500] text-text-primary`}
                    >
                        {isFavorite ? "Remove Favorite" : "Add to Favorites"}
                    </h2>
                </div>
            ),
        },
        {
            key: "2",
            label: (
                <div
                    className="h-[28px] flex gap-2 items-center py-1"
                    onClick={
                        isCompleted
                            ? handleRemoveFromCompleted
                            : handleMarkAsCompleted
                    }
                >
                    <div className="w-[20px]">
                        {completeLoader ? (
                            <Spin
                                indicator={
                                    <LoadingOutlined
                                        spin
                                        className="text-text-primary relative -top-[3px]"
                                        style={{ fontSize: 18 }}
                                    />
                                }
                            />
                        ) : (
                            <>
                                {isCompleted ? (
                                    <RiResetLeftFill className="text-[20px] text-secondary" />
                                ) : (
                                    <Image
                                        src="/images/helperComponents/IconCheck.svg"
                                        width={20}
                                        height={20}
                                        alt=""
                                        className={`w-[20px] h-[20px]`}
                                    />
                                )}
                            </>
                        )}
                    </div>
                    <h2
                        className={`w-[calc(100%-28px)] text-[14px] leading-5 font-[500] text-text-primary`}
                    >
                        {isCompleted ? "Reset Completed" : "Mark as Complete"}
                    </h2>
                </div>
            ),
        },
        {
            key: "3",
            label: (
                <div
                    className="flex gap-2 items-center py-1"
                    onClick={() => setIsAddToPlaylistModalOpen(true)}
                >
                    <Image
                        src="/images/helperComponents/IconPlaylistAdd.svg"
                        width={20}
                        height={20}
                        alt=""
                        className={`w-[20px] h-[20px]`}
                    />
                    <h2
                        className={`text-[14px] leading-5 font-[500] text-text-primary`}
                    >
                        Add to Playlist
                    </h2>
                </div>
            ),
        },
        {
            key: "4",
            label: (
                <div
                    className="flex gap-2.5 items-center py-1"
                    onClick={() => setIsLectureInfoModalOpen(true)}
                >
                    <Image
                        src="/images/ui/info.png"
                        width={18}
                        height={18}
                        alt=""
                        className={`w-[18px] h-[18px]`}
                    />
                    <h2
                        className={`text-[14px] leading-5 font-[500] text-text-primary`}
                    >
                        More info
                    </h2>
                </div>
            ),
        },
        ...(isPlaylistPage
            ? [
                  {
                      key: "5",
                      label: (
                          <div
                              className="flex gap-2 items-center py-1"
                              onClick={handleRemoveFromPlaylist}
                          >
                              <div className="w-[20px]">
                                  {removeFromPlaylistLoader ? (
                                      <Spin
                                          indicator={
                                              <LoadingOutlined
                                                  spin
                                                  className="text-text-primary relative -top-[3px]"
                                                  style={{ fontSize: 18 }}
                                              />
                                          }
                                      />
                                  ) : (
                                      <MdOutlinePlaylistRemove
                                          className={`w-[20px] h-[20px]`}
                                      />
                                  )}
                              </div>
                              <h2 className="text-[14px] leading-5 font-[500] text-text-primary">
                                  Remove From Playlist
                              </h2>
                          </div>
                      ),
                  },
              ]
            : []),
        // {
        //     key: "6",
        //     label: (
        //         <div
        //             className="flex gap-2.5 items-center py-1"
        //             onClick={() => setIsShareModalOpen(true)}
        //         >
        //             <Image
        //                 src="/images/ui/share.svg"
        //                 width={18}
        //                 height={18}
        //                 alt=""
        //                 className={`w-[18px] h-[18px]`}
        //             />
        //             <h2
        //                 className={`text-[14px] leading-5 font-[500] text-text-primary`}
        //             >
        //                 Share
        //             </h2>
        //         </div>
        //     ),
        // },
    ];

    const handleLecture = () => {
        if (isSelectFileOpen) {
            if (
                !selectedFiles.some(
                    (lecture: any) => lecture.id === lectureData.id
                )
            ) {
                setSelectedFiles([...selectedFiles, lectureData]);
            } else {
                setSelectedFiles(
                    selectedFiles.filter(
                        (lecture: any) => lecture.id !== lectureData.id
                    )
                );
            }
        } else {
            console.log("lectureData", lectureData.resources.videos);
            // Check if lecture has video content
            const audioOn = localStorage.getItem("audioOn");
            // const hasVideo = lectureData.resources?.videos?.length > 0;

            if (
                audioOn === "false" &&
                lectureData.resources.videos.length > 0
            ) {
                // Navigate to video player
                router.push(`/video/${lectureData.id}`);
                if (isPlaying) {
                    closePlayer();
                }
            } else {
                // Play audio as usual
                playLecture(lectureData);
            }
        }
    };

    const calculatePercentage = () => {
        if (lectureData?.totallength && lectureData?.lastPlayedPoint) {
            return (
                Math.floor(
                    (lectureData.lastPlayedPoint / lectureData.totallength) *
                        100
                ) || 0
            );
        }
        return 0;
    };

    return (
        <div className="cursor-pointer" onClick={handleLecture}>
            <div className={`w-full transition-all duration-500 relative`}>
                <div className={`relative rounded-[6px] overflow-hidden`}>
                    {/* Show skeleton until image loads */}
                    {/* <div
                        className="absolute -top-1 inset-0 z-10 transition-opacity duration-300"
                        style={{ opacity: imageLoaded ? 0 : 1 }}
                    >
                        <Skeleton height={155} width="100%" borderRadius={6} />
                    </div> */}
                    <img
                        src={getThumbnail()}
                        width={400}
                        height={300}
                        alt=""
                        className={`w-full h-auto rounded-[6px] object-cover aspect-[16/9] ${
                            isSelectFileOpen &&
                            selectedFiles.some(
                                (lecture: any) => lecture.id === lectureData.id
                            ) &&
                            "opacity-50"
                        }`}
                        loading="lazy"
                        onLoad={() => setImageLoaded(true)}
                        onError={(e) => {
                            setImageLoaded(true);
                            e.currentTarget.src =
                                appConfig.defaultLectureThumbnail;
                        }}
                    />
                    {isSelectFileOpen &&
                        selectedFiles.some(
                            (lecture: any) => lecture.id === lectureData.id
                        ) && (
                            <div className="absolute top-3 left-3 z-10">
                                <IconSelected
                                    width={22}
                                    height={22}
                                    color={"var(--primary-color)"}
                                />
                            </div>
                        )}
                    <div
                        className={`flex flex-col gap-1 absolute top-2 right-2 ${
                            isSelectFileOpen &&
                            selectedFiles.some(
                                (lecture: any) => lecture.id === lectureData.id
                            ) &&
                            "opacity-50"
                        }`}
                    >
                        {/* {(isFavorite || lectureData?.isFavourite) && (
                            <div className="w-6 h-6 rounded-full flex justify-center items-center pt-[1px] bg-primary border-2 border-white">
                                <FaRegHeart
                                    style={{ color: "white", fontSize: 14 }}
                                />
                            </div>
                        )}
                        {(isCompleted || lectureData?.isCompleted) && (
                            <div className="w-6 h-6 rounded-full flex justify-center items-center pt-[1px] text-textLight bg-primary border-2 border-white">
                                <FaCheck
                                    style={{ color: "white", fontSize: 14 }}
                                />
                            </div>
                        )} */}
                        {(isFavorite || lectureData?.isFavourite) && (
                            <div className="w-6 h-6 rounded-full flex justify-center items-center pt-[1px] ">
                                <FaHeart
                                    style={{ color: "#F83F3F", fontSize: 20 }}
                                />
                            </div>
                        )}
                    </div>
                    {(calculatePercentage() > 0 ||
                        lectureData?.isCompleted) && (
                        <div className="absolute bottom-0 w-full h-1 bg-gray-300">
                            <div
                                className="h-full bg-primary"
                                style={{
                                    width: lectureData?.isCompleted
                                        ? "100%"
                                        : `${
                                              calculatePercentage() <= 100
                                                  ? calculatePercentage()
                                                  : 100
                                          }%`,
                                }}
                            ></div>
                        </div>
                    )}
                </div>
                <div className="flex w-full justify-between items-center px-2 absolute bottom-2">
                    <div className="flex gap-1 items-center">
                        {/* {calculatePercentage() < 97 && ( */}
                        <p className="px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]">
                            {lectureData?.isCompleted
                                ? "100%"
                                : `${Math.min(
                                      calculatePercentage(),
                                      100
                                  )}%`}{" "}
                            {deepSearch && lectureData?.count
                                ? ` | (${lectureData?.count}) Mentions`
                                : ``}
                        </p>
                        {/* )} */}
                    </div>
                    <div className="flex gap-1 items-center">
                        <div
                            className="flex gap-1 px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]"
                            title="Total Listens"
                        >
                            <TbHeadphonesFilled className="text-[14px]" />
                            {lectureData?.listens
                                ? formatToK(lectureData?.listens * 9)
                                : 0}
                        </div>
                        <p className="px-1 py-[2px] text-[12px] leading-4 font-[400] rounded-md text-textLight bg-[#343A40F5]">
                            {formatSecondsToHHMMSS(getLength())}
                        </p>
                    </div>
                </div>
            </div>
            <div className="flex gap-2">
                <div className="w-[calc(100%-26px)] flex flex-col gap-1 py-2">
                    <h1
                        className={`text-[15px] leading-6 font-[600] text-text ${poppins.className}`}
                    >
                        {getTitle()}
                    </h1>
                    <div className="flex space-x-1 justify-start items-start">
                        {(isCompleted || lectureData?.isCompleted) && (
                            <span className="w-6 h-6 rounded-full flex justify-center items-center pt-[1px] text-textLight">
                                <FaCheck
                                    style={{ color: "black", fontSize: 16 }}
                                />
                            </span>
                        )}
                        <p
                            className={`text-[13px] leading-5 font-[400] text-text-primary ${poppins.className}`}
                        >
                            {formatDateObjToDDMMYYYY(getDate())}{" "}
                            {getCategory() ? `• ${getCategory()}` : ""}
                        </p>
                    </div>
                </div>
                <div
                    className="pt-4 cursor-pointer"
                    onClick={(e) => e.stopPropagation()}
                >
                    <Dropdown
                        menu={{ items }}
                        trigger={["click"]}
                        placement="bottomRight"
                        overlayClassName="w-[205px]"
                    >
                        <Image
                            src="/images/ui/menuDot.svg"
                            width={18}
                            height={18}
                            alt=""
                            className={`w-[18px] h-[18px] relative left-1`}
                        />
                    </Dropdown>
                </div>
            </div>
            <div onClick={(e) => e.stopPropagation()}>
                <LectureInfoModal
                    isModalOpen={isLectureInfoModalOpen}
                    setIsModalOpen={setIsLectureInfoModalOpen}
                    lectureData={lectureData}
                />
                <AddToPlaylistModal
                    isModalOpen={isAddToPlaylistModalOpen}
                    setIsModalOpen={setIsAddToPlaylistModalOpen}
                    selectedFiles={lectureData}
                />
                <ShareModal
                    isModalOpen={isShareModalOpen}
                    setIsModalOpen={setIsShareModalOpen}
                    title={getTitle()}
                    url={window.location.href}
                />
            </div>
        </div>
    );
};

export default LectureCard;
