import Image from "next/image";
import React, { useState } from "react";
import { addToFavourite } from "@/src/services/favouriteLecture.service";
import { broadcastFavoriteChange } from "@/src/hooks/useFavoriteSync";
import { message, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { MdFavoriteBorder } from "react-icons/md";

interface AddToFavoritesProps {
  selectedFiles: any[];
  onFavoritesUpdated?: (updatedIds: (string | number)[], field: string, value: boolean) => void;
}

const AddToFavorites: React.FC<AddToFavoritesProps> = ({
  selectedFiles,
  onFavoritesUpdated
}: any) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAddToFavorites = async () => {
    if (selectedFiles.length === 0) return;

    try {
      setIsProcessing(true);

      // Call the addToFavourite function with batch size
      const successfulIds = await addToFavourite(selectedFiles);

      if (successfulIds.length > 0) {
        // message.success(`Added ${successfulIds.length} lecture(s) to favorites`);

        // Notify parent component about the updated favorites
        onFavoritesUpdated(successfulIds, "isFavourite", true);

        // Broadcast favorite changes to all listening components
        successfulIds.forEach(lectureId => {
          broadcastFavoriteChange(lectureId, true);
        });
      } else {
        message.warning('No lectures were added to favorites');
      }
      message.success("Lectures has been added to Favorite.");
    } catch (error) {
      console.error('Error adding to favorites:', error);
      message.error('Failed to add to favorites');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div
      className={`h-[36px] flex gap-2 items-center pt-[2px] px-1 md:px-3 text-[13px] text-left rounded-[12px] transition-all duration-300 ${
        selectedFiles.length > 0 && !isProcessing
          ? "opacity-100 cursor-pointer hover:bg-primary-hover"
          : "opacity-70 cursor-default"
      }`}
      onClick={selectedFiles.length > 0 && !isProcessing ? handleAddToFavorites : undefined}
    >
      {isProcessing ? (
        <Spin
          indicator={<LoadingOutlined spin className="text-secondary" />}
          size="small"
        />
      ) :
      // <Image
      //   src="/images/helperComponents/IconFavorite.svg"
      //   width={20}
      //   height={20}
      //   alt=""
      //   className={`w-[20px] h-[20px]`}
      // />
      <MdFavoriteBorder className="text-[18px] text-text-primary" />
      }
      <h2 className={`text-[14px] leading-5 font-[500] text-text-primary min-[924px]:block hidden`}>
        {"Add to Favorites"}
      </h2>
    </div>
  );
};

export default AddToFavorites;
