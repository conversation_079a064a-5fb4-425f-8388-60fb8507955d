/**
 * Default configuration for the application
 * This serves as a fallback when specific app configuration is not available
 */

export const defaultConfig = {
  // App metadata
  appName: 'Default App',
  appTitle: 'Default App Title',
  appDescription: 'Default App Description',

  // Theme colors
  theme: {
    primary: '#fd7e14',       // Primary color (orange)
    primaryLight: '#fd7d1447', // Light version of primary color
    primaryHover: '#e67212',  // Darker version for hover states
    secondary: '#343A40',     // Secondary color
    accent: '#E0E0E0',        // Accent color
    border: '#e0e0e0',        // Border color
    background: '#ffffff',    // Background color
    text: '#000000',          // Text color
    textLight: '#ffffff',          // Text color
    textSecondary: '#1b1f3b66', // Secondary text color
  },

  // UI specific configurations
  ui: {
    borderRadius: {
      small: '10px',
      medium: '12px',
      large: '20px',
    },
    buttonHeight: '44px',
  },

  // Feature flags
  features: {
    enableDeepSearch: true,
    enableAppleLogin: true,
  }
};

export default defaultConfig;
