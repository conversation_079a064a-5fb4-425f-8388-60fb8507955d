"use client";

import React, { useState, useRef, useEffect } from "react";
import { useVideoContext } from "@/src/context/video.context";
import { Slider } from "antd";
import {
    FaPlay,
    FaPause,
    FaVolumeUp,
    FaVolumeDown,
    FaVolumeMute,
    FaExpand,
    FaCompress,
} from "react-icons/fa";
import { RiReplay10Fill, RiForward10Fill } from "react-icons/ri";
import { MdSettings } from "react-icons/md";

const VideoPlayerControls = () => {
    const {
        isPlaying,
        togglePlay,
        currentTime,
        duration,
        seekTo,
        volume,
        setVolume,
        playbackRate,
        setPlaybackRate,
        showControls,
        isFullscreen,
        toggleFullscreen,
        showControlsTemporarily,
    } = useVideoContext();

    const [showVolumeSlider, setShowVolumeSlider] = useState(false);
    const [showSpeedOptions, setShowSpeedOptions] = useState(false);
    const [speedOptions] = useState([0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]);
    const volumeControlRef = useRef<HTMLDivElement>(null);
    const speedControlRef = useRef<HTMLDivElement>(null);

    // Format time to MM:SS or HH:MM:SS
    const formatTime = (seconds: number) => {
        if (isNaN(seconds)) return "0:00";

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    // Handle progress bar change
    const handleProgressChange = (value: number) => {
        seekTo(value);
    };

    // Handle volume change
    const handleVolumeChange = (value: number) => {
        setVolume(value);
    };

    // Handle volume icon click
    const handleVolumeClick = () => {
        if (volume > 0) {
            setVolume(0);
        } else {
            setVolume(1);
        }
    };

    // Handle rewind 10 seconds
    const handleRewind10 = () => {
        const newTime = Math.max(0, currentTime - 10);
        seekTo(newTime);
    };

    // Handle forward 10 seconds
    const handleForward10 = () => {
        const newTime = Math.min(duration, currentTime + 10);
        seekTo(newTime);
    };

    // Handle speed change
    const handleSpeedChange = (speed: number) => {
        setPlaybackRate(speed);
        setShowSpeedOptions(false);
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (volumeControlRef.current && !volumeControlRef.current.contains(event.target as Node)) {
                setShowVolumeSlider(false);
            }
            if (speedControlRef.current && !speedControlRef.current.contains(event.target as Node)) {
                setShowSpeedOptions(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    // Get volume icon
    const getVolumeIcon = () => {
        if (volume === 0) return <FaVolumeMute />;
        if (volume < 0.5) return <FaVolumeDown />;
        return <FaVolumeUp />;
    };

    if (!showControls) return null;

    return (
        <div
            className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent flex flex-col justify-end p-6"
            onMouseMove={showControlsTemporarily}
        >
            {/* Progress Bar */}
            <div className="mb-6">
                <Slider
                    min={0}
                    max={duration || 100}
                    value={currentTime}
                    onChange={handleProgressChange}
                    tooltip={{ formatter: (value) => formatTime(value || 0) }}
                    className="video-progress-slider"
                />
            </div>

            {/* Controls Row */}
            <div className="flex items-center justify-between text-white">
                {/* Left Controls */}
                <div className="flex items-center gap-3">
                    {/* Play/Pause */}
                    <button
                        type="button"
                        onClick={togglePlay}
                        aria-label={isPlaying ? "Pause video" : "Play video"}
                        className="p-3 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
                    >
                        {isPlaying ? <FaPause size={22} /> : <FaPlay size={22} />}
                    </button>

                    {/* Rewind 10s */}
                    <button
                        type="button"
                        onClick={handleRewind10}
                        aria-label="Rewind 10 seconds"
                        className="p-2 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
                    >
                        <RiReplay10Fill size={26} />
                    </button>

                    {/* Forward 10s */}
                    <button
                        type="button"
                        onClick={handleForward10}
                        aria-label="Forward 10 seconds"
                        className="p-2 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
                    >
                        <RiForward10Fill size={26} />
                    </button>

                    {/* Volume Control */}
                    <div className="relative" ref={volumeControlRef}>
                        <button
                            type="button"
                            onClick={handleVolumeClick}
                            onMouseEnter={() => setShowVolumeSlider(true)}
                            aria-label={volume === 0 ? "Unmute" : "Mute"}
                            className="p-2 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
                        >
                            {getVolumeIcon()}
                        </button>

                        {/* Volume Slider */}
                        {showVolumeSlider && (
                            <div
                                className="absolute bottom-14 left-0 bg-black/90 p-3 rounded-lg shadow-lg"
                                onMouseLeave={() => setShowVolumeSlider(false)}
                            >
                                <div className="h-20 flex items-center">
                                    <Slider
                                        vertical
                                        min={0}
                                        max={1}
                                        step={0.1}
                                        value={volume}
                                        onChange={handleVolumeChange}
                                        className="h-16"
                                    />
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Time Display */}
                    <div className="text-sm font-medium bg-black/30 px-3 py-1 rounded-full">
                        {formatTime(currentTime)} / {formatTime(duration)}
                    </div>
                </div>

                {/* Right Controls */}
                <div className="flex items-center gap-3">
                    {/* Speed Control */}
                    <div className="relative" ref={speedControlRef}>
                        <button
                            type="button"
                            onClick={() => setShowSpeedOptions(!showSpeedOptions)}
                            aria-label="Playback speed settings"
                            className="flex items-center gap-2 px-3 py-2 bg-black/30 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-105"
                        >
                            <MdSettings size={18} />
                            <span className="text-sm font-medium">{playbackRate}x</span>
                        </button>

                        {/* Speed Options */}
                        {showSpeedOptions && (
                            <div className="absolute bottom-14 right-0 bg-black/95 rounded-lg overflow-hidden shadow-lg min-w-[80px]">
                                {speedOptions.map((speed) => (
                                    <button
                                        key={speed}
                                        type="button"
                                        onClick={() => handleSpeedChange(speed)}
                                        aria-label={`Set playback speed to ${speed}x`}
                                        className={`block w-full px-4 py-3 text-left hover:bg-white/20 transition-colors text-sm ${
                                            playbackRate === speed ? "bg-white/30 text-blue-400" : ""
                                        }`}
                                    >
                                        {speed}x
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Fullscreen */}
                    <button
                        type="button"
                        onClick={toggleFullscreen}
                        aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                        className="p-2 hover:bg-white/20 rounded-full transition-all duration-200 hover:scale-110"
                    >
                        {isFullscreen ? <FaCompress size={20} /> : <FaExpand size={20} />}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default VideoPlayerControls;
