"use client";
import React, { createContext, useContext, useState } from "react";

const FilterContext = createContext();

export const FilterContextProvider = ({ children }) => {

    const [playbackMode, setPlaybackMode] = useState(1)
    const [sortBy, setSortBy] = useState(1)
    const [selectedFilterValues, setSelectedFilterValues] = useState({
        LANGUAGE: [],
        COUNTRY: [],
        PLACE: [],
        YEAR: [],
        MONTH: [],
        CATEGORY: [],
        TRANSLATION: [],
    });
    const [isFiltering, setIsFiltering] = useState(false)
    const [historyPeriod, setHistoryPeriod] = useState(1)
    const [popularPeriod, setPopularPeriod] = useState("allTime")
    const [playlistShow, setPlaylistShow] = useState(1)

    const resetFilters = () => {
        setPlaybackMode(1);
        setSortBy(1);
        setSelectedFilterValues({
            LANGUAGE: [],
            COUNTRY: [],
            PLACE: [],
            YEAR: [],
            MONTH: [],
            CATEGORY: [],
            TRANSLATION: [],
        });
        setIsFiltering(false);
        setHistoryPeriod(1);
        setPopularPeriod("allTime");
        setPlaylistShow(1);
    };

    return (
        <FilterContext.Provider
            value={{
                playbackMode, setPlaybackMode, sortBy, setSortBy,
                selectedFilterValues,
                setSelectedFilterValues,
                isFiltering, setIsFiltering,
                historyPeriod, setHistoryPeriod,
                popularPeriod, setPopularPeriod,
                resetFilters,
                playlistShow, setPlaylistShow,
            }}
        >
            {children}
        </FilterContext.Provider>
    );
};

export const useFilterContext = () => {
    return useContext(FilterContext);
};
