/**
 * Configuration loader
 * Loads the appropriate configuration based on the APP environment variable
 */

import { defaultConfig } from './default';
import { bvksConfig } from './bvks';

// Add more app configurations as needed
const appConfigs: { [key: string]: any } = {
  'BVKS': bvksConfig,
  'DEFAULT': defaultConfig,
  // Add more app configurations here
  // 'OTHER_APP': otherAppConfig,
};

/**
 * Get the configuration for the current app
 * Falls back to default config if the app is not found
 */
export const getAppConfig = () => {
  // Get the APP from Next.js environment variable
  // Next.js makes environment variables available through process.env on both client and server
  // This ensures consistent rendering between server and client
  let appName = process.env.APP || 'BVKS'; // Default to BVKS if not set

  // Normalize app name to uppercase
  appName = appName.toUpperCase();

  // Return the config for the app, or default if not found
  return appConfigs[appName] || bvksConfig; // Default to BVKS config
};

// Export a singleton instance of the config
const appConfig = getAppConfig();
export default appConfig;
