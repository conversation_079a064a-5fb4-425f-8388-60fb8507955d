import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Poppins } from "next/font/google";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const StatisticsSkeleton = () => {
    return (
        <div className={`w-full h-full bg-white ${poppins.className}`}>
            {/* Header */}
            <div className="w-full h-[60px] md:h-[84px] flex items-center md:items-end justify-between px-4 md:py-3 gap-1 min-[460px]:gap-4 lg:gap-2 border-b relative">
                <h1 className="max-[392px]:text-[24px] max-[460px]:text-[24px] text-[28px] font-[600] leading-8">
                    Statistics
                </h1>
            </div>

            {/* Progress Section */}
            <div className="h-[calc(100%-100px)] overflow-y-auto scrollbar">
                <div className="h-full flex flex-col xl:flex-row gap-4 p-4 md:p-6 xl:mb-[100px] mb-0">
                    {/* Progress Info */}
                    <div className="flex-1 space-y-3 h-full">
                        {/* Five horizontal skeletons for Progress Info */}
                        <div className="h-[90px] rounded-2xl">
                            <Skeleton width="100%" height="100%" borderRadius={10} />
                        </div>
                        <div
                            className="h-[calc(100%-102px)] rounded-2xl pt-4 xl:block hidden" 
                            // style={{
                            //     boxShadow:
                            //         "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
                            // }}
                        >
                            <Skeleton width="100%" height="100%" borderRadius={10} />
                        </div>
                    </div>

                    {/* Progress Chart */}
                    <div
                        className="w-full h-full xl:w-[450px] rounded-2xl px-4"
                        // style={{
                        //     boxShadow: "0px 2px 8px 0px rgba(0, 0, 0, 0.16)",
                        // }}
                    >
                        {/* One skeleton for Progress Chart */}
                        <Skeleton width="100%" height="100%" borderRadius={10} />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StatisticsSkeleton;
