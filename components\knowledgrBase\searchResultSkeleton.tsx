"use client";
import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { Poppins } from "next/font/google";

const poppins = Poppins({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const SearchResultSkeleton = () => {
    return (
        <div
            className={`p-3 sm:p-4 md:p-5 bg-primary bg-opacity-10 rounded-lg border border-primary  border-b-primary ${poppins.className}`}
        >
            {/* Title skeleton */}
            <Skeleton height={24} width="70%" className="mb-2" />

            {/* Source, Location and Date skeleton */}
            <div className="flex flex-wrap items-center gap-2 mb-3">
                <Skeleton width={100} height={16} />
                <Skeleton width={120} height={16} />
                <Skeleton width={80} height={16} />
            </div>

            {/* Excerpt skeleton */}
            <Skeleton count={1} height={18} className="mb-1" />
        </div>
    );
};

export default SearchResultSkeleton;
