"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Open_Sans, Inter, Roboto } from "next/font/google";
import { Button, Input, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import Link from "next/link";
import { forgotPasswordWithFirebase } from "@/src/api/auth.api";
import { useRouter } from "next/navigation";

const open_Sans = Open_Sans({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const inter = Inter({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});
const roboto = Roboto({
    weight: ["300", "400", "500", "700"],
    subsets: ["latin"],
});

const ForgotPwd = () => {
    const router = useRouter();
    const [email, setEmail] = useState("");
    const [submitted, setSubmitted] = useState(false);
    const [loader, setLoader] = useState(false);

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleResetPassword = async () => {
        try {
            setSubmitted(true); // Mark form as submitted

            if (validateEmail(email)) {
                setLoader(true);
                await forgotPasswordWithFirebase(email);
                message.success(
                    "We've sent you a password reset link. Check your email to continue."
                );
                setEmail("")
            }
        } catch (error: any) {
            if (error?.message === "Firebase: Error (auth/user-not-found).") {
                message.error(
                    "Email not recognized. Please enter a registered email address."
                );
            } else {
                message.error(
                    "Oops! Something went wrong. Please try again in a moment."
                );
            }
        } finally {
            setLoader(false);
            setSubmitted(false)
        }
    };

    return (
        <div
            className={`w-full h-full flex justify-center items-center ${roboto.className}`}
        >
            <div
                className="w-[90%] sm:w-[424px] rounded-[12px] my-6 p-6 relative"
                style={{
                    boxShadow: "0 4px 24px #0000001f",
                }}
            >
                <Link
                    href={"/login"}
                    className="absolute top-6 left-6 text-sm flex gap-2 hover:opacity-60 transition-all"
                >
                    <ArrowLeftOutlined className="text-[18px]" />
                    Login
                </Link>
                <div className="w-full flex justify-center">
                    <Image
                        src="/images/auth/ava.jpg"
                        width={88}
                        height={88}
                        alt=""
                        className="w-[88px] h-[88px] mt-8 mb-5"
                    />
                </div>

                <h3 className="text-[28px] font-[600] leading-[100%] text-center mb-10">
                    Bhakti Vikasa Swami
                </h3>

                <div className="flex flex-col gap-5 mb-6">
                    {/* Email Input */}
                    <div className="relative">
                        <Input
                            size="large"
                            placeholder="E-mail"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className="h-[54px] !rounded-[10px] !text-sm"
                            status={submitted && !validateEmail(email) ? "error" : undefined}
                            onPressEnter={handleResetPassword}
                            disabled={loader}
                        />
                        {submitted && !validateEmail(email) && (
                            <p className="absolute -bottom-4.5 left-2 text-red-500 text-[10px]">
                                Invalid Email
                            </p>
                        )}
                    </div>
                </div>

                {/* Reset Password Button */}
                <Button
                    className={`w-full !text-white !font-[500] !border-none ${email.trim() === "" ? "!bg-primary-light" : "!bg-primary"
                        }`}
                    style={{
                        height: "54px",
                        fontSize: "17px",
                        borderRadius: "10px",
                    }}
                    onClick={handleResetPassword}
                    disabled={email.trim() === ""}
                    loading={loader}
                >
                    Reset Password
                </Button>
            </div>
        </div>
    );
};

export default ForgotPwd;
